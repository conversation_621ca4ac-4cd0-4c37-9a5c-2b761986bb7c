# Common base for Node.js applications
FROM node:18-slim AS node-base
RUN npm install -g pnpm@7
WORKDIR /app
COPY ./package*.json ./pnpm-lock.yaml ./pnpm-workspace.yaml ./
# COPY ./landing-page/package*.json ./landing-page/
# COPY ./vocab-ai/package*.json ./vocab-ai/
COPY ./pickvocab-client/package*.json ./pickvocab-client/
COPY ./packages/dictionary/package*.json ./packages/dictionary/
RUN pnpm install
# COPY ./landing-page ./landing-page/
# COPY ./vocab-ai ./vocab-ai/
COPY ./pickvocab-client ./pickvocab-client/
COPY ./packages/dictionary ./packages/dictionary/
RUN pnpm build

# nginx
FROM nginx:alpine as reverse_proxy
# COPY --from=node-base /app/landing-page/dist /data/www/landing-page
# COPY --from=node-base /app/vocab-ai/dist /data/www/client
COPY ./reverse-proxy/proxy.conf /etc/nginx/conf.d/proxy.conf
COPY ./reverse-proxy/nginx.conf /etc/nginx/nginx.conf

# pickvocab-client
FROM node:18-slim as pickvocab-client
COPY --from=node-base /app/pickvocab-client/.output /pickvocab-client
WORKDIR /pickvocab-client
ENTRYPOINT ["node", "./server/index.mjs" ]
