# client
server {
  listen 80;
  server_name pickvocab.com www.pickvocab.com;

  return 301 https://pickvocab.com$request_uri;
}

server {
  listen 443 ssl;
  server_name www.pickvocab.com;
  ssl_certificate /root/.acme.sh/pickvocab.com_ecc/fullchain.cer;
  ssl_certificate_key /root/.acme.sh/pickvocab.com_ecc/pickvocab.com.key;

  return 301 https://pickvocab.com$request_uri;
}

server {
  listen 443 ssl;
  server_name pickvocab.com;
  ssl_certificate /root/.acme.sh/pickvocab.com_ecc/fullchain.cer;
  ssl_certificate_key /root/.acme.sh/pickvocab.com_ecc/pickvocab.com.key;

  # root /data/www/landing-page;
  # index index.html;

  gzip on;
  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_min_length 256;
  gzip_types text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;

  location ~* ^/(wp-admin|wp-login\.php|xmlrpc\.php|wp-content|wp-includes|wp-json|wp-cron\.php) {
    return 404;
  }

  location / {
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_pass http://client:3000/;
  }
}

# old client
server {
  listen 80;

  # root /var/www/html;

  # Add index.php to the list if you are using PHP
  # index index.html index.htm index.nginx-debian.html;

  server_name app.pickvocab.com;

  return 301 https://pickvocab.com/app$request_uri;
}

server {
  listen 443 ssl;
  server_name app.pickvocab.com;
  ssl_certificate /root/.acme.sh/pickvocab.com_ecc/fullchain.cer;
  ssl_certificate_key /root/.acme.sh/pickvocab.com_ecc/pickvocab.com.key;

  return 301 https://pickvocab.com/app$request_uri;
}

# backend
server {
  listen 80;

  server_name api.pickvocab.com;

  return 301 https://api.pickvocab.com$request_uri;
}

server {
  listen 443 ssl;
  server_name api.pickvocab.com;
  ssl_certificate /root/.acme.sh/pickvocab.com_ecc/fullchain.cer;
  ssl_certificate_key /root/.acme.sh/pickvocab.com_ecc/pickvocab.com.key;

  location /static/ {
    alias /var/www/api.pickvocab.com/static/;
  }

  location ~* ^/(wp-admin|wp-login\.php|xmlrpc\.php|wp-content|wp-includes|wp-json|wp-cron\.php) {
    return 404;
  }

  location / {
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_pass http://backend:8000/;
  }
}

# Flower monitoring
server {
  listen 80;
  server_name flower.pickvocab.com;
  return 301 https://flower.pickvocab.com$request_uri;
}

server {
  listen 443 ssl;
  server_name flower.pickvocab.com;
  ssl_certificate /root/.acme.sh/pickvocab.com_ecc/fullchain.cer;
  ssl_certificate_key /root/.acme.sh/pickvocab.com_ecc/pickvocab.com.key;

  location / {
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # WebSocket support
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    
    proxy_pass http://flower:5555/;
  }
}
