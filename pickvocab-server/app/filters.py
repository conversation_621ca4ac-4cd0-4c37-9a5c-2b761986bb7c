# import ipdb
from django_filters import rest_framework as filters
from app.models import GenericCard

class WordComparisonFilterBackend(filters.DjangoFilterBackend):
    def get_filterset_kwargs(self, request, queryset, view):
        tmp_dict = request.query_params.copy()
        words = tmp_dict["words"].split(",")
        words.sort()
        tmp_dict["words"] = ",".join(words)

        return {
            "data": tmp_dict,
            "queryset": queryset,
            "request": request,
        }


class GenericCardFilterSet(filters.FilterSet):
    """
    FilterSet for GenericCard model with support for filtering by multiple IDs.
    """
    ids = filters.CharFilter(method='filter_ids')
    
    class Meta:
        model = GenericCard
        fields = ['id', 'owner', 'decks__is_demo', 'ids']
    
    def filter_ids(self, queryset, name, value):
        """
        Filter queryset for cards with IDs in the provided comma-separated list.
        
        Example usage: ?ids=1,2,3,4
        """
        if not value:
            return queryset
            
        id_list = [int(id_str) for id_str in value.split(',') if id_str.strip().isdigit()]
        if not id_list:
            return queryset
            
        return queryset.filter(id__in=id_list)