from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app import views

# Create a router and register our ViewSets with it.
router = DefaultRouter()
router.register(r"words", views.WordViewSet, basename="word")
router.register(r"cards", views.CardViewSet, basename="card")
router.register(r"decks", views.DeckViewSet, basename="deck")
router.register(r"reviews", views.ReviewViewSet, basename="review")
router.register(r"llm_models", views.LLMModelViewSet, basename="llm_model")
router.register(r"word_comparisons", views.WordComparisonViewSet, basename="word_comparison")
router.register(r"word_in_context", views.WordInContextViewSet, basename="word_in_context")
router.register(r"generic_cards", views.GenericCardViewSet, basename="generic_card")
router.register(r"decks2", views.Deck2ViewSet, basename="deck2")
router.register(r"reviews2", views.Review2ViewSet, basename="review2")
router.register(r"feature_flags", views.FeatureFlagViewSet, basename="feature_flag")
router.register(r"write/history", views.WritingRevisionHistoryViewSet, basename="writing_revision_history")
router.register(r"custom_tones", views.CustomToneViewSet, basename="custom_tone")

# The API URLs are now determined automatically by the router.
urlpatterns = [
    path("", include(router.urls)),
]

# pprint(router.urls)
