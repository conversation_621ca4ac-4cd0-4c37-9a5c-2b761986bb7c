from django.db.models import Prefetch
from rest_framework import serializers
from dj_rest_auth.models import TokenModel
from django.contrib.auth import get_user_model
from allauth.socialaccount.models import SocialAccount
from django.contrib.contenttypes.models import ContentType
from django.apps import apps
from django.contrib.contenttypes.prefetch import GenericPrefetch
from .models import (
    ContextCard,
    Deck,
    Deck2,
    # Deck2,
    Deck<PERSON>ard,
    DeckCard2,
    # DeckCard2,
    DefinitionCard,
    FeatureFlag,
    GenericCard,
    LLMModel,
    Review,
    Review2,
    # Review2,
    ReviewCard,
    ReviewGenericCard,
    # ReviewGenericCard,
    Word,
    Card,
    WordComparison,
    WordInContext,
    CustomTone, # Added CustomTone model
    WritingRevisionHistory,
    WritingRevisionHistoryCards,
)
import environ
import os

env = environ.Env()
environ.Env.read_env(os.path.join(os.path.dirname(__file__), "../pickvocab/.env"))


class WordSerializer(serializers.ModelSerializer):
    slug = serializers.SlugField(read_only=True)

    class Meta:
        model = Word
        fields = [
            "id",
            "word",
            "definitions",
            "creator",
            "llm_model",
            "is_verified",
            "slug",
            "created_at",
            "updated_at",
        ]


class CardSerializer(serializers.ModelSerializer):
    word_ref = serializers.PrimaryKeyRelatedField(
        queryset=Word.objects.all(), required=False
    )
    word_ref_detail = serializers.SerializerMethodField(read_only=True)
    # word_ref = serializers.SerializerMethodField() # read-only
    owner = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = Card
        fields = [
            "id",
            "word",
            "word_ref",
            "word_ref_detail",
            "word_def_idx",
            "definition",
            "owner",
            "progress_score",
            "created_at",
            "updated_at",
        ]

    def get_word_ref_detail(self, obj):
        if obj.word_ref:
            return WordSerializer(obj.word_ref).data
        return None


class DeckCardSerializer(serializers.ModelSerializer):
    card = CardSerializer(read_only=True)
    deck = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = DeckCard
        fields = [
            "id",
            "deck",
            "card",
            "created_at",
            "updated_at",
        ]


class DeckWithCardSerializer(serializers.ModelSerializer):
    owner = serializers.PrimaryKeyRelatedField(read_only=True)
    # cards = DeckCardField(queryset=Card.objects.all(), many=True, required=False)
    card_set = serializers.SerializerMethodField()  # get_cards

    class Meta:
        model = Deck
        fields = [
            "id",
            "name",
            "description",
            "owner",
            "is_demo",
            "card_set",
            "created_at",
            "updated_at",
        ]

    def get_card_set(self, obj):
        pagination_class = self.context["pagination_class"]
        pagination = pagination_class()
        queryset = obj.deckcard_set.prefetch_related(
            Prefetch("card", queryset=Card.objects.select_related("word_ref"))
        ).order_by("-created_at")
        page = pagination.paginate_queryset(queryset, self.context["request"])

        serializer = DeckCardSerializer(page, many=True)
        return {
            "count": pagination.page.paginator.count,
            "previous": pagination.get_previous_link(),
            "next": pagination.get_next_link(),
            "results": serializer.data,
        }


class DeckSerializer(serializers.ModelSerializer):
    owner = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = Deck
        fields = [
            "id",
            "name",
            "description",
            "owner",
            "is_demo",
            "created_at",
            "updated_at",
        ]


class UserSerializer(serializers.ModelSerializer):
    social_account_extra_data = serializers.SerializerMethodField()

    class Meta:
        model = get_user_model()
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "is_annonymous",
            "social_account_extra_data",
        ]

    def get_social_account_extra_data(self, obj):
        try:
            return SocialAccount.objects.get(user=obj).extra_data
        except SocialAccount.DoesNotExist:
            return None


class AppTokenSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = TokenModel
        fields = ("key", "user")


class ReviewCardSerializer(serializers.ModelSerializer):
    card = CardSerializer(required=False)

    class Meta:
        model = ReviewCard
        fields = [
            "card",
            "delta_score",
            "created_at",
            "updated_at",
        ]


class ReviewSerializer(serializers.ModelSerializer):
    owner = serializers.PrimaryKeyRelatedField(read_only=True)
    # https://stackoverflow.com/a/********/8559774
    cards = ReviewCardSerializer(
        source="reviewcard_set", many=True, required=False, read_only=True
    )
    # decks = ReviewDeckField(queryset=Deck.objects.all(), many=True, required=False)
    decks = serializers.PrimaryKeyRelatedField(
        queryset=Deck.objects.all(), many=True, required=False
    )

    class Meta:
        model = Review
        fields = [
            "id",
            "owner",
            "cards",
            "decks",
            "is_master",
            "created_at",
            "updated_at",
        ]

    def create(self, validated_data):
        decks_data = validated_data.pop("decks")
        cards_data = validated_data.pop("cards")
        review = Review.objects.create(**validated_data)
        review.cards.set(cards_data)
        review.decks.set(decks_data)
        return review


class LLMModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = LLMModel
        fields = [
            "id",
            "name",
            "label",
            "provider",
            "arena_score",
            "is_free",
            "is_hidden",
            "is_thinking",
            "created_at",
            "updated_at",
        ]


class WordComparisonSerializer(serializers.ModelSerializer):
    class Meta:
        model = WordComparison
        fields = [
            "id",
            "words",
            "content",
            "llm_model",
            "creator",
            "created_at",
            "updated_at",
        ]


class WordInContextSerializer(serializers.ModelSerializer):
    class Meta:
        model = WordInContext
        fields = [
            "id",
            "word",
            "context",
            "offset",
            "definition",
            "definition_short",
            "creator",
            "llm_model",
            "created_at",
            "updated_at",
        ]


class DefinitionCardSerializer(serializers.ModelSerializer):
    # supports both read and write
    # read -> word object instead of id
    # write -> word id
    word_ref = serializers.PrimaryKeyRelatedField(
        queryset=Word.objects.all(), required=False
    )
    word_ref_detail = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = DefinitionCard
        fields = [
            "id",
            "word",
            "word_ref",
            "word_ref_detail",
            "word_def_idx",
            "definition",
            "created_at",
            "updated_at",
        ]

    def get_word_ref_detail(self, obj):
        if obj.word_ref:
            return WordSerializer(obj.word_ref).data
        return None


class ContextCardSerializer(serializers.ModelSerializer):
    word_in_context = serializers.PrimaryKeyRelatedField(
        queryset=WordInContext.objects.all(), required=False
    )
    word_in_context_detail = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ContextCard
        fields = [
            "id",
            "word_in_context",
            "word_in_context_detail",
            "created_at",
            "updated_at",
        ]

    def get_word_in_context_detail(self, obj):
        if obj.word_in_context:
            return WordInContextSerializer(obj.word_in_context).data
        return None


class GenericCardSerializer(serializers.ModelSerializer):
    card = serializers.SerializerMethodField()
    card_content_type = serializers.PrimaryKeyRelatedField(
        queryset=ContentType.objects.all()
    )
    card_object_id = serializers.IntegerField()
    owner = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = GenericCard
        fields = [
            "id",
            "card",
            "card_content_type",
            "card_object_id",
            "owner",
            "progress_score",
            "created_at",
            "updated_at",
        ]

    def get_card(self, obj):
        """
        Retrieves the serialized data of the related object.
        """
        try:
            model = obj.card_content_type.model_class()
            serializer_class = self.get_serializer_class(model)
            serializer = serializer_class(obj.card)
            return serializer.data
        except Exception as e:
            print(f"Error in get_card: {e}")
            return None

    def get_serializer_class(self, model):
        """
        Dynamically determines the serializer class based on the related object's model.
        """
        # Assuming a naming convention for serializers (e.g., ModelName + "Serializer")
        serializer_name = model.__name__ + "Serializer"
        # Assuming serializers are in a 'serializers' module within the app
        app_label = model._meta.app_label
        try:
            serializers_module = apps.get_app_config(app_label).module
            serializer_class = getattr(serializers_module.serializers, serializer_name)
            return serializer_class
        except (AttributeError, LookupError):
            # Handle cases where the serializer is not found or app is not installed
            raise serializers.ValidationError(
                f"Serializer for {model.__name__} not found."
            )


class Deck2Serializer(serializers.ModelSerializer):
    owner = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = Deck2
        fields = [
            "id",
            "name",
            "description",
            "owner",
            "is_demo",
            "created_at",
            "updated_at",
        ]


class DeckCard2Serializer(serializers.ModelSerializer):
    deck = serializers.PrimaryKeyRelatedField(read_only=True)
    card = GenericCardSerializer(read_only=True)

    class Meta:
        model = DeckCard2
        fields = [
            "id",
            "deck",
            "card",
            "created_at",
            "updated_at",
        ]


class DeckWithCard2Serializer(serializers.ModelSerializer):
    owner = serializers.PrimaryKeyRelatedField(read_only=True)
    card_set = serializers.SerializerMethodField()

    class Meta:
        model = Deck2
        fields = [
            "id",
            "name",
            "description",
            "owner",
            "is_demo",
            "card_set",
            "created_at",
            "updated_at",
        ]

    def get_card_set(self, obj):
        pagination_class = self.context["pagination_class"]
        pagination = pagination_class()
        queryset = obj.deckcard2_set.order_by("-created_at").prefetch_related(
            Prefetch(
                "card",
                queryset=GenericCard.objects.all()
                .prefetch_related(
                    GenericPrefetch(
                        "card",
                        [
                            DefinitionCard.objects.all().select_related("word_ref"),
                            ContextCard.objects.all().select_related("word_in_context"),
                        ],
                    ),
                )
                .select_related("card_content_type"),
            )
        )
        page = pagination.paginate_queryset(queryset, self.context["request"])

        serializer = DeckCard2Serializer(page, many=True)
        return {
            "count": pagination.page.paginator.count,
            "previous": pagination.get_previous_link(),
            "next": pagination.get_next_link(),
            "results": serializer.data,
        }


class ReviewGenericCardSerializer(serializers.ModelSerializer):
    card = GenericCardSerializer(required=False)

    class Meta:
        model = ReviewGenericCard
        fields = [
            "card",
            "delta_score",
            "created_at",
            "updated_at",
        ]


class Review2Serializer(serializers.ModelSerializer):
    owner = serializers.PrimaryKeyRelatedField(read_only=True)
    cards = ReviewGenericCardSerializer(
        source="reviewgenericcard_set", many=True, required=False, read_only=True
    )
    decks = serializers.PrimaryKeyRelatedField(
        queryset=Deck2.objects.all(), many=True, required=False
    )

    class Meta:
        model = Review2
        fields = [
            "id",
            "owner",
            "cards",
            "decks",
            "is_master",
            "created_at",
            "updated_at",
        ]


class FeatureFlagSerializer(serializers.ModelSerializer):
    except_users = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=get_user_model().objects.all(),
        required=False
    )

    class Meta:
        model = FeatureFlag
        fields = [
            "name",
            "is_active",
            "except_users",
            "created_at",
            "updated_at",
        ]


# Serializer for CustomTone model
class CustomToneSerializer(serializers.ModelSerializer):
    owner = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = CustomTone
        fields = [
            "id",
            "owner",
            "name",
            "description",
            "created_at",
            "updated_at",
        ]


class WritingRevisionHistorySerializer(serializers.ModelSerializer):
    owner = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = WritingRevisionHistory
        fields = [
            "id",
            "owner",
            "created_at",
            "trigger_type",
            "original_text",
            "use_my_vocabulary",
            "selected_tone",
            "revisions",
        ]


class WritingRevisionHistoryCardsSerializer(serializers.ModelSerializer):
    history = serializers.PrimaryKeyRelatedField(queryset=WritingRevisionHistory.objects.all())
    card = serializers.PrimaryKeyRelatedField(queryset=GenericCard.objects.all())

    class Meta:
        model = WritingRevisionHistoryCards
        fields = [
            "id",
            "history",
            "card",
        ]
