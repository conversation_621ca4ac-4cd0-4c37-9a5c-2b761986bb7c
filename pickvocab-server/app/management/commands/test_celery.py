from django.core.management.base import BaseCommand
from app.tasks import example_task

class Command(BaseCommand):
    help = 'Test Celery by sending a task'

    def add_arguments(self, parser):
        parser.add_argument('param', type=str, help='Parameter to pass to the task')

    def handle(self, *args, **options):
        param = options['param']
        self.stdout.write(self.style.SUCCESS(f'Sending task with parameter: {param}'))
        
        # Send the task to Celery
        result = example_task.delay(param)
        
        self.stdout.write(self.style.SUCCESS(f'Task sent with ID: {result.id}'))
        self.stdout.write(self.style.SUCCESS('Check the Celery worker logs to see the task execution.')) 