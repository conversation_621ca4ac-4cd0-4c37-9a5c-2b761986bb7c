from rest_framework import permissions


class IsOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """

    def has_object_permission(self, request, view, obj):
        return obj.owner == request.user


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    Assumes the model instance has an `owner` attribute.
    """
    owner_field = 'owner'  # Field name to check for ownership

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Instance must have an attribute named by `owner_field`.
        return getattr(obj, self.owner_field) == request.user

class IsCreatorOrReadOnly(IsOwnerOrReadOnly):
    """
    Custom permission to only allow creators of an object to edit it.
    Assumes the model instance has a `creator` attribute.
    """
    owner_field = 'creator'  # Override owner_field for creator check


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow admins to edit it.
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True

        return request.user.is_superuser

    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS:
            return True

        return request.user.is_superuser


class IsDemoDeck(permissions.BasePermission):
    """
    Allow users to view demo decks.
    """
    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS and obj.is_demo:
            return True

        return False


class IsDemoCardReadonly(permissions.BasePermission):
    """
    Allow users to view demo cards.
    """
    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS and obj.decks.filter(is_demo=True).exists():
            return True

        return False