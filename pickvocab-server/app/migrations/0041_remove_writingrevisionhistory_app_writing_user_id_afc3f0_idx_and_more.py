# Generated by Django 5.0.6 on 2025-04-08 09:40

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0040_writingrevisionhistory_writingrevisionhistorycards_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='writingrevisionhistory',
            name='app_writing_user_id_afc3f0_idx',
        ),
        migrations.RenameField(
            model_name='writingrevisionhistory',
            old_name='user',
            new_name='owner',
        ),
        migrations.AddIndex(
            model_name='writingrevisionhistory',
            index=models.Index(fields=['owner'], name='app_writing_owner_i_548ac6_idx'),
        ),
    ]
