# Generated by Django 5.0.4 on 2024-04-27 05:11

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Word',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('word', models.CharField(max_length=200)),
                ('definitions', models.JSONField(default=list)),
            ],
            options={
                'indexes': [models.Index(fields=['word'], name='app_word_word_be39c8_idx')],
            },
        ),
    ]
