# Generated by Django 5.0.6 on 2024-11-14 01:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0022_alter_wordcomparison_content_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='llmmodel',
            name='provider',
            field=models.CharField(choices=[('OpenAI', 'OpenAI'), ('Anthropic', 'Anthropic'), ('Gemini', 'Gemini'), ('OpenRouter', 'OpenRouter'), ('ArliAI', 'ArliAI'), ('Groq', 'Groq'), ('Cerebras', 'Cerebras'), ('SambaNova', '<PERSON>ba<PERSON>ova'), ('Pickvocab', 'Pickvocab')], max_length=50),
        ),
        migrations.CreateModel(
            name='VerifiedWord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('word', models.CharField(max_length=200)),
                ('definitions', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('creator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('llm_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.llmmodel')),
            ],
            options={
                'ordering': ['-id'],
                'indexes': [models.Index(fields=['word'], name='app_verifie_word_d2de1a_idx')],
            },
        ),
    ]
