# Generated by Django 5.0.6 on 2025-03-20 03:34

import django.db.models.deletion
import pgvector.django.indexes
import pgvector.django.vector
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0037_5_create_vector_extension'),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='userembeddingpreference',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.CreateModel(
            name='GeminiTextEmbedding004Store',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vector', pgvector.django.vector.VectorField(dimensions=768)),
                ('object_id', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('embedding_model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.embeddingmodel')),
            ],
            options={
                'ordering': ['-id'],
                'indexes': [models.Index(fields=['content_type', 'object_id'], name='app_geminit_content_89f9d9_idx'), pgvector.django.indexes.IvfflatIndex(fields=['vector'], lists=100, name='gemini_vector_idx', opclasses=['vector_cosine_ops'])],
            },
        ),
        migrations.CreateModel(
            name='MistralEmbedStore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vector', pgvector.django.vector.VectorField(dimensions=1024)),
                ('object_id', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('embedding_model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.embeddingmodel')),
            ],
            options={
                'ordering': ['-id'],
                'indexes': [models.Index(fields=['content_type', 'object_id'], name='app_mistral_content_d38688_idx'), pgvector.django.indexes.IvfflatIndex(fields=['vector'], lists=100, name='mistral_vector_idx', opclasses=['vector_cosine_ops'])],
            },
        ),
        migrations.CreateModel(
            name='WordDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('word_def_idx', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('word', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.word')),
            ],
            options={
                'ordering': ['-id'],
                'indexes': [models.Index(fields=['word'], name='app_worddef_word_id_7ca471_idx')],
            },
        ),
    ]
