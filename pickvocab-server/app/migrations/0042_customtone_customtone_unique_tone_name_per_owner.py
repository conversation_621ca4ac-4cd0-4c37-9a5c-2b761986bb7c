# Generated by Django 5.0.6 on 2025-04-12 03:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0041_remove_writingrevisionhistory_app_writing_user_id_afc3f0_idx_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomTone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_tones', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['owner'], name='app_customt_owner_i_aed8cd_idx'), models.Index(fields=['name'], name='app_customt_name_2f7bb5_idx')],
            },
        ),
        migrations.AddConstraint(
            model_name='customtone',
            constraint=models.UniqueConstraint(fields=('owner', 'name'), name='unique_tone_name_per_owner'),
        ),
    ]
