# Generated by Django 5.0.6 on 2024-06-23 10:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0010_alter_deck_description'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ReviewCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.card')),
                ('review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.review')),
            ],
        ),
        migrations.AddField(
            model_name='review',
            name='cards',
            field=models.ManyToManyField(through='app.ReviewCard', to='app.card'),
        ),
        migrations.CreateModel(
            name='ReviewDeck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deck', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.deck')),
                ('review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.review')),
            ],
        ),
        migrations.AddField(
            model_name='review',
            name='decks',
            field=models.ManyToManyField(through='app.ReviewDeck', to='app.deck'),
        ),
    ]
