# Generated by Django 5.0.6 on 2025-02-08 00:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0033_featureflag'),
    ]

    operations = [
        migrations.AddField(
            model_name='wordincontext',
            name='definition_short',
            field=models.JSONField(null=True),
        ),
        migrations.AlterField(
            model_name='llmmodel',
            name='provider',
            field=models.CharField(choices=[('OpenAI', 'OpenAI'), ('Anthropic', 'Anthropic'), ('Gemini', 'Gemini'), ('OpenRouter', 'OpenRouter'), ('ArliAI', 'ArliAI'), ('Groq', 'Groq'), ('<PERSON><PERSON><PERSON><PERSON>', 'Cere<PERSON>s'), ('SambaNova', 'SambaNova'), ('Pickvocab', 'Pickvocab'), ('Mistral', 'Mistral')], max_length=50),
        ),
    ]
