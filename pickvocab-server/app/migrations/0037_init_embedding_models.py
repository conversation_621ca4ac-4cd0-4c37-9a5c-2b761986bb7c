# Generated manually

import os
from django.db import migrations

def init_embedding_models(apps, schema_editor):
    """
    Initialize two embedding models:
    1. Gemini: models/text-embedding-004 with 768 dimensions
    2. Mistral: mistral-embed with 1024 dimensions
    """
    EmbeddingModel = apps.get_model('app', 'EmbeddingModel')

    # Create Gemini embedding model
    gemini_model = EmbeddingModel(
        provider='Gemini',
        model_name='models/text-embedding-004',
        dimensions=768,
        api_key=os.environ.get('GOOGLE_API_KEY', '')
    )
    gemini_model.save()

    # Create Mistral embedding model
    mistral_model = EmbeddingModel(
        provider='Mistral',
        model_name='mistral-embed',
        dimensions=1024,
        api_key=os.environ.get('MISTRAL_API_KEY', '')
    )
    mistral_model.save()


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0036_embeddingmodel_userembeddingpreference'),
    ]

    operations = [
        migrations.RunPython(init_embedding_models),
    ]
