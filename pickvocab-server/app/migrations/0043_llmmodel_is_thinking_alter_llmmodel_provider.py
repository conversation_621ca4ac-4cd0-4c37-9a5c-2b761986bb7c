# Generated by Django 5.0.6 on 2025-05-01 01:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0042_customtone_customtone_unique_tone_name_per_owner'),
    ]

    operations = [
        migrations.AddField(
            model_name='llmmodel',
            name='is_thinking',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='llmmodel',
            name='provider',
            field=models.CharField(choices=[('OpenAI', 'OpenAI'), ('Anthropic', 'Anthropic'), ('Gemini', 'Gemini'), ('OpenRouter', 'OpenRouter'), ('ArliAI', 'ArliAI'), ('Groq', 'Groq'), ('Cere<PERSON>s', 'Cere<PERSON>s'), ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'), ('Pickvocab', 'Pickvocab'), ('Mistral', 'Mistral'), ('Codestral', 'Codestral'), ('Chutes', 'Chutes')], max_length=50),
        ),
    ]
