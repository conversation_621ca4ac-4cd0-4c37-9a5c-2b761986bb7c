# Generated by Django 5.0.6 on 2025-04-08 09:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0039_alter_featureflag_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WritingRevisionHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('trigger_type', models.CharField(max_length=20)),
                ('original_text', models.TextField()),
                ('use_my_vocabulary', models.BooleanField()),
                ('selected_tone', models.CharField(max_length=100)),
                ('revisions', models.JSONField()),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WritingRevisionHistoryCards',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.genericcard')),
                ('history', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cards', to='app.writingrevisionhistory')),
            ],
        ),
        migrations.AddIndex(
            model_name='writingrevisionhistory',
            index=models.Index(fields=['user'], name='app_writing_user_id_afc3f0_idx'),
        ),
        migrations.AddIndex(
            model_name='writingrevisionhistory',
            index=models.Index(fields=['created_at'], name='app_writing_created_799b54_idx'),
        ),
        migrations.AddIndex(
            model_name='writingrevisionhistory',
            index=models.Index(fields=['trigger_type'], name='app_writing_trigger_e68306_idx'),
        ),
        migrations.AddIndex(
            model_name='writingrevisionhistorycards',
            index=models.Index(fields=['history'], name='app_writing_history_b564b9_idx'),
        ),
        migrations.AddIndex(
            model_name='writingrevisionhistorycards',
            index=models.Index(fields=['card'], name='app_writing_card_id_e1980a_idx'),
        ),
    ]
