# Generated by Django 5.0.6 on 2024-12-23 09:58

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0032_review2_reviewdeck2_review2_decks_reviewgenericcard_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FeatureFlag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('GenericCard', 'GenericCard')], max_length=100, unique=True)),
                ('is_active', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('except_users', models.ManyToManyField(blank=True, related_name='feature_flags', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
