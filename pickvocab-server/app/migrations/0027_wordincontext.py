# Generated by Django 5.0.6 on 2024-12-07 05:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0026_remove_word_app_word_word_be39c8_idx_word_slug_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WordInContext',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('word', models.CharField(max_length=2000)),
                ('context', models.CharField(max_length=10000)),
                ('offset', models.PositiveSmallIntegerField(default=0)),
                ('definition', models.J<PERSON>NField(default=dict, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('creator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('llm_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.llmmodel')),
            ],
            options={
                'ordering': ['-id'],
                'indexes': [models.Index(fields=['word'], name='app_wordinc_word_fae4cf_idx')],
            },
        ),
    ]
