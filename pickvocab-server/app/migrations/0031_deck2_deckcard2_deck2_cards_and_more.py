# Generated by Django 5.0.6 on 2024-12-22 04:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0030_contextcard_genericcard_definitioncard'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Deck2',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.CharField(blank=True, max_length=500, null=True)),
                ('is_demo', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='DeckCard2',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.genericcard')),
                ('deck', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.deck2')),
            ],
        ),
        migrations.AddField(
            model_name='deck2',
            name='cards',
            field=models.ManyToManyField(related_name='decks', through='app.DeckCard2', to='app.genericcard'),
        ),
        migrations.AddIndex(
            model_name='deck2',
            index=models.Index(fields=['name'], name='app_deck2_name_d8ca77_idx'),
        ),
        migrations.AddIndex(
            model_name='deck2',
            index=models.Index(fields=['is_demo'], name='app_deck2_is_demo_f3347b_idx'),
        ),
    ]
