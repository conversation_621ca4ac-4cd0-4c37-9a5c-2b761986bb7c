# Generated by Django 5.0.6 on 2024-10-13 05:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0020_alter_deck_cards_deck_app_deck_is_demo_cdbd00_idx'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='llmmodel',
            name='provider',
            field=models.CharField(choices=[('OpenAI', 'OpenAI'), ('Anthropic', 'Anthropic'), ('Gemini', 'Gemini'), ('OpenRouter', 'OpenRouter'), ('ArliAI', 'ArliAI'), ('Groq', 'Groq'), ('Cerebras', 'Cerebras'), ('Pickvocab', 'Pickvocab')], max_length=50),
        ),
        migrations.CreateModel(
            name='WordComparison',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('words', models.CharField(max_length=500)),
                ('content', models.CharField(max_length=5000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('creator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('llm_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.llmmodel')),
            ],
            options={
                'ordering': ['-id'],
                'indexes': [models.Index(fields=['words'], name='app_wordcom_words_3f2798_idx')],
            },
        ),
    ]
