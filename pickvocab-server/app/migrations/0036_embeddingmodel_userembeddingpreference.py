# Generated by Django 5.0.6 on 2025-03-19 08:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0035_celery_results'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmbeddingModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider', models.CharField(choices=[('Gemini', 'Gemini'), ('Mistral', 'Mistral')], max_length=50)),
                ('dimensions', models.PositiveIntegerField()),
                ('model_name', models.CharField(max_length=200)),
                ('api_key', models.CharField(max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-id'],
                'indexes': [models.Index(fields=['model_name'], name='app_embeddi_model_n_d10f08_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserEmbeddingPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('embedding_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.embeddingmodel')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user'], name='app_useremb_user_id_5fd683_idx')],
            },
        ),
    ]
