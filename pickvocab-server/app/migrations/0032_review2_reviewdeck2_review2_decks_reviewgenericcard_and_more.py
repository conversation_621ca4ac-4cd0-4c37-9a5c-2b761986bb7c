# Generated by Django 5.0.6 on 2024-12-22 05:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0031_deck2_deckcard2_deck2_cards_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Review2',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_master', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ReviewDeck2',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deck', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.deck2')),
                ('review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.review2')),
            ],
        ),
        migrations.AddField(
            model_name='review2',
            name='decks',
            field=models.ManyToManyField(through='app.ReviewDeck2', to='app.deck2'),
        ),
        migrations.CreateModel(
            name='ReviewGenericCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('delta_score', models.SmallIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.genericcard')),
                ('review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.review2')),
            ],
        ),
        migrations.AddField(
            model_name='review2',
            name='cards',
            field=models.ManyToManyField(through='app.ReviewGenericCard', to='app.genericcard'),
        ),
    ]
