# Generated by Django 5.0.6 on 2024-08-31 00:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0013_review_is_master'),
    ]

    operations = [
        migrations.CreateModel(
            name='LLMModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('provider', models.CharField(choices=[('OPENAI', 'OpenAI'), ('ANTHROPIC', 'Anthropic'), ('GEMINI', 'GEMINI')], max_length=50)),
                ('arena_score', models.SmallIntegerField(default=0)),
            ],
        ),
        migrations.AddField(
            model_name='word',
            name='llm_model',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.llmmodel'),
        ),
    ]
