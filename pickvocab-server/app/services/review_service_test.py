from typing import TypeVar
from app.services.review_service import pick_items_from_buckets
from django.test import TestCase

T = TypeVar("T")

def get_count_map(buckets: dict[int, list[T]], items: list[T]) -> dict[int, int]:
    counts = {}

    for bucket_id, bucket_items in buckets.items():
        counts[bucket_id] = len([item for item in items if item in bucket_items])

    return counts


class ReviewServiceTest(TestCase):
    def test_pick_items_from_buckets_1(self):
        buckets = {
            1: ['100', '101', '102', '103', '104', '105', '106', '107', '108', '109', '110'],
            2: ['200', '201', '202', '203', '204', '205', '206', '207', '208', '209', '210'],
            3: ['300', '301', '302', '303', '304', '305', '306', '307', '308', '309', '310'],
            4: ['400', '401', '402', '403', '404', '405', '406', '407', '408', '409', '410'],
            5: ['500', '501', '502', '503', '504', '505', '506', '507', '508', '509', '510'],
        }
        items = pick_items_from_buckets(buckets, 10)
        count_map = get_count_map(buckets, items)
        self.assertDictEqual(count_map, {
            1: 5,
            2: 2,
            3: 1,
            4: 1,
            5: 1,
        })

    def test_pick_items_from_buckets_2(self):
        buckets = {
            1: ['100', '101'],
            2: ['200', '201', '202'],
            3: ['300'],
            4: ['400', '401'],
            5: ['500', '501', '502', '503', '504', '505', '506', '507', '508', '509', '510'],
        }
        items = pick_items_from_buckets(buckets, 10)
        count_map = get_count_map(buckets, items)
        self.assertDictEqual(count_map, {
            1: 2,
            2: 3,
            3: 1,
            4: 2,
            5: 2
        })

    def test_pick_items_from_buckets_3(self):
        buckets = {
            1: [],
            2: [],
            3: ['300', '301', '302', '303', '304', '305', '306', '307', '308', '309', '310'],
            4: [],
            5: ['500', '501', '502', '503', '504', '505', '506', '507', '508', '509', '510'],
        }
        items = pick_items_from_buckets(buckets, 10)
        count_map = get_count_map(buckets, items)
        self.assertDictEqual(count_map, {
            1: 0,
            2: 0,
            3: 8,
            4: 0,
            5: 2
        })

    def test_pick_items_from_buckets_4(self):
        buckets = {
            1: ['100', '101', '102', '103', '104', '105', '106', '107', '108', '109', '110'],
            2: [],
            3: [],
            4: [],
            5: [],
        }
        items = pick_items_from_buckets(buckets, 10)
        count_map = get_count_map(buckets, items)
        self.assertDictEqual(count_map, {
            1: 10,
            2: 0,
            3: 0,
            4: 0,
            5: 0
        })

    def test_pick_items_from_buckets_5(self):
        buckets = {
            1: [],
            2: [],
            3: [],
            4: ['400', '401', '402', '403'],
            5: ['500', '501'],
        }
        items = pick_items_from_buckets(buckets, 10)
        count_map = get_count_map(buckets, items)
        self.assertDictEqual(count_map, {
            1: 0,
            2: 0,
            3: 0,
            4: 4,
            5: 2
        })

    def test_pick_items_from_buckets_6(self):
        buckets = {
            1: [],
            2: [],
            3: [],
            4: ['400', '401', '402', '403', '404', '405', '406', '407', '408', '409', '410'],
            5: ['500', '501'],
        }
        items = pick_items_from_buckets(buckets, 10)
        count_map = get_count_map(buckets, items)
        self.assertDictEqual(count_map, {
            1: 0,
            2: 0,
            3: 0,
            4: 9,
            5: 1
        })

    def test_pick_items_from_buckets_7(self):
        buckets = {
            1: [],
            2: [],
            3: [],
            4: [],
            5: [],
        }
        items = pick_items_from_buckets(buckets, 10)
        count_map = get_count_map(buckets, items)
        self.assertDictEqual(count_map, {
            1: 0,
            2: 0,
            3: 0,
            4: 0,
            5: 0
        })
