from typing import List, Dict

from langchain_mistralai import MistralAIEmbeddings
from .base import BaseEmbeddingService
from ...models import EmbeddingModel
from app.utils import get_service_logger

logger = get_service_logger()


class MistralEmbeddingService(BaseEmbeddingService):
    """
    Embedding service implementation for Mistral's models.
    """

    def __init__(self, embedding_model: EmbeddingModel):
        super().__init__(embedding_model)
        self.embeddings_model = MistralAIEmbeddings(
            model=self.model_name, mistral_api_key=self.api_key
        )

    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate an embedding for the given text using Mistral model.

        Args:
            text: The text to generate an embedding for

        Returns:
            A list of floating point values representing the embedding
        """
        try:
            result = self.embeddings_model.embed_query(text)
            return result
        except Exception as e:
            logger.error(f"Error generating Mistral embedding: {e}")
            raise

    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts using Mistral model.

        This implementation deduplicates identical texts to avoid redundant API calls,
        then reconstructs the full results array to maintain the original ordering.

        Args:
            texts: The list of texts to generate embeddings for

        Returns:
            A list of embeddings, each being a list of floating point values
        """
        if not texts:
            return []

        try:
            # Create a mapping of unique texts to their indices in the original list
            unique_texts_map: Dict[str, List[int]] = {}
            for i, text in enumerate(texts):
                if text in unique_texts_map:
                    unique_texts_map[text].append(i)
                else:
                    unique_texts_map[text] = [i]

            # Generate embeddings only for unique texts
            unique_texts = list(unique_texts_map.keys())
            unique_embeddings = self.embeddings_model.embed_documents(unique_texts)

            # Create a mapping of text to its embedding for efficient lookup
            text_to_embedding = dict(zip(unique_texts, unique_embeddings))

            # Reconstruct results in the original order
            results = [None] * len(texts)
            for text, indices in unique_texts_map.items():
                text_embedding = text_to_embedding[text]
                for idx in indices:
                    results[idx] = text_embedding

            return results
        except Exception as e:
            logger.error(f"Error generating Mistral embeddings: {e}")
            raise
