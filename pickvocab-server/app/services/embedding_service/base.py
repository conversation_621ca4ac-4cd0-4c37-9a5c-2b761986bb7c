from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Type
from django.contrib.contenttypes.models import ContentType
from django.db.models import Model
from pgvector.django import CosineDistance

from ...models import (
    EmbeddingModel,
    GeminiTextEmbedding004Store,
    MistralEmbedStore,
)
from ...utils import get_service_logger

logger = get_service_logger()


class BaseEmbeddingService(ABC):
    """
    Abstract base class for embedding services.

    This class defines the interface that all embedding services must implement.
    It also provides common utility methods for working with embeddings.
    """

    def __init__(self, embedding_model: EmbeddingModel):
        """
        Initialize the embedding service with an embedding model.

        Args:
            embedding_model: The EmbeddingModel instance to use for generating embeddings
        """
        self.embedding_model = embedding_model
        self.api_key = embedding_model.api_key
        self.model_name = embedding_model.model_name
        self.dimensions = embedding_model.dimensions
        self.provider = embedding_model.provider

    @abstractmethod
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate an embedding for the given text.

        Args:
            text: The text to generate an embedding for

        Returns:
            A list of floating point values representing the embedding
        """
        pass

    @abstractmethod
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in a single batch operation.

        This method should be more efficient than calling generate_embedding multiple times
        as it can take advantage of batching in the underlying API calls.

        Implementation notes:
        - Should handle empty lists gracefully (return empty list)
        - May apply internal size limits and break requests into batches if needed
        - Should maintain order of results to match input texts
        - Should handle errors for individual texts without failing the entire batch

        Args:
            texts: List of texts to generate embeddings for

        Returns:
            A list of embeddings, each being a list of floating point values.
            The order matches the input texts.
        """
        pass

    def get_embedding_store_model(self):
        """
        Get the appropriate embedding store model based on the provider.

        Returns:
            The embedding store model class
        """
        if self.provider == EmbeddingModel.EmbeddingProvider.Gemini:
            return GeminiTextEmbedding004Store
        elif self.provider == EmbeddingModel.EmbeddingProvider.Mistral:
            return MistralEmbedStore
        else:
            raise ValueError(f"Unsupported embedding provider: {self.provider}")

    def similarity_search(
        self,
        query: str,
        content_type_models: Optional[List[Type[Model]]] = None,
        limit: int = 10,
        threshold: float = 0.0,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Perform a similarity search against stored embeddings.

        Args:
            query: The query text to search for
            content_type_models: List of model classes to restrict the search to
                               (e.g., [WordDefinition, WordInContext])
            limit: Maximum number of results to return (default: 10)
            threshold: Minimum similarity score threshold (default: 0.0)
            filters: Additional filters to apply to the query

        Returns:
            List of dictionaries containing object_id, content_object, similarity_score
        """
        # Generate query embedding
        query_embedding = self.generate_embedding(query)
        
        # Get the store model based on the provider
        store_model = self.get_embedding_store_model()
        
        # Start building the query
        qs = store_model.objects.filter(embedding_model=self.embedding_model)
        
        # Apply content type filter if provided
        if content_type_models:
            # Get content types for all models
            content_types = [ContentType.objects.get_for_model(model) for model in content_type_models]
            
            # Filter by any of the content types
            if content_types:
                qs = qs.filter(content_type__in=content_types)
        
        # Apply additional filters if provided
        if filters:
            qs = qs.filter(**filters)
        
        # Calculate cosine similarity (1 - cosine_distance)
        # Lower distance means higher similarity
        qs = qs.annotate(
            similarity=1 - CosineDistance('vector', query_embedding)
        )
        
        # Filter by threshold, sort by similarity (highest first), and limit results
        results = qs.filter(similarity__gte=threshold).order_by('-similarity')[:limit]
        
        # Convert to dictionary list
        return [
            {
                'object_id': result.object_id,
                'content_object': result.content_object,
                'similarity_score': result.similarity,
                'content_type': result.content_type,
            }
            for result in results
        ]
