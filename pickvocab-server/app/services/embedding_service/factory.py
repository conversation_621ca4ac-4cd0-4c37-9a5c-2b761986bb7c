from typing import Op<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Type
import environ

from django.conf import settings

from ...models import EmbeddingModel
from .base import BaseEmbeddingService
from .gemini_service import GeminiEmbeddingService
from .mistral_service import MistralEmbeddingService
from app.utils import get_service_logger

logger = get_service_logger()

# Initialize environ
env = environ.Env()

# Cache of embedding services to avoid recreation
_embedding_service_cache: Dict[Tuple[str, str, int], BaseEmbeddingService] = {}


def get_embedding_service(
    provider: Optional[str] = None,
    model_name: Optional[str] = None,
    embedding_model_id: Optional[int] = None,
) -> BaseEmbeddingService:
    """
    Factory function to get the appropriate embedding service.

    This function implements a caching mechanism to avoid recreating
    embedding service instances. Services are cached by provider, model name,
    and embedding model ID.

    Args:
        provider: Optional provider name (Gemini or Mistral)
        model_name: Optional model name
        embedding_model_id: Optional embedding model ID from the database

    Returns:
        An instance of the appropriate embedding service

    Raises:
        ValueError: If the provider is not supported or if no embedding model can be found
    """
    # Normalize arguments
    if provider:
        provider = provider.capitalize()

    # Check cache first
    cache_key = (str(provider), str(model_name), embedding_model_id or 0)
    if cache_key in _embedding_service_cache:
        logger.info(f"Using cached embedding service for {provider}/{model_name}")
        return _embedding_service_cache[cache_key]

    embedding_model = None

    # If embedding_model_id is provided, get the model from the database
    if embedding_model_id is not None:
        try:
            embedding_model = EmbeddingModel.objects.get(id=embedding_model_id)
        except EmbeddingModel.DoesNotExist:
            logger.error(f"Embedding model with ID {embedding_model_id} not found")
            raise ValueError(f"Embedding model with ID {embedding_model_id} not found")

    # If provider and model_name are provided, find or create the model
    elif provider is not None and model_name is not None:
        # Convert provider to the format used in the model
        if provider.lower() == "gemini":
            provider_enum = EmbeddingModel.EmbeddingProvider.Gemini
        elif provider.lower() == "mistral":
            provider_enum = EmbeddingModel.EmbeddingProvider.Mistral
        else:
            logger.error(f"Unsupported embedding provider: {provider}")
            raise ValueError(f"Unsupported embedding provider: {provider}")

        # Try to find an existing model
        try:
            embedding_model = EmbeddingModel.objects.get(
                provider=provider_enum, model_name=model_name
            )
        except EmbeddingModel.DoesNotExist:
            raise ValueError(
                f"Embedding model with provider {provider} and model name {model_name} not found"
            )

    # If no parameters are provided, try to get a default model
    else:
        # get gemini model
        try:
            embedding_model = EmbeddingModel.objects.get(
                provider=EmbeddingModel.EmbeddingProvider.Gemini,
                model_name="models/text-embedding-004",
            )
        except EmbeddingModel.DoesNotExist:
            raise ValueError("No default Gemini embedding model found in the database")

    # Create and return the appropriate service
    if embedding_model.provider == EmbeddingModel.EmbeddingProvider.Gemini:
        service = GeminiEmbeddingService(embedding_model)
    elif embedding_model.provider == EmbeddingModel.EmbeddingProvider.Mistral:
        service = MistralEmbeddingService(embedding_model)
    else:
        logger.error(f"Unsupported embedding provider: {embedding_model.provider}")
        raise ValueError(f"Unsupported embedding provider: {embedding_model.provider}")

    # Cache the service
    _embedding_service_cache[cache_key] = service
    return service
