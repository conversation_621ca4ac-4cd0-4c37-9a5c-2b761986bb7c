from typing import List, Tuple, Union, Optional, Dict, Callable, TypeVar, Any, Type
from django.db import transaction
from django.contrib.contenttypes.models import ContentType
import numpy as np
from functools import lru_cache, wraps

from .embedding_service.base import BaseEmbeddingService
from ..models import (
    Word,
    WordDefinition,
    WordInContext,
    GeminiTextEmbedding004Store,
    MistralEmbedStore,
    EmbeddingModel,
)
from ..utils import get_service_logger

# Use the service logger utility instead of direct logger instantiation
logger = get_service_logger()

# Type variables for generic memoization
T = TypeVar("T")
R = TypeVar("R")


def instance_memoize(
    func: Callable[[Any, T], R] = None, *, maxsize: int = 1024
) -> Callable[[Any, T], R]:
    """
    Decorator to memoize instance method results based on object id.
    Unlike lru_cache, this works with mutable objects by using their id() as key.

    Args:
        func: Instance method to memoize
        maxsize: Maximum number of items to store in cache before clearing

    Returns:
        Wrapped function with memoization
    """

    def decorator(f):
        cache = {}

        @wraps(f)
        def wrapper(self, obj):
            obj_id = id(obj)
            if obj_id not in cache:
                # Clear entire cache if it's too large
                if len(cache) >= maxsize:
                    cache.clear()
                cache[obj_id] = f(self, obj)
            return cache[obj_id]

        # Add method to manually clear cache if needed
        wrapper.clear_cache = lambda: cache.clear()

        return wrapper

    # Handle both @instance_memoize and @instance_memoize(maxsize=N) syntax
    if func is None:
        return decorator
    return decorator(func)


@lru_cache(maxsize=128)
def get_content_type_for_model(model_class):
    """
    Get ContentType for a model class with caching to avoid repeated DB lookups.

    Args:
        model_class: The model class

    Returns:
        The ContentType instance
    """
    return ContentType.objects.get_for_model(model_class)


class WordEmbeddingService:
    """
    Service for handling word-related embedding operations.
    This service uses an embedding service to generate and store embeddings for words and word contexts.
    """

    def __init__(self, embedding_service: BaseEmbeddingService):
        """
        Initialize the word embedding service.

        Args:
            embedding_service: The embedding service to use for generating embeddings
        """
        self.embedding_service = embedding_service

    @instance_memoize
    def get_embedding_text_for_word_definition(
        self, word_definition: WordDefinition
    ) -> str:
        """
        Get the text to use for embedding a word definition.

        Args:
            word_definition: The WordDefinition instance

        Returns:
            The text to embed
        """
        word = word_definition.word
        definition_idx = word_definition.word_def_idx

        if not word.definitions or definition_idx >= len(word.definitions):
            raise ValueError(
                f"Definition index {definition_idx} is out of range for word {word.word}"
            )

        definition = word.definitions[definition_idx].get("definition", "")
        if definition:
            return f"word: {word.word}\ndefinition: {definition}"
        else:
            return f"word: {word.word}"

    @instance_memoize
    def get_embedding_text_for_word_in_context(
        self, word_in_context: WordInContext
    ) -> str:
        """
        Get the text to use for embedding a word in context.

        Args:
            word_in_context: The WordInContext instance

        Returns:
            The text to embed
        """
        if word_in_context.definition:
            return f"word: {word_in_context.word}\ndefinition: {word_in_context.definition['explanation']}"
        elif word_in_context.definition_short:
            return f"word: {word_in_context.word}\ndefinition: {word_in_context.definition_short['explanation']}"
        else:
            return f"word: {word_in_context.word}"

    def get_existing_embedding(
        self, content_type_model, object_id: int
    ) -> Optional[List[float]]:
        """
        Check if an embedding already exists for the given content object.

        Args:
            content_type_model: The model class for which to get the ContentType
            object_id: The ID of the object for which to check embeddings

        Returns:
            The existing embedding vector if found, None otherwise
        """
        content_type = get_content_type_for_model(content_type_model)

        # Determine which store model to use based on provider
        provider = self.embedding_service.provider
        if provider == EmbeddingModel.EmbeddingProvider.Gemini:
            store_model = GeminiTextEmbedding004Store
        elif provider == EmbeddingModel.EmbeddingProvider.Mistral:
            store_model = MistralEmbedStore
        else:
            raise ValueError(f"Unsupported embedding provider: {provider}")

        # Check if embedding exists
        existing_embedding = store_model.objects.filter(
            content_type=content_type,
            object_id=object_id,
            embedding_model=self.embedding_service.embedding_model,
        ).first()

        if existing_embedding:
            logger.info(
                f"Found existing embedding for {content_type_model.__name__} with ID {object_id}"
            )
            return existing_embedding.vector

        return None

    def get_existing_embeddings_batch(
        self, content_type_model, object_ids: List[int]
    ) -> Dict[int, List[float]]:
        """
        Check if embeddings already exist for the given content objects in batch.

        Args:
            content_type_model: The model class for which to get the ContentType
            object_ids: List of object IDs to check

        Returns:
            Dictionary mapping object_id to embedding vector
        """
        if not object_ids:
            return {}

        content_type = get_content_type_for_model(content_type_model)

        # Determine which store model to use based on provider
        provider = self.embedding_service.provider
        if provider == EmbeddingModel.EmbeddingProvider.Gemini:
            store_model = GeminiTextEmbedding004Store
        elif provider == EmbeddingModel.EmbeddingProvider.Mistral:
            store_model = MistralEmbedStore
        else:
            raise ValueError(f"Unsupported embedding provider: {provider}")

        # Check for existing embeddings in batch
        existing_embeddings = store_model.objects.filter(
            content_type=content_type,
            object_id__in=object_ids,
            embedding_model=self.embedding_service.embedding_model,
        )

        # Create mapping from object_id to embedding vector
        result = {}
        for emb in existing_embeddings:
            result[emb.object_id] = emb.vector

        logger.info(
            f"Found {len(result)} existing embeddings for {content_type_model.__name__}"
        )
        return result

    # Pure calculation methods

    def calculate_embedding_for_word_definition(
        self, word: Word, word_def_idx: int
    ) -> Tuple[WordDefinition, List[float], bool]:
        """
        Calculate an embedding for a word definition without storing it.
        If an embedding already exists, returns that instead.

        Args:
            word: The Word instance
            word_def_idx: The index of the definition to embed

        Returns:
            A tuple containing:
            - The WordDefinition instance (created or retrieved)
            - The generated embedding vector
            - Boolean indicating if this is a new embedding (True) or retrieved from storage (False)

        Raises:
            ValueError: If definition index is out of range
        """
        # Get or create WordDefinition instance
        word_definition, created = WordDefinition.objects.get_or_create(
            word=word, word_def_idx=word_def_idx
        )

        # Check if embedding already exists
        existing_embedding = self.get_existing_embedding(
            WordDefinition, word_definition.id
        )
        if existing_embedding is not None:
            return word_definition, existing_embedding, False

        # Get text to embed
        text_to_embed = self.get_embedding_text_for_word_definition(word_definition)

        # Generate embedding
        embedding = self.embedding_service.generate_embedding(text_to_embed)

        return word_definition, embedding, True

    def calculate_embedding_for_word_in_context(
        self, word_in_context: WordInContext
    ) -> Tuple[List[float], bool]:
        """
        Calculate an embedding for a word in context without storing it.
        If an embedding already exists, returns that instead.

        Args:
            word_in_context: The WordInContext instance

        Returns:
            A tuple containing:
            - The generated embedding
            - Boolean indicating if this is a new embedding (True) or retrieved from storage (False)
        """
        # Check if embedding already exists
        existing_embedding = self.get_existing_embedding(
            WordInContext, word_in_context.id
        )
        if existing_embedding is not None:
            return existing_embedding, False

        # Get text to embed
        text_to_embed = self.get_embedding_text_for_word_in_context(word_in_context)

        # Generate embedding
        embedding = self.embedding_service.generate_embedding(text_to_embed)

        return embedding, True

    # Storage methods

    def store_embedding_for_object(
        self,
        content_object: Any,
        embedding: List[float],
        content_type_model: Optional[Type] = None,
    ) -> Union[GeminiTextEmbedding004Store, MistralEmbedStore]:
        """
        Store a pre-calculated embedding for any content object.

        Args:
            content_object: The object instance to store embedding for
            embedding: The embedding to store
            content_type_model: Optional model class (inferred from object if not provided)

        Returns:
            The created embedding store instance
        """
        # Infer content type model from object if not provided
        if content_type_model is None:
            content_type_model = type(content_object)

        content_type = get_content_type_for_model(content_type_model)

        # Determine which store model to use based on provider
        provider = self.embedding_service.provider
        if provider == EmbeddingModel.EmbeddingProvider.Gemini:
            store_model = GeminiTextEmbedding004Store
        elif provider == EmbeddingModel.EmbeddingProvider.Mistral:
            store_model = MistralEmbedStore
        else:
            raise ValueError(f"Unsupported embedding provider: {provider}")

        # Create and save the embedding store
        with transaction.atomic():
            # Delete any existing embeddings for this object and model
            store_model.objects.filter(
                content_type=content_type,
                object_id=content_object.id,
                embedding_model=self.embedding_service.embedding_model,
            ).delete()

            # Create new embedding
            embedding_store = store_model.objects.create(
                embedding_model=self.embedding_service.embedding_model,
                vector=embedding,
                content_type=content_type,
                object_id=content_object.id,
            )

            return embedding_store

    def store_embedding_for_word_definition(
        self, word_definition: WordDefinition, embedding: List[float]
    ) -> Union[GeminiTextEmbedding004Store, MistralEmbedStore]:
        """
        Store a pre-calculated embedding for a word definition.

        Args:
            word_definition: The WordDefinition instance
            embedding: The embedding to store

        Returns:
            The created embedding store instance
        """
        return self.store_embedding_for_object(
            word_definition, embedding, WordDefinition
        )

    def store_embedding_for_word_in_context(
        self, word_in_context: WordInContext, embedding: List[float]
    ) -> Union[GeminiTextEmbedding004Store, MistralEmbedStore]:
        """
        Store a pre-calculated embedding for a word in context.

        Args:
            word_in_context: The WordInContext instance
            embedding: The embedding to store

        Returns:
            The created embedding store instance
        """
        return self.store_embedding_for_object(
            word_in_context, embedding, WordInContext
        )

    def store_embeddings_batch(
        self, content_type_model, object_embeddings: Dict[int, List[float]]
    ) -> List[Union[GeminiTextEmbedding004Store, MistralEmbedStore]]:
        """
        Store pre-calculated embeddings for multiple objects in batch.
        This function assumes only new embeddings are passed (not already in the database).

        Args:
            content_type_model: The model class for the objects
            object_embeddings: Dictionary mapping object_id to embedding vector

        Returns:
            List of created embedding store instances
        """
        if not object_embeddings:
            return []

        content_type = get_content_type_for_model(content_type_model)

        # Determine which store model to use based on provider
        provider = self.embedding_service.provider
        if provider == EmbeddingModel.EmbeddingProvider.Gemini:
            store_model = GeminiTextEmbedding004Store
        elif provider == EmbeddingModel.EmbeddingProvider.Mistral:
            store_model = MistralEmbedStore
        else:
            raise ValueError(f"Unsupported embedding provider: {provider}")

        # Create and save the embedding stores
        with transaction.atomic():
            # We don't need to delete existing embeddings anymore since we only store new ones
            # that don't exist in the database yet

            # Prepare bulk create data
            embedding_stores = []
            for object_id, embedding in object_embeddings.items():
                embedding_stores.append(
                    store_model(
                        embedding_model=self.embedding_service.embedding_model,
                        vector=embedding,
                        content_type=content_type,
                        object_id=object_id,
                    )
                )

            # Bulk create the embedding stores
            created_stores = store_model.objects.bulk_create(embedding_stores)

            return created_stores

    # Combined methods (for backward compatibility)

    def calculate_and_store_embedding_for_word_definition(
        self, word: Word, word_def_idx: int
    ) -> List[float]:
        """
        Calculate and store an embedding for a word definition.
        If an embedding already exists, it will be reused and not re-calculated.

        Args:
            word: The Word instance
            word_def_idx: The index of the definition to embed

        Returns:
            The generated embedding

        Raises:
            ValueError: If definition index is out of range
        """
        # Calculate the embedding - this also checks for existing embeddings
        word_definition, embedding, is_new = (
            self.calculate_embedding_for_word_definition(
                word=word, word_def_idx=word_def_idx
            )
        )

        # Only store if this is a new embedding
        if is_new:
            try:
                self.store_embedding_for_word_definition(word_definition, embedding)
            except Exception as e:
                logger.error(f"Error storing embedding: {str(e)}")
                # Still return the embedding even if storing failed

        return embedding

    def calculate_and_store_embedding_for_word_in_context(
        self, word_in_context: WordInContext
    ) -> List[float]:
        """
        Calculate and store an embedding for a word in context.
        If an embedding already exists, it will be reused and not re-calculated.

        Args:
            word_in_context: The WordInContext instance

        Returns:
            The generated embedding
        """
        # Calculate the embedding - this also checks for existing embeddings
        embedding, is_new = self.calculate_embedding_for_word_in_context(
            word_in_context=word_in_context
        )

        # Only store if this is a new embedding
        if is_new:
            try:
                self.store_embedding_for_word_in_context(word_in_context, embedding)
            except Exception as e:
                logger.error(f"Error storing embedding: {str(e)}")
                # Still return the embedding even if storing failed

        return embedding
