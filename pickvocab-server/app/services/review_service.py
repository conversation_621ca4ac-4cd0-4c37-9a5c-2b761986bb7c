from typing import Dict, TypeVar
import random

from accounts.models import CustomUser
from ..models import Card, Deck

from django.db import models


T = TypeVar("T")


# pick n items from bucket and remove them
def pick_items_from_bucket(items: list[T], n: int) -> list[T]:
    picked = []
    for i in range(min(n, len(items))):
        picked.append(items.pop())

    return picked


def pick_items_from_buckets(bucket: Dict[int, list[T]], n: int) -> list[T]:
    sorted_bucket = [(i, bucket.copy()) for (i, bucket) in sorted(bucket.items(), key=lambda x: x[0])]

    total_items_in_bucket = 0
    for (_, items) in sorted_bucket:
        total_items_in_bucket += len(items)

    picked_items: list[T] = []
    number_of_items_each_turn = [5, 2, 1, 1, 1]
    current_bucket = 0
    turn = 0

    # Shuffle items in each bucket, then pop items from bucket each turn.
    for (_, items) in sorted_bucket:
        random.shuffle(items)

    while len(picked_items) < min(n, total_items_in_bucket):
        c = pick_items_from_bucket(
            sorted_bucket[current_bucket][1], number_of_items_each_turn[turn]
        )
        picked_items.extend(c)

        next_turn = (turn + 1) % len(number_of_items_each_turn)
        next_bucket = (current_bucket + 1) % len(sorted_bucket)

        number_of_items_each_turn[next_turn] += number_of_items_each_turn[turn] - len(c)
        number_of_items_each_turn[turn] = 0 # Reset to prevent incorrect accumulation during circular rotation.
        # print(f'turn: {turn}, #: {number_of_items_each_turn[turn]}, next #: {number_of_items_each_turn[next_turn]}')

        turn = next_turn
        current_bucket = next_bucket

    return picked_items


# number of cards: 10
#
# 5 scores
# 1     2     3      4     5
# 5     2     1      1     1
C = TypeVar('ModelType', bound=models.Model)
D = TypeVar('ModelType', bound=models.Model)

def pick_cards_to_review(user: CustomUser, deck_ids: list[int], CardModel: C, DeckModel: D, is_master=False, number_of_cards=10) -> list[C]:
    decks = []
    cards = []
    if not is_master:
        decks = [
            deck for deck in DeckModel.objects.filter(owner=user, pk__in=deck_ids).prefetch_related("cards")
        ]
        # deduplicated cards from decks
        card_map: dict[int, CardModel] = {}
        for deck in decks:
            for card in deck.cards.all():
                if card_map.get(card.id) is None:
                    card_map[card.id] = card

        cards = card_map.values()
    else:
        # TODO: optimize this
        cards = CardModel.objects.filter(owner=user)

    score_buckets: Dict[int, list[CardModel]] = {}
    for card in cards:
        if score_buckets.get(card.progress_score) is None:
            score_buckets[card.progress_score] = []
        score_buckets[card.progress_score].append(card)

    return pick_items_from_buckets(score_buckets, number_of_cards)
