"""
Card embedding service for calculating and storing embeddings for cards.
"""

from typing import List, Union, Tuple, Dict
from django.contrib.auth.models import User

from ..word_embedding_service import WordEmbeddingService
from ..embedding_service.base import BaseEmbeddingService
from ...models import (
    DefinitionCard,
    ContextCard,
    WordDefinition,
    Word,
    WordInContext,
    GenericCard,
)
from ...utils import get_service_logger
from .data_classes import CardEmbeddingResult
from .prefetching import (
    prefetch_related_objects,
    group_cards_by_type,
    combine_batch_results
)
from .batch_processing import (
    batch_calculate_context_cards,
    batch_calculate_definition_cards,
    batch_store_embeddings_from_maps
)
from .types import CardId, EmbeddingVector
from .similarity_search import similarity_search, CardSimilaritySearchResult

# Use the service logger utility
logger = get_service_logger()

class CardEmbeddingService:
    """
    Service for calculating embeddings for card-associated words.

    This service handles two types of cards:
    - DefinitionCard: paired with WordDefinition
    - ContextCard: paired with WordInContext
    """

    def __init__(self, embedding_service: BaseEmbeddingService):
        """
        Initialize the card embedding service.

        Args:
            embedding_service: The embedding service to use for generating embeddings
        """
        self.embedding_service = embedding_service
        self.word_embedding_service = WordEmbeddingService(embedding_service)

    # Pure calculation methods

    def calculate_embedding_for_definition_card(
        self,
        definition_card: DefinitionCard
    ) -> Tuple[WordDefinition, EmbeddingVector, bool]:
        """
        Calculate an embedding for a definition card without storing it.

        Args:
            definition_card: The DefinitionCard instance

        Returns:
            A tuple containing:
            - The WordDefinition instance used for the embedding
            - The generated embedding vector
            - Boolean indicating if this is a new embedding (True) or retrieved from storage (False)

        Raises:
            ValueError: If the card doesn't have a valid word reference or definition
        """
        # Get or create WordDefinition from the definition card
        if definition_card.word_ref:
            # If the card has a word reference, use that
            word = definition_card.word_ref
            word_def_idx = definition_card.word_def_idx
        else:
            # If not, create a new word
            word = Word.objects.create(word=definition_card.word, definitions=[definition_card.definition])
            definition_card.word_ref = word
            definition_card.save()
            word_def_idx = 0

        # Use word embedding service to calculate the embedding
        return self.word_embedding_service.calculate_embedding_for_word_definition(
            word=word,
            word_def_idx=word_def_idx
        )

    def calculate_embedding_for_context_card(
        self,
        context_card: ContextCard
    ) -> Tuple[EmbeddingVector, bool]:
        """
        Calculate an embedding for a context card without storing it.

        Args:
            context_card: The ContextCard instance

        Returns:
            A tuple containing:
            - The generated embedding
            - Boolean indicating if this is a new embedding (True) or retrieved from storage (False)

        Raises:
            ValueError: If the card doesn't have a valid word in context reference
        """
        # Check if the card has a valid word in context reference
        if not context_card.word_in_context:
            raise ValueError("Context card has no associated word in context")

        # Use word embedding service to calculate the embedding
        return self.word_embedding_service.calculate_embedding_for_word_in_context(
            word_in_context=context_card.word_in_context
        )

    def calculate_embedding_for_card(
        self,
        card: Union[GenericCard, DefinitionCard, ContextCard]
    ) -> Union[Tuple[WordDefinition, EmbeddingVector, bool], Tuple[EmbeddingVector, bool]]:
        """
        Calculate an embedding for a card without storing it.

        This method handles different card types:
        - GenericCard: Will extract the actual card using the card attribute
        - DefinitionCard: Will calculate embedding directly
        - ContextCard: Will calculate embedding directly

        Args:
            card: The card instance (GenericCard, DefinitionCard, or ContextCard)

        Returns:
            Union of possible return types from calculate_embedding methods:
            - For DefinitionCard: Tuple[WordDefinition, EmbeddingVector, bool]
            - For ContextCard: Tuple[EmbeddingVector, bool]

        Raises:
            ValueError: If the card type is not supported
        """
        # If it's a GenericCard, extract the actual card content
        if isinstance(card, GenericCard):
            actual_card = card.card
            if isinstance(actual_card, DefinitionCard):
                return self.calculate_embedding_for_definition_card(actual_card)
            elif isinstance(actual_card, ContextCard):
                return self.calculate_embedding_for_context_card(actual_card)
            else:
                raise ValueError(f"Unsupported card content type: {type(actual_card)}")
        # If it's a direct card instance
        elif isinstance(card, DefinitionCard):
            return self.calculate_embedding_for_definition_card(card)
        elif isinstance(card, ContextCard):
            return self.calculate_embedding_for_context_card(card)
        else:
            raise ValueError(f"Unsupported card type: {type(card)}")

    # Storage methods

    def store_embedding_for_definition_card(
        self,
        word_definition: WordDefinition,
        embedding: EmbeddingVector
    ):
        """
        Store a pre-calculated embedding for a definition card.

        Args:
            word_definition: The WordDefinition instance
            embedding: The embedding to store

        Returns:
            The created embedding store instance
        """
        return self.word_embedding_service.store_embedding_for_word_definition(
            word_definition=word_definition,
            embedding=embedding
        )

    def store_embedding_for_context_card(
        self,
        word_in_context: WordInContext,
        embedding: EmbeddingVector
    ):
        """
        Store a pre-calculated embedding for a context card.

        Args:
            word_in_context: The WordInContext instance
            embedding: The embedding to store

        Returns:
            The created embedding store instance
        """
        return self.word_embedding_service.store_embedding_for_word_in_context(
            word_in_context=word_in_context,
            embedding=embedding
        )

    # Combined methods (for backward compatibility)

    def calculate_and_store_embedding_for_definition_card(
        self,
        definition_card: DefinitionCard
    ) -> EmbeddingVector:
        """
        Calculate and store an embedding for a definition card.

        Args:
            definition_card: The DefinitionCard instance

        Returns:
            The generated embedding

        Raises:
            ValueError: If the card doesn't have a valid word reference or definition
        """
        word_definition, embedding, is_new = self.calculate_embedding_for_definition_card(definition_card)
        if is_new:
            self.store_embedding_for_definition_card(word_definition, embedding)
        return embedding

    def calculate_and_store_embedding_for_context_card(
        self,
        context_card: ContextCard
    ) -> EmbeddingVector:
        """
        Calculate and store an embedding for a context card.

        Args:
            context_card: The ContextCard instance

        Returns:
            The generated embedding

        Raises:
            ValueError: If the card doesn't have a valid word in context reference
        """
        embedding, is_new = self.calculate_embedding_for_context_card(context_card)
        if is_new:
            self.store_embedding_for_context_card(context_card.word_in_context, embedding)
        return embedding

    def calculate_and_store_embedding_for_card(
        self,
        card: Union[GenericCard, DefinitionCard, ContextCard]
    ) -> EmbeddingVector:
        """
        Calculate and store an embedding for a card.

        This method handles different card types:
        - GenericCard: Will extract the actual card using the card attribute
        - DefinitionCard: Will calculate embedding directly
        - ContextCard: Will calculate embedding directly

        Args:
            card: The card instance (GenericCard, DefinitionCard, or ContextCard)

        Returns:
            The generated embedding

        Raises:
            ValueError: If the card type is not supported
        """
        # If it's a GenericCard, extract the actual card content
        if isinstance(card, GenericCard):
            actual_card = card.card
            if isinstance(actual_card, DefinitionCard):
                return self.calculate_and_store_embedding_for_definition_card(actual_card)
            elif isinstance(actual_card, ContextCard):
                return self.calculate_and_store_embedding_for_context_card(actual_card)
            else:
                raise ValueError(f"Unsupported card content type: {type(actual_card)}")
        # If it's a direct card instance
        elif isinstance(card, DefinitionCard):
            return self.calculate_and_store_embedding_for_definition_card(card)
        elif isinstance(card, ContextCard):
            return self.calculate_and_store_embedding_for_context_card(card)
        else:
            raise ValueError(f"Unsupported card type: {type(card)}")

    def calculate_embeddings_for_multiple_cards(
        self,
        cards: List[Union[GenericCard, DefinitionCard, ContextCard]]
    ) -> CardEmbeddingResult:
        """
        Calculate embeddings for multiple cards without storing them.

        Args:
            cards: List of card instances (GenericCard, DefinitionCard, or ContextCard)

        Returns:
            CardEmbeddingResult containing error map and mappings to model objects
        """
        if not cards:
            return CardEmbeddingResult({}, {}, {})

        # First, prefetch related objects based on card types to avoid N+1 queries
        cards = prefetch_related_objects(cards)

        # Group cards by type
        grouped_cards = group_cards_by_type(cards)

        # Process each card type with the appropriate batch method
        definition_results = batch_calculate_definition_cards(
            grouped_cards.definition_cards,
            self.word_embedding_service
        )
        context_results = batch_calculate_context_cards(
            grouped_cards.context_cards,
            self.word_embedding_service
        )

        # Combine results and map back to original cards
        error_map, definition_map, context_map = combine_batch_results(
            cards, grouped_cards, definition_results, context_results
        )

        return CardEmbeddingResult(error_map, definition_map, context_map)

    def calculate_and_store_embeddings_for_multiple_cards(
        self,
        cards: List[Union[GenericCard, DefinitionCard, ContextCard]]
    ) -> Tuple[int, Dict[CardId, str], Dict[CardId, Tuple[WordDefinition, EmbeddingVector]], Dict[CardId, Tuple[WordInContext, EmbeddingVector]]]:
        """
        Calculate and store embeddings for multiple cards.

        Args:
            cards: List of card instances (GenericCard, DefinitionCard, or ContextCard)

        Returns:
            A tuple containing:
            - Count of successfully generated embeddings
            - Dictionary mapping card IDs to error reasons for failed cards
            - Dictionary mapping card IDs to (WordDefinition, embedding) tuples
            - Dictionary mapping card IDs to (WordInContext, embedding) tuples
        """
        if not cards:
            return 0, {}, {}, {}

        # First calculate the embeddings
        result = self.calculate_embeddings_for_multiple_cards(cards)

        # Then store the embeddings using the mappings directly
        batch_store_embeddings_from_maps(
            self.word_embedding_service,
            result.definition_map,
            result.context_map
        )

        # The number of successful embeddings is the total count of entries in both maps
        successful_count = len(result.definition_map) + len(result.context_map)
        return successful_count, result.error_map, result.definition_map, result.context_map

    def similarity_search(
        self,
        query: str,
        user: User,
        limit: int = 10,
        threshold: float = 0.0,
    ) -> List[CardSimilaritySearchResult]:
        """
        Perform a similarity search for generic cards owned by the user.

        This method searches for cards based on the similarity of their associated
        WordDefinition or WordInContext embeddings to the query text.

        Args:
            query: The query text to search for
            user: The user who owns the cards
            limit: Maximum number of results to return (default: 10)
            threshold: Minimum similarity score threshold (default: 0.0)

        Returns:
            List of CardSimilaritySearchResult dictionaries with generic_card and similarity_score,
            with all related objects prefetched for optimal performance
        """
        return similarity_search(
            query=query,
            user=user,
            embedding_service=self.embedding_service,
            limit=limit,
            threshold=threshold
        )
