"""
Type definitions for the card embedding service.
"""

from typing import NewType, List, Optional, Dict, Any
from app.utils.errors import AppError

# Define CardId as a distinct type from int
CardId = NewType("CardId", int)

# Define EmbeddingVector type for clarity
EmbeddingVector = List[float]
# Define additional types for improved type safety
CardIndex = NewType("CardIndex", int)  # Index of a card in a list/array
WordId = NewType("WordId", int)  # Database ID of a Word
WordDefIdx = NewType("WordDefIdx", int)  # Index of a definition within a word
ObjectId = NewType("ObjectId", int)  # Database ID of a model object (generic)
