"""
Similarity search functionality for card embeddings.

This module provides functions for searching cards based on semantic similarity
of their associated word embeddings.
"""

from typing import Dict, List, Tuple, Set, Any
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.db.models import Prefetch, Q, QuerySet

from ...models import (
    DefinitionCard,
    ContextCard,
    WordDefinition,
    WordInContext,
    GenericCard,
)
from ...utils import get_service_logger
from ..embedding_service.base import BaseEmbeddingService

# Use the service logger utility
logger = get_service_logger()

# Type aliases for better readability
ContentTypeId = int
ObjectId = int
WordId = int
WordDefIdx = int
WordInContextId = int
GenericCardKey = Tuple[ContentTypeId, ObjectId]
WordDefKey = Tuple[WordId, WordDefIdx]

# Type for similarity search results
class CardSimilaritySearchResult(dict):
    """
    Result type for similarity search results.
    
    Attributes:
        generic_card: The generic card matching the search
        similarity_score: The similarity score between the query and the card
    """
    generic_card: GenericCard
    similarity_score: float


def get_model_content_types() -> Dict[str, ContentType]:
    """
    Get content types for all models used in similarity search.
    
    Returns:
        Dictionary mapping model names to their ContentType objects
    """
    return {
        'word_definition': ContentType.objects.get_for_model(WordDefinition),
        'word_in_context': ContentType.objects.get_for_model(WordInContext),
        'definition_card': ContentType.objects.get_for_model(DefinitionCard),
        'context_card': ContentType.objects.get_for_model(ContextCard),
    }


def fetch_user_definition_cards(
    user: User, 
    definition_card_content_type: ContentType
) -> QuerySet:
    """
    Fetch definition cards owned by the user with prefetched generic cards.
    
    Args:
        user: The user who owns the cards
        definition_card_content_type: ContentType for DefinitionCard model
    
    Returns:
        QuerySet of DefinitionCard objects with prefetched generic cards
    """
    # Create a queryset that filters generic cards by owner and content type
    user_def_cards_qs = GenericCard.objects.filter(
        owner=user, 
        card_content_type=definition_card_content_type
    )
    
    # Fetch definition cards with their generic cards in a single query
    return DefinitionCard.objects.filter(
        generic_card__owner=user,
        generic_card__card_content_type=definition_card_content_type
    ).select_related('word_ref').prefetch_related(
        Prefetch('generic_card', queryset=user_def_cards_qs, to_attr='prefetched_generic_cards')
    )


def fetch_user_context_cards(
    user: User, 
    context_card_content_type: ContentType
) -> QuerySet:
    """
    Fetch context cards owned by the user with prefetched generic cards.
    
    Args:
        user: The user who owns the cards
        context_card_content_type: ContentType for ContextCard model
    
    Returns:
        QuerySet of ContextCard objects with prefetched generic cards
    """
    # Create a queryset that filters generic cards by owner and content type
    user_ctx_cards_qs = GenericCard.objects.filter(
        owner=user, 
        card_content_type=context_card_content_type
    )
    
    # Fetch context cards with their generic cards in a single query
    return ContextCard.objects.filter(
        generic_card__owner=user,
        generic_card__card_content_type=context_card_content_type
    ).select_related('word_in_context').prefetch_related(
        Prefetch('generic_card', queryset=user_ctx_cards_qs, to_attr='prefetched_generic_cards')
    )


def extract_word_definition_ids(
    definition_cards: QuerySet
) -> Tuple[List[int], Dict[WordDefKey, DefinitionCard]]:
    """
    Extract WordDefinition IDs from definition cards.
    
    Args:
        definition_cards: QuerySet of DefinitionCard objects
    
    Returns:
        Tuple containing:
        - List of WordDefinition IDs
        - Dictionary mapping (word_id, word_def_idx) pairs to DefinitionCard objects
    """
    # Extract unique pairs from definition cards
    unique_pairs: Set[Tuple[int, int]] = set(
        (dc.word_ref_id, dc.word_def_idx) for dc in definition_cards
    )
    
    # Create a lookup map for definition cards
    def_card_map = {(dc.word_ref_id, dc.word_def_idx): dc for dc in definition_cards}
    
    # Create a single filter with Q objects, but only once per unique pair
    if unique_pairs:
        word_def_filter = Q()
        for word_id, word_def_idx in unique_pairs:
            word_def_filter |= Q(word_id=word_id, word_def_idx=word_def_idx)
        
        # Execute the optimized query
        user_word_def_ids = list(WordDefinition.objects.filter(word_def_filter).values_list('id', flat=True))
    else:
        user_word_def_ids = []
    
    return user_word_def_ids, def_card_map


def build_card_mappings(
    definition_cards: QuerySet,
    context_cards: QuerySet,
) -> Tuple[Dict[WordDefKey, DefinitionCard], Dict[WordInContextId, ContextCard], Dict[GenericCardKey, GenericCard]]:
    """
    Build in-memory mappings for lookups.
    
    Args:
        definition_cards: QuerySet of DefinitionCard objects
        context_cards: QuerySet of ContextCard objects
    
    Returns:
        Tuple containing:
        - Dictionary mapping (word_id, word_def_idx) to DefinitionCard
        - Dictionary mapping word_in_context_id to ContextCard
        - Dictionary mapping (content_type_id, object_id) to GenericCard
    """
    # Create mappings for lookups - these are in-memory operations
    def_card_map = {(dc.word_ref_id, dc.word_def_idx): dc for dc in definition_cards}
    ctx_card_map = {cc.word_in_context_id: cc for cc in context_cards}
    
    # Create maps from definition/context card IDs to their prefetched generic cards
    generic_card_map = {}
    
    # Process definition cards
    for dc in definition_cards:
        if hasattr(dc, 'prefetched_generic_cards'):
            for gc in dc.prefetched_generic_cards:
                key = (gc.card_content_type_id, gc.card_object_id)
                generic_card_map[key] = gc
    
    # Process context cards
    for cc in context_cards:
        if hasattr(cc, 'prefetched_generic_cards'):
            for gc in cc.prefetched_generic_cards:
                key = (gc.card_content_type_id, gc.card_object_id)
                generic_card_map[key] = gc
                
    return def_card_map, ctx_card_map, generic_card_map


def process_embedding_results(
    embedding_results: List[Dict[str, Any]],
    def_card_map: Dict[WordDefKey, DefinitionCard],
    ctx_card_map: Dict[WordInContextId, ContextCard],
    generic_card_map: Dict[GenericCardKey, GenericCard],
    content_types: Dict[str, ContentType]
) -> List[CardSimilaritySearchResult]:
    """
    Process embedding similarity results into card similarity results.
    
    Args:
        embedding_results: Results from the embedding service similarity search
        def_card_map: Dictionary mapping word definition keys to definition cards
        ctx_card_map: Dictionary mapping word in context IDs to context cards
        generic_card_map: Dictionary mapping content type and object ID to generic cards
        content_types: Dictionary of ContentType objects
        
    Returns:
        List of CardSimilaritySearchResult dictionaries
    """
    results = []
    
    # Get content types
    word_def_content_type = content_types['word_definition']
    word_in_context_content_type = content_types['word_in_context']
    definition_card_content_type = content_types['definition_card']
    context_card_content_type = content_types['context_card']
    
    # Process WordDefinition embeddings
    word_def_results = [r for r in embedding_results if r['content_type'] == word_def_content_type]
    for result in word_def_results:
        word_def = result['content_object']
        key = (word_def.word_id, word_def.word_def_idx)
        
        if key in def_card_map:
            def_card = def_card_map[key]
            gc_key = (definition_card_content_type.id, def_card.id)
            
            if gc_key in generic_card_map:
                gc = generic_card_map[gc_key]
                gc.card = def_card
                results.append({
                    'generic_card': gc,
                    'similarity_score': result['similarity_score']
                })

    # Process WordInContext embeddings
    word_in_context_results = [r for r in embedding_results if r['content_type'] == word_in_context_content_type]
    for result in word_in_context_results:
        word_in_context_id = result['object_id']
        
        if word_in_context_id in ctx_card_map:
            ctx_card = ctx_card_map[word_in_context_id]
            gc_key = (context_card_content_type.id, ctx_card.id)
            
            if gc_key in generic_card_map:
                gc = generic_card_map[gc_key]
                gc.card = ctx_card
                results.append({
                    'generic_card': gc,
                    'similarity_score': result['similarity_score']
                })
                
    return results


def similarity_search(
    query: str,
    user: User,
    embedding_service: BaseEmbeddingService,
    limit: int = 10,
    threshold: float = 0.0,
) -> List[CardSimilaritySearchResult]:
    """
    Perform a similarity search for generic cards owned by the user.

    This method searches for cards based on the similarity of their associated
    WordDefinition or WordInContext embeddings to the query text.

    Args:
        query: The query text to search for
        user: The user who owns the cards
        embedding_service: Service for performing embedding similarity search
        limit: Maximum number of results to return (default: 10)
        threshold: Minimum similarity score threshold (default: 0.0)

    Returns:
        List of CardSimilaritySearchResult dictionaries with generic_card and similarity_score,
        with all related objects prefetched for optimal performance
    """
    if not query.strip():
        return []

    try:
        # Get content types for our models
        content_types = get_model_content_types()
        
        # Fetch user's definition and context cards
        definition_cards = fetch_user_definition_cards(
            user, 
            content_types['definition_card']
        )
        
        context_cards = fetch_user_context_cards(
            user, 
            content_types['context_card']
        )
        
        # Extract WordDefinition IDs and create definition card map
        user_word_def_ids, def_card_map = extract_word_definition_ids(definition_cards)
        
        # Get WordInContext IDs directly from context cards
        user_word_in_context_ids = list(context_cards.values_list('word_in_context_id', flat=True))
        
        # Create filters dictionary for embedding search
        filters = {
            'object_id__in': user_word_def_ids + user_word_in_context_ids
        }
        
        # Use the base similarity_search for vector similarity
        embedding_results = embedding_service.similarity_search(
            query=query,
            content_type_models=[WordDefinition, WordInContext],
            limit=limit,
            threshold=threshold,
            filters=filters,
        )
        
        # Build all necessary mappings
        def_card_map, ctx_card_map, generic_card_map = build_card_mappings(
            definition_cards, 
            context_cards, 
        )
        
        # Process results into the final format
        results = process_embedding_results(
            embedding_results,
            def_card_map,
            ctx_card_map,
            generic_card_map,
            content_types
        )

        # Sort results by similarity score (highest first) and limit
        results.sort(key=lambda x: x['similarity_score'], reverse=True)
        return results[:limit]
    except Exception as e:
        logger.error(f"Error in similarity_search: {str(e)}")
        return [] 