"""
Factory functions for creating card embedding services.
"""

from typing import Optional

from ..embedding_service.factory import get_embedding_service
from .service import CardEmbeddingService


def get_card_embedding_service(
    provider: Optional[str] = None,
    model_name: Optional[str] = None,
    embedding_model_id: Optional[int] = None,
) -> CardEmbeddingService:
    """
    Factory function to get a card embedding service with the appropriate embedding service.

    Args:
        provider: Optional provider name (Gemini or Mistral)
        model_name: Optional model name
        embedding_model_id: Optional embedding model ID from the database

    Returns:
        An instance of CardEmbeddingService with the appropriate embedding service
    """
    # Get the appropriate embedding service using the factory function
    embedding_service = get_embedding_service(
        provider=provider, model_name=model_name, embedding_model_id=embedding_model_id
    )

    # Create and return the card embedding service
    return CardEmbeddingService(embedding_service)
