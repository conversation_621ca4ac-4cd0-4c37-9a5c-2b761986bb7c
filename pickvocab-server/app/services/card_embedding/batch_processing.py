"""
Batch processing utilities for card embedding service.

This module contains methods for processing cards in batches to optimize performance.
"""

from typing import List, Dict, Tuple, Optional, Callable, Set, Type, TypeVar, Any, Union
from slugify import slugify

from django.db import transaction, models

from ...models import (
    Word,
    WordDefinition,
    WordInContext,
    DefinitionCard,
    ContextCard,
    GenericCard,
)
from ..word_embedding_service import WordEmbeddingService
from ..embedding_service.base import BaseEmbeddingService
from .data_classes import BatchProcessResult, ObjectBatchMapping, T
from .types import CardId, EmbeddingVector, CardIndex, WordId, WordDefIdx, ObjectId
from app.utils.errors import AppError
from app.utils import get_service_logger

logger = get_service_logger()

# Use type variable T imported from data_classes.py
ModelClass = Type[T]

# Define types for word embedding service methods
GetEmbeddingTextFunc = Callable[[T], str]
BatchStoreFunc = Callable[[Dict[CardId, EmbeddingVector]], None]


def batch_calculate_cards_generic(
    cards: List[Tuple[GenericCard, Union[DefinitionCard, ContextCard], CardIndex]],
    preparation_func: Callable[
        [List[Union[DefinitionCard, ContextCard]]],
        Tuple[List[Optional[T]], Dict[CardIndex, AppError]],
    ],
    model_class: ModelClass,
    get_embedding_text_func: GetEmbeddingTextFunc,
    word_embedding_service: WordEmbeddingService,
) -> BatchProcessResult:
    """
    Generic function to calculate embeddings for cards in batch without storing them.

    This function serves as a common implementation for batch processing different card types.
    It handles the shared logic between definition cards and context cards processing,
    reducing code duplication while maintaining the same behavior.

    The function follows these steps:
    1. Extract specific card types from input tuples
    2. Use the provided preparation function to get model objects
    3. Process the model objects in batch using batch_calculate_objects
    4. Map errors from both preparation and processing steps
    5. Return embeddings and error information

    Args:
        cards: List of (original_card, typed_card, original_index) tuples
        preparation_func: Function to prepare model objects from cards
        model_class: The model class for the objects (WordDefinition or WordInContext)
        get_embedding_text_func: Function to get embedding text for a model object
        word_embedding_service: The word embedding service to use

    Returns:
        BatchProcessResult with embeddings, error map, and existing embeddings
    """
    if not cards:
        return BatchProcessResult([], {})

    # Extract just the typed cards (DefinitionCard or ContextCard)
    typed_cards = [card[1] for card in cards]

    # Prepare model objects and track errors
    model_objects, preparation_errors = preparation_func(typed_cards)
    # model_objects is a list of WordDefinition or WordInContext instances
    # Example for WordDefinition:
    #   model_objects = [WordDefinition(id=1, word_id=1, word_def_idx=0),
    #                    WordDefinition(id=2, word_id=1, word_def_idx=1),
    #                    None,  # Represents a failed preparation
    #                    WordDefinition(id=3, word_id=2, word_def_idx=0)]
    # preparation_errors would contain any errors encountered during preparation

    # Process model objects in batch
    embeddings_result = batch_calculate_objects(
        objects=model_objects,
        model_class=model_class,
        get_embedding_text_func=get_embedding_text_func,
        embedding_service=word_embedding_service.embedding_service,
        word_embedding_service=word_embedding_service,
    )

    # Map errors from preparation and processing
    combined_error_map: Dict[int, AppError] = {}
    for i, error in preparation_errors.items():
        combined_error_map[i] = error

    for i, obj in enumerate(model_objects):
        if obj is None:
            # Already tracked in preparation_errors
            continue
        if i in embeddings_result.error_map:
            combined_error_map[i] = embeddings_result.error_map[i]

    # Make sure to include existing_embeddings in the result
    return BatchProcessResult(
        embeddings_result.embeddings,
        combined_error_map,
        embeddings_result.existing_embeddings,
    )


def batch_calculate_definition_cards(
    definition_cards: List[Tuple[GenericCard, DefinitionCard, CardIndex]],
    word_embedding_service: WordEmbeddingService,
) -> BatchProcessResult:
    """
    Calculate embeddings for definition cards in batch without storing them.

    Args:
        definition_cards: List of (original_card, definition_card, original_index) tuples
        word_embedding_service: The word embedding service to use

    Returns:
        BatchProcessResult with embeddings and error map
    """
    return batch_calculate_cards_generic(
        cards=definition_cards,
        preparation_func=prepare_word_definitions,
        model_class=WordDefinition,
        get_embedding_text_func=word_embedding_service.get_embedding_text_for_word_definition,
        word_embedding_service=word_embedding_service,
    )


def prepare_word_definitions(
    definition_cards: List[DefinitionCard],
) -> Tuple[List[Optional[WordDefinition]], Dict[CardIndex, AppError]]:
    """
    Prepare WordDefinition objects for a list of definition cards.

    This function processes cards in two ways:
    1. For cards with word_ref: Uses the reference to get or create WordDefinition objects
    2. For cards without word_ref: Always creates a new Word object with the card's definition
       and sets word_def_idx=0, then creates a corresponding WordDefinition

    Args:
        definition_cards: List of DefinitionCard instances

    Returns:
        Tuple of (word_definitions, error_map)
    """
    word_definitions: List[Optional[WordDefinition]] = [None] * len(definition_cards)
    error_map: Dict[CardIndex, AppError] = {}

    # Step 1: Group cards by whether they have word_ref
    word_ref_groups, cards_without_word_ref = group_definition_cards_by_word_ref(
        definition_cards, error_map
    )

    # Step 2: Process cards with word_ref
    if word_ref_groups:
        process_cards_with_word_ref(word_ref_groups, word_definitions, error_map)

    # Step 3: Process cards without word_ref (create words if needed)
    if cards_without_word_ref:
        process_cards_without_word_ref(
            cards_without_word_ref, word_definitions, error_map
        )

    # Filter out None values
    return [wd for wd in word_definitions if wd is not None], error_map


def group_definition_cards_by_word_ref(
    definition_cards: List[DefinitionCard],
    error_map: Dict[CardIndex, AppError],
) -> Tuple[
    Dict[Tuple[WordId, WordDefIdx], List[Tuple[CardIndex, DefinitionCard]]],
    List[Tuple[CardIndex, DefinitionCard]],
]:
    """
    Group definition cards by whether they have a word_ref or not.
    Cards without word_ref will have a new Word created.

    Args:
        definition_cards: List of DefinitionCard instances
        error_map: Dictionary to track errors

    Returns:
        Tuple containing:
        - Dictionary mapping (word_id, word_def_idx) to list of (card_index, card) tuples
        - List of (card_index, card) tuples for cards without word_ref
    """
    cards_without_word_ref: List[Tuple[CardIndex, DefinitionCard]] = []
    word_ref_groups: Dict[
        Tuple[WordId, WordDefIdx], List[Tuple[CardIndex, DefinitionCard]]
    ] = {}  # {(word_id, word_def_idx): [(card_index, card), ...]}

    for i, card in enumerate(definition_cards):
        try:
            if card.word_ref:
                # Group cards by both word_ref and word_def_idx for batch processing
                word_id = WordId(card.word_ref.id)
                word_def_idx = WordDefIdx(card.word_def_idx)
                key = (word_id, word_def_idx)
                if key not in word_ref_groups:
                    word_ref_groups[key] = []
                word_ref_groups[key].append((CardIndex(i), card))
            else:
                # Card needs new word creation
                cards_without_word_ref.append((CardIndex(i), card))
        except Exception as e:
            # Create ErrorMessage directly from the exception
            error = AppError.from_exception(
                e, prefix="Error preparing definition card"
            )
            logger.error(
                f"{error.get_detailed_message()} for card ID: {getattr(card, 'id', 'unknown')}"
            )
            error_map[CardIndex(i)] = error

    return word_ref_groups, cards_without_word_ref


def process_cards_with_word_ref(
    word_ref_groups: Dict[Tuple[WordId, WordDefIdx], List[Tuple[CardIndex, DefinitionCard]]],
    word_definitions: List[Optional[WordDefinition]],
    error_map: Dict[CardIndex, AppError],
) -> None:
    """
    Process cards that have word_ref, fetching or creating WordDefinition objects.

    Args:
        word_ref_groups: Dictionary mapping (word_id, word_def_idx) to list of (card_index, card) tuples
        word_definitions: List to populate with WordDefinition objects
        error_map: Dictionary to track errors
    """
    # Get existing WordDefinition objects
    existing_word_defs = fetch_existing_word_definitions(word_ref_groups)

    # Create missing WordDefinition objects
    create_missing_word_definitions(word_ref_groups, existing_word_defs)

    # Assign WordDefinition objects to cards
    assign_word_definitions_to_cards(
        word_ref_groups, existing_word_defs, word_definitions, error_map
    )


def fetch_existing_word_definitions(
    word_ref_groups: Dict[Tuple[WordId, WordDefIdx], List[Tuple[CardIndex, DefinitionCard]]],
) -> Dict[Tuple[WordId, WordDefIdx], WordDefinition]:
    """
    Fetch existing WordDefinition objects for the given word_ref groups.

    Args:
        word_ref_groups: Dictionary mapping (word_id, word_def_idx) to list of (card_index, card) tuples

    Returns:
        Dictionary mapping (word_id, word_def_idx) to WordDefinition object
    """
    existing_word_defs: Dict[Tuple[WordId, WordDefIdx], WordDefinition] = {}
    word_definition_filters: List[models.Q] = []

    # Build query filters - now we can directly use the keys
    for (word_id, word_def_idx) in word_ref_groups.keys():
        word_definition_filters.append(
            models.Q(word_id=word_id, word_def_idx=word_def_idx)
        )

    if word_definition_filters:
        # Combine filters with OR
        query = word_definition_filters.pop(0)
        for filter_q in word_definition_filters:
            query |= filter_q

        # Execute query and build mapping
        for wd in WordDefinition.objects.filter(query):
            existing_word_defs[(WordId(wd.word_id), WordDefIdx(wd.word_def_idx))] = wd

    return existing_word_defs


def create_missing_word_definitions(
    word_ref_groups: Dict[Tuple[WordId, WordDefIdx], List[Tuple[CardIndex, DefinitionCard]]],
    existing_word_defs: Dict[Tuple[WordId, WordDefIdx], WordDefinition],
) -> None:
    """
    Create WordDefinition objects that don't exist yet.

    Args:
        word_ref_groups: Dictionary mapping (word_id, word_def_idx) to list of (card_index, card) tuples
        existing_word_defs: Dictionary mapping (word_id, word_def_idx) to WordDefinition object
    """
    to_create: List[WordDefinition] = []
    create_mapping: Dict[
        Tuple[WordId, WordDefIdx], int
    ] = {}  # Maps (word_id, word_def_idx) to index in to_create list

    # Identify WordDefinitions that need to be created
    for key in word_ref_groups.keys():
        if key not in existing_word_defs:
            # Need to create this WordDefinition
            word_id, word_def_idx = key
            word_def = WordDefinition(word_id=word_id, word_def_idx=word_def_idx)
            create_mapping[key] = len(to_create)
            to_create.append(word_def)

    # Bulk create the new WordDefinitions
    if to_create:
        try:
            with transaction.atomic():
                created = WordDefinition.objects.bulk_create(to_create)

                # Update our existing_word_defs with newly created objects
                for key, idx in create_mapping.items():
                    existing_word_defs[key] = created[idx]
        except Exception as e:
            error = AppError.from_exception(
                e, prefix="Error bulk creating WordDefinitions"
            )
            logger.error(error.get_detailed_message())
            # Fall back to individual creation for any that failed
            for key, idx in create_mapping.items():
                word_id, word_def_idx = key
                try:
                    wd, _ = WordDefinition.objects.get_or_create(
                        word_id=word_id, word_def_idx=word_def_idx
                    )
                    existing_word_defs[key] = wd
                except Exception as inner_e:
                    inner_error = AppError.from_exception(
                        inner_e, prefix=f"Error creating WordDefinition {key}"
                    )
                    logger.error(inner_error.get_detailed_message())


def assign_word_definitions_to_cards(
    word_ref_groups: Dict[Tuple[WordId, WordDefIdx], List[Tuple[CardIndex, DefinitionCard]]],
    existing_word_defs: Dict[Tuple[WordId, WordDefIdx], WordDefinition],
    word_definitions: List[Optional[WordDefinition]],
    error_map: Dict[CardIndex, AppError],
) -> None:
    """
    Assign WordDefinition objects to their corresponding cards.

    Args:
        word_ref_groups: Dictionary mapping (word_id, word_def_idx) to list of (card_index, card) tuples
        existing_word_defs: Dictionary mapping (word_id, word_def_idx) to WordDefinition object
        word_definitions: List to populate with WordDefinition objects
        error_map: Dictionary to track errors
    """
    for key, card_indices in word_ref_groups.items():
        word_id, word_def_idx = key
        if key in existing_word_defs:
            word_def = existing_word_defs[key]
            for card_idx, card in card_indices:
                word_definitions[card_idx] = word_def
                # Directly assign the WordDefinition to the DefinitionCard
                card._word_definition = word_def
        else:
            error = AppError(
                f"Failed to get or create word definition for word_id={word_id}, def_idx={word_def_idx}"
            )
            logger.error(error.get_detailed_message())
            for card_idx, _ in card_indices:
                error_map[CardIndex(card_idx)] = error


def process_cards_without_word_ref(
    cards_without_word_ref: List[Tuple[CardIndex, DefinitionCard]],
    word_definitions: List[Optional[WordDefinition]],
    error_map: Dict[CardIndex, AppError],
) -> None:
    """
    Process cards without word_ref, creating Word and WordDefinition objects.
    For each card without word_ref, a new Word will be created with word_def_idx=0.

    Args:
        cards_without_word_ref: List of (card_index, card) tuples for cards without word_ref
        word_definitions: List to populate with WordDefinition objects
        error_map: Dictionary to track errors
    """
    if not cards_without_word_ref:
        return

    # Prepare Word objects for bulk creation
    words_to_create, card_map = prepare_words_for_bulk_creation(
        cards_without_word_ref, error_map
    )

    # Create Words and WordDefinitions in bulk and assign to cards
    if words_to_create:
        create_and_assign_bulk_objects(
            words_to_create, card_map, word_definitions, error_map
        )


def prepare_words_for_bulk_creation(
    cards_without_word_ref: List[Tuple[CardIndex, DefinitionCard]],
    error_map: Dict[CardIndex, AppError],
) -> Tuple[List[Word], Dict[int, Tuple[CardIndex, DefinitionCard]]]:
    """
    Prepare Word objects for bulk creation.
    
    Args:
        cards_without_word_ref: List of cards without word references
        error_map: Dictionary to track errors
        
    Returns:
        Tuple of (words_to_create, card_map)
    """
    words_to_create = []
    card_map = {}  # Maps index in words_to_create to (card_index, card) tuple

    for idx, (card_index, card) in enumerate(cards_without_word_ref):
        try:
            # Create a Word object with the card's definition if available
            definitions = []
            if hasattr(card, "definition") and card.definition:
                definitions = [card.definition]

            # Prepare the Word object
            word = Word(
                word=card.word,
                definitions=definitions,
                slug=slugify(card.word),
            )
            words_to_create.append(word)
            card_map[idx] = (card_index, card)
        except Exception as e:
            error = AppError.from_exception(
                e, prefix=f"Error preparing Word for '{card.word}'"
            )
            logger.error(
                f"{error.get_detailed_message()} for card ID: {getattr(card, 'id', 'unknown')}"
            )
            error_map[CardIndex(card_index)] = error
            
    return words_to_create, card_map


def create_and_assign_bulk_objects(
    words_to_create: List[Word],
    card_map: Dict[int, Tuple[CardIndex, DefinitionCard]],
    word_definitions: List[Optional[WordDefinition]],
    error_map: Dict[CardIndex, AppError],
) -> None:
    """
    Create Words and WordDefinitions in bulk and assign them to cards.
    
    Args:
        words_to_create: List of Word objects to create
        card_map: Mapping from index to (card_index, card) tuple
        word_definitions: List to populate with WordDefinition objects
        error_map: Dictionary to track errors
    """
    try:
        with transaction.atomic():
            # Bulk create all Word objects
            created_words = Word.objects.bulk_create(words_to_create)
            logger.info(f"Bulk created {len(created_words)} Word objects")

            # Prepare and create WordDefinition objects
            created_word_defs = create_word_definitions_bulk(created_words)
            
            # Assign results to cards
            assign_bulk_created_word_definitions(
                created_word_defs, card_map, word_definitions
            )
    except Exception as e:
        error = AppError.from_exception(
            e, prefix="Error during bulk creation of Words and WordDefinitions"
        )
        logger.error(error.get_detailed_message())
        
        # Fall back to individual creation
        handle_bulk_creation_failure(
            words_to_create, card_map, word_definitions, error_map
        )


def create_word_definitions_bulk(
    words: List[Word]
) -> List[WordDefinition]:
    """
    Create WordDefinition objects in bulk.
    
    Args:
        words: List of Word objects
        
    Returns:
        List of created WordDefinition objects
    """
    # Prepare WordDefinition objects
    word_defs_to_create = []
    for word in words:
        word_def = WordDefinition(
            word=word,
            word_def_idx=0  # Default to 0 for new words
        )
        word_defs_to_create.append(word_def)

    # Bulk create WordDefinition objects
    created_word_defs = WordDefinition.objects.bulk_create(word_defs_to_create)
    logger.info(f"Bulk created {len(created_word_defs)} WordDefinition objects")
    
    return created_word_defs


def assign_bulk_created_word_definitions(
    word_defs: List[WordDefinition],
    card_map: Dict[int, Tuple[CardIndex, DefinitionCard]],
    word_definitions: List[Optional[WordDefinition]],
) -> None:
    """
    Assign bulk-created WordDefinition objects to cards and result list.
    
    Args:
        word_defs: List of WordDefinition objects
        card_map: Mapping from index to (card_index, card) tuple
        word_definitions: List to populate with WordDefinition objects
    """
    for idx, word_def in enumerate(word_defs):
        card_index, card = card_map[idx]
        word_definitions[card_index] = word_def
        # Directly assign the WordDefinition to the DefinitionCard
        card._word_definition = word_def
        logger.info(
            f"Created new WordDefinition for word '{card.word}' with def_idx=0"
        )


def handle_bulk_creation_failure(
    words_to_create: List[Word],
    card_map: Dict[int, Tuple[CardIndex, DefinitionCard]],
    word_definitions: List[Optional[WordDefinition]],
    error_map: Dict[CardIndex, AppError],
) -> None:
    """
    Handle fallback to individual creation when bulk creation fails.
    
    Args:
        words_to_create: List of Word objects that failed to create in bulk
        card_map: Mapping from index to (card_index, card) tuple
        word_definitions: List to populate with WordDefinition objects
        error_map: Dictionary to track errors
    """
    for idx in range(len(words_to_create)):
        card_index, card = card_map[idx]
        try:
            process_single_card_without_word_ref(card_index, card, word_definitions)
        except Exception as inner_e:
            inner_error = AppError.from_exception(
                inner_e, prefix=f"Error processing card with word '{card.word}'"
            )
            logger.error(
                f"{inner_error.get_detailed_message()} for card ID: {getattr(card, 'id', 'unknown')}"
            )
            error_map[CardIndex(card_index)] = inner_error


def process_single_card_without_word_ref(
    card_index: CardIndex,
    card: DefinitionCard,
    word_definitions: List[Optional[WordDefinition]],
) -> None:
    """
    Process a single card without word_ref, creating Word and WordDefinition objects.
    This is a fallback method when bulk creation fails.

    Args:
        card_index: Index of the card in the original list
        card: The DefinitionCard to process
        word_definitions: List to populate with WordDefinition objects
    """
    word_def_idx = 0  # Default to 0 for new words
    
    # Create a Word object with the card's definition if available
    definitions = []
    if hasattr(card, "definition") and card.definition:
        definitions = [card.definition]

    # Create the Word object with the word and definition
    word = Word(
        word=card.word,
        definitions=definitions,
        slug=slugify(card.word),
    )
    word.save()
    logger.info(
        f"Created new Word '{card.word}' for card ID: {getattr(card, 'id', 'unknown')}"
    )

    # Create WordDefinition
    word_definition = WordDefinition(word=word, word_def_idx=word_def_idx)
    word_definition.save()
    word_definitions[card_index] = word_definition
    # Directly assign the WordDefinition to the DefinitionCard
    card._word_definition = word_definition
    logger.info(
        f"Created new WordDefinition for word '{card.word}' with def_idx={word_def_idx}"
    )


def prepare_word_in_contexts(
    context_cards: List[ContextCard],
) -> Tuple[List[Optional[WordInContext]], Dict[CardIndex, AppError]]:
    """
    Prepare WordInContext objects for a list of context cards.

    Args:
        context_cards: List of ContextCard instances

    Returns:
        Tuple of (word_in_contexts, error_map)
    """
    word_in_contexts: List[Optional[WordInContext]] = []
    error_map: Dict[CardIndex, AppError] = {}

    for i, card in enumerate(context_cards):
        try:
            if not card.word_in_context:
                error = AppError("Context card has no associated word in context")
                logger.error(
                    f"{error.get_detailed_message()} for card ID: {getattr(card, 'id', 'unknown')}"
                )
                error_map[CardIndex(i)] = error
                word_in_contexts.append(None)
                continue

            word_in_contexts.append(card.word_in_context)
        except Exception as e:
            error = AppError.from_exception(
                e, prefix="Error preparing context card"
            )
            logger.error(
                f"{error.get_detailed_message()} for card ID: {getattr(card, 'id', 'unknown')}"
            )
            error_map[CardIndex(i)] = error
            word_in_contexts.append(None)

    return word_in_contexts, error_map


def batch_calculate_context_cards(
    context_cards: List[Tuple[GenericCard, ContextCard, CardIndex]],
    word_embedding_service: WordEmbeddingService,
) -> BatchProcessResult:
    """
    Calculate embeddings for context cards in batch without storing them.

    Args:
        context_cards: List of (original_card, context_card, original_index) tuples
        word_embedding_service: The word embedding service to use

    Returns:
        BatchProcessResult with embeddings and error map
    """
    return batch_calculate_cards_generic(
        cards=context_cards,
        preparation_func=prepare_word_in_contexts,
        model_class=WordInContext,
        get_embedding_text_func=word_embedding_service.get_embedding_text_for_word_in_context,
        word_embedding_service=word_embedding_service,
    )


def batch_calculate_objects(
    objects: List[Optional[T]],  # List of WordDefinition or WordInContext instances
    model_class: ModelClass,  # WordDefinition or WordInContext
    get_embedding_text_func: GetEmbeddingTextFunc,
    embedding_service: BaseEmbeddingService,
    word_embedding_service: WordEmbeddingService,
) -> BatchProcessResult:
    """
    Generic helper method to calculate embeddings for objects in batch without storing them.

    Args:
        objects: List of objects (may contain None values)
        model_class: The model class for the objects
        get_embedding_text_func: Function to get embedding text for an object
        embedding_service: The embedding service to use
        word_embedding_service: The word embedding service to use

    Returns:
        BatchProcessResult containing a list of embeddings and an error map
    """
    if not objects:
        return BatchProcessResult([], {})

    # Setup for batch processing
    object_mapping = prepare_objects_for_batch(objects)

    # Handle the case where all objects were invalid
    if not object_mapping.id_to_object:  # or needs_embedding
        # Return an array of None values with the same length as the input
        return BatchProcessResult([None] * len(objects), object_mapping.error_map)

    # Process valid objects
    batch_results = calculate_valid_objects_batch(
        object_mapping=object_mapping,
        model_class=model_class,
        get_embedding_text_func=get_embedding_text_func,
        embedding_service=embedding_service,
        word_embedding_service=word_embedding_service,
    )

    return batch_results


def calculate_valid_objects_batch(
    object_mapping: ObjectBatchMapping[T],
    model_class: ModelClass,
    get_embedding_text_func: GetEmbeddingTextFunc,
    embedding_service: BaseEmbeddingService,
    word_embedding_service: WordEmbeddingService,
) -> BatchProcessResult:
    """
    Process valid objects in batch, fetching existing embeddings and generating new ones without storing.

    Args:
        object_mapping: Result from prepare_objects_for_batch
        model_class: The model class for the objects
        get_embedding_text_func: Function to get embedding text for an object
        embedding_service: The embedding service to use
        word_embedding_service: The word embedding service to use

    Returns:
        BatchProcessResult containing a list of embeddings, an error map, and existing embeddings
    """
    # Get existing embeddings
    existing_embeddings = {}
    try:
        # Use the provided word_embedding_service directly
        used_word_embedding_service = word_embedding_service

        # Fetch existing embeddings
        existing_embeddings = fetch_existing_embeddings(
            model_class, object_mapping.needs_embedding, used_word_embedding_service,
        )

        # Apply existing embeddings to objects
        apply_embeddings_to_objects(object_mapping, existing_embeddings)

        # Filter out objects that already have embeddings
        objects_needing_embedding = filter_objects_needing_embedding(
            object_mapping, existing_embeddings
        )

        # Now process embeddings and texts only for objects that need them
        texts_to_embed, new_embeddings_info, prep_errors = prepare_texts_for_embedding(
            objects_needing_embedding, get_embedding_text_func
        )

        # Record any preparation errors
        if prep_errors:
            record_errors_in_object_mapping(object_mapping, prep_errors)

        # Generate new embeddings for texts that need them
        if texts_to_embed:
            try:
                generate_embeddings(
                    texts_to_embed,
                    new_embeddings_info,
                    object_mapping,
                    embedding_service,
                )
            except Exception as e:
                handle_embedding_generation_error(
                    e, new_embeddings_info, object_mapping
                )
    except Exception as e:
        # Pass the actual exception object to preserve stack trace
        handle_batch_error(
            e,  # Pass the exception itself, not str(e)
            "Error retrieving existing embeddings",
            object_mapping,
        )

    # Now object_mapping.result_embeddings should contain all embeddings (existing and new)
    # Make sure to include existing_embeddings in the result
    return BatchProcessResult(
        object_mapping.result_embeddings, object_mapping.error_map, existing_embeddings
    )


def fetch_existing_embeddings(
    model_class: ModelClass,
    object_ids: List[ObjectId],
    word_embedding_service: WordEmbeddingService,
) -> Dict[ObjectId, EmbeddingVector]:
    """
    Fetch existing embeddings for the objects in the mapping.

    Args:
        model_class: The model class (WordDefinition or WordInContext)
        object_ids: List of object IDs to fetch embeddings for
        word_embedding_service: The word embedding service instance

    Returns:
        Dictionary mapping object IDs to their embeddings
    """
    return word_embedding_service.get_existing_embeddings_batch(model_class, object_ids)


def filter_objects_needing_embedding(
    object_mapping: ObjectBatchMapping[T],
    existing_embeddings: Dict[ObjectId, EmbeddingVector],
) -> ObjectBatchMapping[T]:
    """
    Filters out objects that already have embeddings.
    Saves time by only processing objects that need new embeddings.

    Args:
        object_mapping: All the objects we're working with
        existing_embeddings: Embeddings we already have

    Returns:
        New mapping with only objects that need embeddings
    """
    # Create new mappings for objects that need embedding
    id_to_indices = {}
    id_to_object = {}
    needs_embedding = []

    # Filter out objects that already have embeddings
    for obj_id in object_mapping.needs_embedding:
        if obj_id not in existing_embeddings:
            id_to_indices[obj_id] = object_mapping.id_to_indices[obj_id]
            id_to_object[obj_id] = object_mapping.id_to_object[obj_id]
            needs_embedding.append(obj_id)

    # Create a new object mapping with only objects that need embedding
    return ObjectBatchMapping(
        id_to_indices=id_to_indices,
        id_to_object=id_to_object,
        needs_embedding=needs_embedding,
        error_map={},  # No errors yet
        result_embeddings=[None]
        * len(object_mapping.result_embeddings),  # Same length as original
    )


def prepare_texts_for_embedding(
    object_mapping: ObjectBatchMapping[T],
    get_embedding_text_func: GetEmbeddingTextFunc,
) -> Tuple[
    List[str], Dict[int, Tuple[ObjectId, List[int]]], Dict[int, AppError]
]:
    """
    Process objects and prepare texts for embedding generation.

    This function assumes that objects in the mapping all need embedding
    (objects with existing embeddings have been filtered out).

    Args:
        object_mapping: The batch mapping containing only objects that need embedding
        get_embedding_text_func: Function to get embedding text for an object

    Returns:
        Tuple containing:
        - List of texts to embed
        - Dictionary mapping text index to (object_id, indices) tuple
        - Dictionary of errors encountered during preparation
    """
    texts_to_embed: List[str] = []
    new_embeddings_info: Dict[int, Tuple[ObjectId, List[int]]] = {}
    errors: Dict[int, AppError] = {}

    for obj_id, indices in object_mapping.id_to_indices.items():
        # Generate embedding for this object
        try:
            obj = object_mapping.id_to_object[obj_id]
            text = get_embedding_text_func(obj)
            texts_to_embed.append(text)
            new_embeddings_info[len(texts_to_embed) - 1] = (obj_id, indices)
        except Exception as e:
            error = AppError.from_exception(
                e, prefix=f"Failed to generate embedding text for object {obj_id}"
            )
            for idx in indices:
                errors[idx] = error

    return texts_to_embed, new_embeddings_info, errors


def apply_embeddings_to_objects(
    object_mapping: ObjectBatchMapping[T], embeddings: Dict[ObjectId, EmbeddingVector]
) -> None:
    """
    Apply embeddings to objects in the batch mapping.

    This unified function applies embeddings to all corresponding indices
    in the result embeddings list for each object ID.

    Args:
        object_mapping: The batch mapping to update
        embeddings: Dictionary mapping object IDs to their embeddings
    """
    for obj_id, embedding in embeddings.items():
        if obj_id in object_mapping.id_to_indices:
            for idx in object_mapping.id_to_indices[obj_id]:
                object_mapping.result_embeddings[idx] = embedding


def record_errors_in_object_mapping(
    object_mapping: ObjectBatchMapping[T],
    errors: Dict[int, AppError],
) -> None:
    """
    Record errors in the object mapping and set corresponding embeddings to None.

    Args:
        object_mapping: The batch mapping to update
        errors: Dictionary of errors to record
    """
    for idx, error in errors.items():
        object_mapping.error_map[idx] = error
        object_mapping.result_embeddings[idx] = None


def generate_embeddings(
    texts: List[str],
    embedding_info: Dict[int, Tuple[ObjectId, List[int]]],
    object_mapping: ObjectBatchMapping[T],
    embedding_service: BaseEmbeddingService,
) -> None:
    """
    Generate embeddings for the given texts and update the object mapping.

    Args:
        texts: List of texts to generate embeddings for
        embedding_info: Dictionary mapping text index to (object_id, indices) tuple
        object_mapping: The batch mapping to update
        embedding_service: The embedding service to use
    """
    if not texts:
        return

    try:
        # Generate embeddings
        embeddings: List[EmbeddingVector] = embedding_service.generate_embeddings(texts)

        # Create a mapping of generated embeddings
        generated_embeddings = {}
        for i, embedding in enumerate(embeddings):
            obj_id, _ = embedding_info[i]  # We don't need the indices anymore
            generated_embeddings[obj_id] = embedding

        # Apply the generated embeddings
        apply_embeddings_to_objects(object_mapping, generated_embeddings)

    except Exception as e:
        # Handle failure for all texts
        handle_embedding_generation_error(e, embedding_info, object_mapping)


def handle_embedding_generation_error(
    error: Exception,
    embedding_info: Dict[int, Tuple[ObjectId, List[int]]],
    object_mapping: ObjectBatchMapping[T],
) -> None:
    """
    Handle errors that occur during embedding generation.

    Args:
        error: The exception that occurred
        embedding_info: Dictionary mapping text index to (object_id, indices) tuple
        object_mapping: The batch mapping to update
    """
    for _, (obj_id, indices) in embedding_info.items():
        handle_object_error(
            error, "Failed to generate embeddings", indices, object_mapping
        )


def prepare_objects_for_batch(objects: List[Optional[T]]) -> ObjectBatchMapping[T]:
    """
    Prepare objects for batch processing by filtering invalid ones and tracking duplicates.

    This function identifies and efficiently handles duplicate objects to avoid redundant processing:

    1. Duplicate tracking: Multiple cards may reference the same underlying object (e.g., WordDefinition
       or WordInContext). This function identifies these duplicates by their database ID and processes
       each unique object only once.

    2. Index mapping: While deduplicating objects, we maintain a mapping from each unique object ID to all
       the original indices where that object appears in the input list. This allows us to efficiently:
       - Calculate the embedding only once per unique object
       - Apply the resulting embedding to all instances of that object in the results
       - Properly handle errors by propagating them to all affected indices

    Example of duplicate tracking:
    Input objects: [WordDef(id=1), WordDef(id=2), WordDef(id=1), WordDef(id=3), WordDef(id=2)]

    This creates the following mappings:
    id_to_indices = {
        1: [0, 2],  # Object with ID 1 appears at indices 0 and 2
        2: [1, 4],  # Object with ID 2 appears at indices 1 and 4
        3: [3]      # Object with ID 3 appears only at index 3
    }

    needs_embedding = [1, 2, 3]  # Only unique object IDs that need embedding

    When embeddings are calculated, they're applied to all indices where the object appears:
    - Embedding for object ID 1 is applied to result indices 0 and 2
    - Embedding for object ID 2 is applied to result indices 1 and 4
    - Embedding for object ID 3 is applied to result index 3

    Args:
        objects: List of objects (may contain None values)

    Returns:
        ObjectBatchMapping with processing information
    """
    id_to_indices: Dict[ObjectId, List[int]] = {}  # Maps object ID to original indices
    id_to_object: Dict[ObjectId, T] = {}  # Maps object ID to object instance
    needs_embedding: List[ObjectId] = []  # List of unique object IDs after deduplication
    error_map: Dict[int, AppError] = {}  # Map of original indices to error messages

    # Create result array (same length as objects)
    result_embeddings: List[Optional[EmbeddingVector]] = [None] * len(objects)

    # Process each object
    for i, obj in enumerate(objects):
        if obj is None:
            error_map[i] = AppError("Invalid object: None value received")
            logger.error(f"Invalid object at index {i}: None value received")
            continue

        # Track object by ID for deduplication
        obj_id = ObjectId(obj.id)
        if obj_id not in id_to_object:
            id_to_object[obj_id] = obj
            id_to_indices[obj_id] = []
            needs_embedding.append(obj_id)

        # Track this index as another occurrence of the same object
        # This is crucial for handling duplicates - when we calculate the embedding once,
        # we'll apply it to all indices where this object appears
        id_to_indices[obj_id].append(i)

    return ObjectBatchMapping(
        id_to_indices=id_to_indices,
        id_to_object=id_to_object,
        needs_embedding=needs_embedding,
        error_map=error_map,
        result_embeddings=result_embeddings,
    )


def handle_batch_error(
    error: str | Exception, message: str, object_mapping: ObjectBatchMapping[T]
) -> None:
    """
    Handle an error that affects the entire batch.

    Args:
        error: The error message, exception string, or actual Exception object
        message: Context message explaining the error
        object_mapping: The object mapping to update with errors
    """
    # Create a unified error message, preserving exception if available
    if isinstance(error, Exception):
        error = AppError.from_exception(error, prefix=message)
    else:
        error = AppError(f"{message}: {error}")

    # Log the detailed error message with stack trace if available
    logger.error(error.get_detailed_message())

    # Mark all objects as failed
    for obj_id, indices in object_mapping.id_to_indices.items():
        for idx in indices:
            object_mapping.error_map[idx] = error
            object_mapping.result_embeddings[idx] = None


def handle_object_error(
    actual_error: str | Exception,
    message: str,
    indices: List[int],
    object_mapping: ObjectBatchMapping[T],
) -> None:
    """
    Handle an error that affects specific objects.

    Args:
        error: The error message, exception string, or actual Exception object
        message: Context message explaining the error
        indices: List of indices affected by the error
        object_mapping: The object mapping to update with errors
    """
    # Create a unified error message, preserving exception if available
    if isinstance(actual_error, Exception):
        error = AppError.from_exception(actual_error, prefix=message)
    else:
        error = AppError(f"{message}: {actual_error}")

    # Log the detailed error message with stack trace if available
    logger.error(error.get_detailed_message())

    # Mark affected objects as failed
    for idx in indices:
        object_mapping.error_map[idx] = error
        object_mapping.result_embeddings[idx] = None


def batch_store_embeddings_from_maps(
    word_embedding_service: WordEmbeddingService,
    definition_map: Dict[CardId, Tuple[WordDefinition, EmbeddingVector, bool]],
    context_map: Dict[CardId, Tuple[WordInContext, EmbeddingVector, bool]],
) -> None:
    """
    Store pre-calculated embeddings for cards using direct mappings.
    Only stores embeddings that are newly created (is_new=True).

    Args:
        word_embedding_service: The word embedding service to use for storage
        definition_map: Dictionary mapping card IDs to (WordDefinition, embedding, is_new) tuples
        context_map: Dictionary mapping card IDs to (WordInContext, embedding, is_new) tuples
    """
    # Extract word definition embeddings for batch storage (only new ones)
    word_def_embeddings: Dict[ObjectId, EmbeddingVector] = {}
    skipped_def_count = 0
    for _, (word_def, embedding, is_new) in definition_map.items():
        if is_new:
            word_def_embeddings[ObjectId(word_def.id)] = embedding
        else:
            skipped_def_count += 1

    # Extract word in context embeddings for batch storage (only new ones)
    word_in_context_embeddings: Dict[ObjectId, EmbeddingVector] = {}
    skipped_ctx_count = 0
    for _, (word_in_context, embedding, is_new) in context_map.items():
        if is_new:
            word_in_context_embeddings[ObjectId(word_in_context.id)] = embedding
        else:
            skipped_ctx_count += 1

    # Log how many embeddings were skipped
    if skipped_def_count > 0 or skipped_ctx_count > 0:
        logger.info(
            f"Skipped storing {skipped_def_count} existing definition embeddings and {skipped_ctx_count} existing context embeddings"
        )

    # Perform bulk storage operations
    try:
        if word_def_embeddings:
            word_embedding_service.store_embeddings_batch(
                WordDefinition, word_def_embeddings
            )

        if word_in_context_embeddings:
            word_embedding_service.store_embeddings_batch(
                WordInContext, word_in_context_embeddings
            )

    except Exception as e:
        # Create an ErrorMessage directly from the exception
        error = AppError.from_exception(
            e, prefix="Error during bulk storage of embeddings"
        )
        logger.error(error.get_detailed_message())
