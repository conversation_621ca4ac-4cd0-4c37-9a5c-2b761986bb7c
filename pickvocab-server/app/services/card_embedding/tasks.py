from celery import shared_task
from typing import Optional, Dict, Any

from django.contrib.auth import get_user_model
from ...models import GenericCard
from .factory import get_card_embedding_service
from ...utils.errors import AppError
from ...utils import get_service_logger
from ...serializers import GenericCardSerializer

User = get_user_model()

# Use the service logger utility
logger = get_service_logger()


@shared_task
def calculate_and_store_embedding_for_card(
    card_id: int,
    provider: Optional[str] = None,
    model_name: Optional[str] = None,
    embedding_model_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Celery task to calculate and store embedding for a single generic card.

    Args:
        card_id: ID of the generic card to process
        provider: Optional provider name (Gemini or Mistral)
        model_name: Optional model name
        embedding_model_id: Optional embedding model ID from the database

    Returns:
        Dictionary containing task result information:
        - success: Bo<PERSON>an indicating if operation was successful
        - card_id: ID of the processed card
        - error: Error message if any (only present if success is False)
        - embedding: The embedding vector (only present if success is True)
    """
    logger.info(f"Processing embedding for card ID: {card_id}")

    try:
        # Get the card
        card = GenericCard.objects.get(id=card_id)

        # Get embedding service with specified parameters
        card_embedding_service = get_card_embedding_service(
            provider=provider,
            model_name=model_name,
            embedding_model_id=embedding_model_id
        )

        # Calculate and store embedding
        embedding = card_embedding_service.calculate_and_store_embedding_for_card(card)

        logger.info(f"Successfully processed embedding for card ID: {card_id}")

        # Return success result
        return {
            "success": True,
            "card_id": card_id,
            "embedding": embedding.tolist() if hasattr(embedding, "tolist") else embedding
        }

    except GenericCard.DoesNotExist:
        error = AppError(
            message=f"Card with ID {card_id} not found",
            error_code="CARD_NOT_FOUND"
        )
        logger.error(str(error))
        return {
            "success": False,
            "card_id": card_id,
            "error": str(error),
            "error_details": error.to_dict()
        }
    except Exception as e:
        error = AppError.from_exception(
            e,
            prefix=f"Error processing card ID {card_id}",
            details={"card_id": card_id}
        )
        logger.error(error.get_detailed_message(), exc_info=True)
        return {
            "success": False,
            "card_id": card_id,
            "error": str(error),
            "error_details": error.to_dict()
        }


@shared_task
def similarity_search_task(
    query: str,
    user_id: int,
    limit: int = 10,
    threshold: float = 0.0,
    provider: Optional[str] = None,
    model_name: Optional[str] = None,
    embedding_model_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Celery task to perform a similarity search for a user's cards.

    Args:
        query: The query text to search for
        user_id: ID of the user who owns the cards
        limit: Maximum number of results to return (default: 10)
        threshold: Minimum similarity score threshold (default: 0.0)
        provider: Optional provider name for embedding service
        model_name: Optional model name for embedding service
        embedding_model_id: Optional embedding model ID from the database

    Returns:
        Dictionary containing task result information:
        - success: Boolean indicating if operation was successful
        - results: List of search results (only present if success is True)
        - error: Error message if any (only present if success is False)
        - error_details: Additional error information (only present if success is False)
    """
    logger.info(f"Performing similarity search for user ID: {user_id}, query: '{query}'")

    try:
        # Get the user
        user = User.objects.get(id=user_id)

        # Get embedding service with specified parameters
        card_embedding_service = get_card_embedding_service(
            provider=provider,
            model_name=model_name,
            embedding_model_id=embedding_model_id
        )

        # Perform similarity search
        search_results = card_embedding_service.similarity_search(
            query=query,
            user=user,
            limit=limit,
            threshold=threshold
        )

        # Transform results to be serializable using GenericCardSerializer
        serializable_results = []
        for result in search_results:
            generic_card = result['generic_card']
            serialized_card = GenericCardSerializer(generic_card).data
            serializable_results.append({
                'generic_card': serialized_card,
                'similarity_score': result['similarity_score']
            })

        logger.info(f"Successfully performed similarity search for user ID: {user_id}, found {len(serializable_results)} results")

        # Return success result
        return {
            "success": True,
            "query": query,
            "user_id": user_id,
            "results": serializable_results
        }

    except User.DoesNotExist:
        error = AppError(
            message=f"User with ID {user_id} not found",
            error_code="USER_NOT_FOUND"
        )
        logger.error(str(error))
        return {
            "success": False,
            "query": query,
            "user_id": user_id,
            "error": str(error),
            "error_details": error.to_dict()
        }
    except Exception as e:
        error = AppError.from_exception(
            e,
            prefix=f"Error performing similarity search for user ID {user_id}",
            details={"user_id": user_id, "query": query}
        )
        logger.error(error.get_detailed_message(), exc_info=True)
        return {
            "success": False,
            "query": query,
            "user_id": user_id,
            "error": str(error),
            "error_details": error.to_dict()
        }

