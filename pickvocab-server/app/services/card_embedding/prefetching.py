"""
Prefetching utilities for card embedding service.

This module contains methods for prefetching related objects to avoid N+1 queries.
"""

from typing import List, Dict, Tuple, Union, Optional

from django.contrib.contenttypes.models import ContentType

from ...models import (
    DefinitionCard,
    ContextCard,
    GenericCard,
    WordDefinition,
    WordInContext,
)
from .data_classes import CardTypes, GroupedCards, BatchProcessResult
from .types import CardId, EmbeddingVector, ObjectId
from app.utils import get_service_logger

logger = get_service_logger()


def prefetch_related_objects(
    cards: List[Union[GenericCard, DefinitionCard, ContextCard]],
) -> List[Union[GenericCard, DefinitionCard, ContextCard]]:
    """
    Prefetch related objects for cards to avoid N+1 queries.

    Args:
        cards: List of card instances

    Returns:
        The same list of cards, but with related objects prefetched
    """
    if not cards:
        return cards

    # Skip if we shouldn't prefetch (e.g., in-memory objects)
    if not should_prefetch(cards):
        return cards

    # Identify card types in the input
    card_types = identify_card_types(cards)

    # Process each card type
    if card_types.has_generic_cards:
        # For GenericCards, we need to replace the original cards with prefetched ones
        cards = prefetch_generic_cards(cards, card_types)

    # For direct card types, we just prefetch related objects
    if card_types.has_definition_cards:
        prefetch_definition_cards(cards)

    if card_types.has_context_cards:
        prefetch_context_cards(cards)

    # Return the processed list
    return cards


def should_prefetch(
    cards: List[Union[GenericCard, DefinitionCard, ContextCard]],
) -> bool:
    """
    Determine if prefetching should be performed.

    Skip prefetching for in-memory objects or newly created objects not yet saved to the database.

    Args:
        cards: List of card instances

    Returns:
        True if prefetching should be performed, False otherwise
    """
    return all(hasattr(card, "id") and card.id is not None for card in cards)


def identify_card_types(
    cards: List[Union[GenericCard, DefinitionCard, ContextCard]],
) -> CardTypes:
    """
    Identify the types of cards in the input list.

    Args:
        cards: List of card instances

    Returns:
        CardTypes object with flags for each card type
    """
    return CardTypes(
        has_generic_cards=any(isinstance(card, GenericCard) for card in cards),
        has_definition_cards=any(isinstance(card, DefinitionCard) for card in cards),
        has_context_cards=any(isinstance(card, ContextCard) for card in cards),
    )


def prefetch_generic_cards(
    cards: List[Union[GenericCard, DefinitionCard, ContextCard]], card_types: CardTypes
) -> List[Union[GenericCard, DefinitionCard, ContextCard]]:
    """
    Handle prefetching for GenericCards.

    Args:
        cards: List of card instances
        card_types: CardTypes object with flags for each card type

    Returns:
        List of cards with GenericCards replaced by prefetched versions
    """
    # Get IDs of generic cards
    generic_card_ids = [card.id for card in cards if isinstance(card, GenericCard)]

    if not generic_card_ids:
        return cards

    # Get content types
    content_types = get_card_content_types()

    # Fetch and map definition cards
    definition_card_map = fetch_definition_cards_for_generic_cards(
        generic_card_ids, content_types["definition"]
    )

    # Fetch and map context cards
    context_card_map = fetch_context_cards_for_generic_cards(
        generic_card_ids, content_types["context"]
    )

    # Replace cards in the original list with prefetched ones
    return replace_generic_cards_with_prefetched(
        cards, content_types, definition_card_map, context_card_map
    )


def get_card_content_types() -> Dict[str, ContentType]:
    """
    Get ContentType objects for card types.

    Returns:
        Dictionary mapping card type names to ContentType objects
    """
    return {
        "definition": ContentType.objects.get_for_model(DefinitionCard),
        "context": ContentType.objects.get_for_model(ContextCard),
    }


def fetch_definition_cards_for_generic_cards(
    generic_card_ids: List[int], definition_content_type: ContentType
) -> Dict[int, DefinitionCard]:
    """
    Fetch DefinitionCard objects for GenericCards.

    Args:
        generic_card_ids: List of GenericCard IDs
        definition_content_type: ContentType for DefinitionCard

    Returns:
        Dictionary mapping DefinitionCard IDs to DefinitionCard objects
    """
    # Fetch generic cards with definition cards
    definition_linked_cards = GenericCard.objects.filter(
        id__in=generic_card_ids, card_content_type=definition_content_type
    ).select_related("card_content_type")

    # Use prefetch_related to load the actual DefinitionCard objects
    # and their word_ref in a single query
    definition_card_ids = [gc.card_object_id for gc in definition_linked_cards]

    if not definition_card_ids:
        return {}

    definition_cards = DefinitionCard.objects.filter(
        id__in=definition_card_ids
    ).select_related("word_ref")

    # Create a mapping for quick lookup
    return {dc.id: dc for dc in definition_cards}


def fetch_context_cards_for_generic_cards(
    generic_card_ids: List[int], context_content_type: ContentType
) -> Dict[int, ContextCard]:
    """
    Fetch ContextCard objects for GenericCards.

    Args:
        generic_card_ids: List of GenericCard IDs
        context_content_type: ContentType for ContextCard

    Returns:
        Dictionary mapping ContextCard IDs to ContextCard objects
    """
    # Context cards with word_in_context
    context_linked_cards = GenericCard.objects.filter(
        id__in=generic_card_ids, card_content_type=context_content_type
    ).select_related("card_content_type")

    context_card_ids = [gc.card_object_id for gc in context_linked_cards]

    if not context_card_ids:
        return {}

    context_cards = ContextCard.objects.filter(id__in=context_card_ids).select_related(
        "word_in_context"
    )

    # Create a mapping for quick lookup
    return {cc.id: cc for cc in context_cards}


def replace_generic_cards_with_prefetched(
    cards: List[Union[GenericCard, DefinitionCard, ContextCard]],
    content_types: Dict[str, ContentType],
    definition_card_map: Dict[int, DefinitionCard],
    context_card_map: Dict[int, ContextCard],
) -> List[Union[GenericCard, DefinitionCard, ContextCard]]:
    """
    Replace GenericCards in the list with prefetched versions.

    Args:
        cards: List of card instances
        content_types: Dictionary mapping card type names to ContentType objects
        definition_card_map: Dictionary mapping DefinitionCard IDs to DefinitionCard objects
        context_card_map: Dictionary mapping ContextCard IDs to ContextCard objects

    Returns:
        List of cards with GenericCards replaced by prefetched versions
    """
    result = []

    for card in cards:
        if isinstance(card, GenericCard):
            # Try to find a prefetched version of this card
            new_card = card  # Default to original

            # Check if this is a definition card
            if card.card_content_type_id == content_types["definition"].id:
                if card.card_object_id in definition_card_map:
                    # Set the card attribute to the prefetched definition card
                    # We can't replace the GenericCard itself, but we can update its contents
                    new_card = card
                    # GenericCard.card is a property that uses GenericForeignKey
                    # We'll access the content type and object ID directly
                    new_card.card = definition_card_map[card.card_object_id]

            # Check if this is a context card
            elif card.card_content_type_id == content_types["context"].id:
                if card.card_object_id in context_card_map:
                    # Set the card attribute to the prefetched context card
                    new_card = card
                    new_card.card = context_card_map[card.card_object_id]

            result.append(new_card)
        else:
            result.append(card)

    return result


def prefetch_definition_cards(
    cards: List[Union[GenericCard, DefinitionCard, ContextCard]],
) -> None:
    """
    Handle prefetching for direct DefinitionCards.

    Args:
        cards: List of card instances
    """
    definition_card_ids = [
        card.id for card in cards if isinstance(card, DefinitionCard)
    ]

    if definition_card_ids:
        # This is just for prefetching - we don't need to replace anything
        DefinitionCard.objects.filter(id__in=definition_card_ids).select_related(
            "word_ref"
        )


def prefetch_context_cards(
    cards: List[Union[GenericCard, DefinitionCard, ContextCard]],
) -> None:
    """
    Handle prefetching for direct ContextCards.

    Args:
        cards: List of card instances
    """
    context_card_ids = [card.id for card in cards if isinstance(card, ContextCard)]

    if context_card_ids:
        # This is just for prefetching - we don't need to replace anything
        ContextCard.objects.filter(id__in=context_card_ids).select_related(
            "word_in_context"
        )


def group_cards_by_type(
    cards: List[Union[GenericCard, DefinitionCard, ContextCard]],
) -> GroupedCards:
    """
    Group cards by their actual type for batch processing.

    Args:
        cards: List of card instances

    Returns:
        GroupedCards containing definition cards, context cards, and error map
    """
    definition_cards = []
    context_cards = []
    card_error_map = {}

    for i, card in enumerate(cards):
        card_id = getattr(card, "id", -1)

        # Handle different card types without excessive nesting
        try:
            if isinstance(card, GenericCard):
                categorize_generic_card_by_type(
                    card, i, definition_cards, context_cards, card_error_map
                )
            elif isinstance(card, DefinitionCard):
                definition_cards.append((card, card, i))
            elif isinstance(card, ContextCard):
                context_cards.append((card, card, i))
            else:
                error_msg = f"Unsupported card type: {type(card)}"
                logger.warning(f"{error_msg} for card ID: {card_id}")
                card_error_map[card_id] = error_msg
        except Exception as e:
            error_msg = f"Error processing card: {str(e)}"
            logger.error(f"{error_msg} for card ID: {card_id}")
            card_error_map[card_id] = error_msg

    return GroupedCards(
        definition_cards=definition_cards,
        context_cards=context_cards,
        card_error_map=card_error_map,
    )


def categorize_generic_card_by_type(
    card: GenericCard,
    index: int,
    definition_cards: List,
    context_cards: List,
    error_map: Dict[int, str],
) -> None:
    """Categorize a generic card based on its actual type and add it to the appropriate collection.

    Args:
        card: The GenericCard to categorize
        index: The original index of the card in the input list
        definition_cards: Collection for definition cards
        context_cards: Collection for context cards
        error_map: Dictionary to track errors by card ID
    """
    card_id = getattr(card, "id", -1)

    # Check if we have a prefetched card
    if hasattr(card, "card") and card.card is not None:
        actual_card = card.card
    else:
        # Use the standard GenericForeignKey accessor
        actual_card = card.card

    if isinstance(actual_card, DefinitionCard):
        definition_cards.append((card, actual_card, index))
    elif isinstance(actual_card, ContextCard):
        context_cards.append((card, actual_card, index))
    else:
        error_msg = f"Unsupported card content type: {type(actual_card)}"
        logger.warning(f"{error_msg} for card ID: {card_id}")
        error_map[card_id] = error_msg


def process_definition_results(
    definition_cards: List[Tuple[GenericCard, DefinitionCard, int]],
    definition_results: BatchProcessResult,
    all_embeddings: List[Optional[EmbeddingVector]],
    final_error_map: Dict[CardId, str],
) -> Dict[CardId, Tuple[WordDefinition, EmbeddingVector, bool]]:
    """
    Process definition card results and map them to original indices.

    Args:
        definition_cards: List of (original card, definition card, original index) tuples
        definition_results: Results from definition card batch processing
        all_embeddings: List to store embeddings at original indices
        final_error_map: Dictionary to update with error reasons

    Returns:
        Dictionary mapping card IDs to (WordDefinition, embedding, is_new) tuples
    """
    definition_map: Dict[CardId, Tuple[WordDefinition, EmbeddingVector, bool]] = {}

    for i, ((orig_card, definition_card, orig_idx), embedding) in enumerate(
        zip(definition_cards, definition_results.embeddings)
    ):
        if embedding is not None:
            all_embeddings[orig_idx] = embedding
            # Store the mapped card ID to its WordDefinition
            if definition_card.word_ref:
                map_definition_to_result(orig_card, definition_card, embedding,
                                       definition_results, definition_map)
        else:
            card_id = orig_card.id
            error_reason = definition_results.error_map.get(
                i, "Failed to generate embedding for definition card"
            )
            final_error_map[card_id] = error_reason

    return definition_map


def map_definition_to_result(
    orig_card: Union[GenericCard, DefinitionCard],
    definition_card: DefinitionCard,
    embedding: EmbeddingVector,
    definition_results: BatchProcessResult,
    definition_map: Dict[CardId, Tuple[WordDefinition, EmbeddingVector, bool]],
) -> None:
    """
    Map a definition card and embedding to the result dictionary.

    Args:
        orig_card: Original card instance
        definition_card: Definition card instance
        embedding: Generated embedding vector
        definition_results: Results from definition card batch processing
        definition_map: Dictionary to update with mapping
    """
    try:
        # First check if the card has a directly assigned WordDefinition
        word_def = None
        if hasattr(definition_card, '_word_definition'):
            word_def = definition_card._word_definition
            logger.debug(f"Using directly assigned WordDefinition for card ID {orig_card.id}")

        # If still not found, fall back to database query
        if word_def is None and definition_card.word_ref:
            try:
                word_def = WordDefinition.objects.get(
                    word=definition_card.word_ref,
                    word_def_idx=definition_card.word_def_idx,
                )
                logger.debug(f"WordDefinition for card ID {orig_card.id} not found in mapping, queried database")
            except WordDefinition.DoesNotExist:
                logger.warning(f"WordDefinition not found for card ID {orig_card.id}")
                return

        if word_def:
            card_id = orig_card.id
            is_new = is_embedding_new(
                word_def, definition_results.existing_embeddings
            )
            definition_map[card_id] = (word_def, embedding, is_new)
        else:
            logger.warning(f"No WordDefinition found for card ID {orig_card.id}")
    except Exception as e:
        logger.warning(
            f"Failed to map WordDefinition for card ID {orig_card.id}: {str(e)}"
        )


def process_context_results(
    context_cards: List[Tuple[GenericCard, ContextCard, int]],
    context_results: BatchProcessResult,
    all_embeddings: List[Optional[EmbeddingVector]],
    final_error_map: Dict[CardId, str],
) -> Dict[CardId, Tuple[WordInContext, EmbeddingVector, bool]]:
    """
    Process context card results and map them to original indices.

    Args:
        context_cards: List of (original card, context card, original index) tuples
        context_results: Results from context card batch processing
        all_embeddings: List to store embeddings at original indices
        final_error_map: Dictionary to update with error reasons

    Returns:
        Dictionary mapping card IDs to (WordInContext, embedding, is_new) tuples
    """
    context_map: Dict[CardId, Tuple[WordInContext, EmbeddingVector, bool]] = {}

    for i, ((orig_card, context_card, orig_idx), embedding) in enumerate(
        zip(context_cards, context_results.embeddings)
    ):
        if embedding is not None:
            all_embeddings[orig_idx] = embedding
            # Store the mapped card ID to its WordInContext
            if context_card.word_in_context:
                card_id = orig_card.id
                is_new = is_embedding_new(
                    context_card.word_in_context,
                    context_results.existing_embeddings
                )
                context_map[card_id] = (
                    context_card.word_in_context,
                    embedding,
                    is_new,
                )
        else:
            card_id = orig_card.id
            error_reason = context_results.error_map.get(
                i, "Failed to generate embedding for context card"
            )
            final_error_map[card_id] = error_reason

    return context_map


def is_embedding_new(
    obj: Union[WordDefinition, WordInContext],
    existing_embeddings: Dict[ObjectId, EmbeddingVector],
) -> bool:
    """
    Determine if an embedding is new by checking if it exists in existing embeddings.

    Args:
        obj: The object to check (WordDefinition or WordInContext)
        existing_embeddings: Dictionary of existing embeddings

    Returns:
        True if the embedding is new, False otherwise
    """
    return ObjectId(obj.id) not in existing_embeddings


def combine_batch_results(
    original_cards: List[Union[GenericCard, DefinitionCard, ContextCard]],
    grouped_cards: GroupedCards,
    definition_results: BatchProcessResult,
    context_results: BatchProcessResult,
) -> Tuple[
    Dict[CardId, str],
    Dict[CardId, Tuple[WordDefinition, EmbeddingVector, bool]],
    Dict[CardId, Tuple[WordInContext, EmbeddingVector, bool]],
]:
    """
    Combine results from batch processing of different card types.

    Args:
        original_cards: Original list of cards
        grouped_cards: Result from group_cards_by_type
        definition_results: Results from definition card batch processing
        context_results: Results from context card batch processing

    Returns:
        A tuple containing:
        - Dictionary mapping card IDs to error reasons for failed cards
        - Dictionary mapping card IDs to (WordDefinition, embedding, is_new) tuples
        - Dictionary mapping card IDs to (WordInContext, embedding, is_new) tuples
    """
    # Initialize results array with None values
    all_embeddings = [None] * len(original_cards)
    final_error_map = {}

    # Process definition and context results
    definition_map = process_definition_results(
        grouped_cards.definition_cards,
        definition_results,
        all_embeddings,
        final_error_map,
    )

    context_map = process_context_results(
        grouped_cards.context_cards,
        context_results,
        all_embeddings,
        final_error_map,
    )

    # Add errors from the initial grouping
    final_error_map.update(grouped_cards.card_error_map)

    return final_error_map, definition_map, context_map
