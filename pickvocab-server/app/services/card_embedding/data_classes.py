"""
Data classes used by the card embedding service.
"""

from typing import List, Optional, Dict, Any, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TypeVar, Generic
from dataclasses import dataclass

from ...models import (
    DefinitionCard,
    ContextCard,
    GenericCard,
    WordDefinition,
    WordInContext,
)
from .types import CardId, EmbeddingVector, CardIndex, ObjectId
from app.utils.errors import AppError

# Define type variable for model classes
# This should match the definition in batch_processing.py
T = TypeVar("T", WordDefinition, WordInContext)


class BatchProcessResult(NamedTuple):
    """Result of a batch processing operation on objects."""

    embeddings: List[
        Optional[EmbeddingVector]
    ]  # List of embeddings (None for failed items)
    error_map: Dict[int, AppError]  # Map of indices to error messages
    existing_embeddings: Dict[
        ObjectId, EmbeddingVector
    ] = {}  # Dictionary of existing embeddings found during calculation


@dataclass
class GroupedCards:
    """Container for cards grouped by their type."""

    definition_cards: List[
        Tuple[GenericCard, Definition<PERSON>ard, CardIndex]
    ]  # (original_card, actual_card, original_index)
    context_cards: List[
        Tuple[GenericCard, ContextCard, CardIndex]
    ]  # (original_card, actual_card, original_index)
    card_error_map: Dict[CardId, AppError]  # Map of card IDs to error reasons


@dataclass
class ObjectBatchMapping(Generic[T]):
    """Container for object batch processing data."""

    id_to_indices: Dict[ObjectId, List[int]]  # Maps object ID to positions in the original input list for duplicate tracking
    id_to_object: Dict[ObjectId, T]  # Maps object ID to object instance
    needs_embedding: List[ObjectId]  # List of unique object IDs after deduplication: id_to_indices.keys()
    error_map: Dict[int, AppError]  # Map of original indices to error messages
    result_embeddings: List[
        Optional[EmbeddingVector]
    ]  # List of embeddings (None for failed items)


@dataclass
class CardTypes:
    """Container for tracking card types in a collection."""

    has_generic_cards: bool = False
    has_definition_cards: bool = False
    has_context_cards: bool = False


class CardEmbeddingResult(NamedTuple):
    """Result of calculating embeddings for multiple cards."""

    error_map: Dict[CardId, AppError]  # Map of card IDs to error messages
    definition_map: Dict[
        CardId, Tuple[WordDefinition, EmbeddingVector, bool]
    ]  # Map of card IDs to (word_definition, embedding, is_new) tuples
    context_map: Dict[
        CardId, Tuple[WordInContext, EmbeddingVector, bool]
    ]  # Map of card IDs to (word_in_context, embedding, is_new) tuples
