"""
Card embedding service package.

This package provides functionality for calculating and storing embeddings for cards.
"""

from .service import CardEmbeddingService
from .factory import get_card_embedding_service
from .data_classes import BatchProcessResult, GroupedCards, ObjectBatchMapping
from .types import CardId, EmbeddingVector
from .similarity_search import CardSimilaritySearchResult
from app.utils.errors import AppError
# Import tasks for Celery discovery
from .tasks import calculate_and_store_embedding_for_card, similarity_search_task

__all__ = [
    'CardEmbeddingService',
    'get_card_embedding_service',
    'BatchProcessResult',
    'GroupedCards',
    'ObjectBatchMapping',
    'CardId',
    'EmbeddingVector',
    'CardSimilaritySearchResult',
    'AppError',
    'calculate_and_store_embedding_for_card',
    'similarity_search_task',
]
