from django.contrib import admin
from .models import (
    Card,
    ContextCard,
    Deck,
    Deck2,
    DeckCard2,
    DefinitionCard,
    EmbeddingModel,
    FeatureFlag,
    GeminiTextEmbedding004Store,
    GenericCard,
    MistralEmbedStore,
    Review,
    Review2,
    ReviewDeck2,
    ReviewGenericCard,
    UserEmbeddingPreference,
    Word,
    DeckCard,
    ReviewDeck,
    ReviewCard,
    LLMModel,
    WordComparison,
    WordDefinition,
    WordInContext,
    CustomTone, # Added CustomTone
    WritingRevisionHistory,
    WritingRevisionHistoryCards,
)

# Register your models here.


class WordAdmin(admin.ModelAdmin):
    list_display = [
        "word",
        "definitions",
        "creator",
        "get_llm_model",
        "is_verified",
        "slug",
        "created_at",
        "updated_at",
    ]
    search_fields = ["word", "creator__username", "creator__email"]

    def get_llm_model(self, obj):
        if obj.llm_model is None:
            return obj.llm_model
        else:
            return obj.llm_model.name

    get_llm_model.short_description = "llm_model"


class CardAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "word",
        "word_ref",
        "word_def_idx",
        "definition",
        "owner",
        "progress_score",
        "created_at",
        "updated_at",
    ]
    # list_display_links = ['id', 'word_ref', 'owner']
    search_fields = ["id", "word", "owner__username", "owner__email"]


class DeckAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "description",
        "owner",
        "is_demo",
        "created_at",
        "updated_at",
    ]
    search_fields = ["id", "name", "owner__username", "owner__email"]


class DeckCardAdmin(admin.ModelAdmin):
    list_display = ["deck", "card", "created_at", "updated_at"]
    list_display_links = ["deck", "card"]
    search_fields = ["deck__id", "card__id"]


class ReviewAdmin(admin.ModelAdmin):
    list_display = ["id", "owner", "is_master", "created_at", "updated_at"]
    search_fields = ["id", "owner__username", "owner__email"]


class ReviewDeckAdmin(admin.ModelAdmin):
    list_display = ["review", "deck", "created_at", "updated_at"]
    list_display_links = ["review", "deck"]
    search_fields = ["review__id", "deck__id"]


class ReviewCardAdmin(admin.ModelAdmin):
    list_display = ["review", "card", "delta_score", "created_at", "updated_at"]
    list_display_links = ["review", "card"]
    search_fields = ["review__id", "card__id"]


class LLMModelAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "name",
        "provider",
        "arena_score",
        "is_free",
        "is_hidden",
        "is_thinking",
        "created_at",
        "updated_at",
    ]
    search_fields = ["name", "provider"]


class WordComparisonAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "words",
        "content",
        "get_llm_model",
        "creator",
        "created_at",
        "updated_at",
    ]
    search_fields = ["id", "words", "creator__username", "creator__email"]

    def get_llm_model(self, obj):
        if obj.llm_model is None:
            return obj.llm_model
        else:
            return obj.llm_model.name

    get_llm_model.short_description = "llm_model"


class WordInContextAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "word",
        "context",
        "offset",
        "definition",
        "definition_short",
        "get_llm_model",
        "creator",
        "created_at",
        "updated_at",
    ]
    search_fields = ["id", "word", "context", "creator__username", "creator__email"]

    def get_llm_model(self, obj):
        if obj.llm_model is None:
            return obj.llm_model
        else:
            return obj.llm_model.name

    get_llm_model.short_description = "llm_model"


class GenericCardAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "card_content_type",
        "card_object_id",
        "owner",
        "progress_score",
        "created_at",
        "updated_at",
    ]
    search_fields = [
        "id",
        "card_object_id",
        "owner__username",
        "owner__email"
    ]


class DefinitionCardAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "word",
        "word_ref",
        "word_def_idx",
        "definition",
        "created_at",
        "updated_at",
    ]
    # list_display_links = ['id', 'word_ref', 'owner']
    search_fields = [
        "id",
        "word",
    ]


class ContextCardAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "word_in_context",
        "created_at",
        "updated_at",
    ]
    # list_display_links = ['id', 'word_ref', 'owner']
    search_fields = [
        "id",
        "word",
    ]


class Deck2Admin(admin.ModelAdmin):
    list_display = [
        "name",
        "description",
        "owner",
        "is_demo",
        "created_at",
        "updated_at",
    ]
    search_fields = ["id", "name", "owner__username", "owner__email"]


class DeckCard2Admin(admin.ModelAdmin):
    list_display = ["deck", "card", "created_at", "updated_at"]
    list_display_links = ["deck", "card"]
    search_fields = ["deck__id", "card__id"]


class Review2Admin(admin.ModelAdmin):
    list_display = ["id", "owner", "is_master", "created_at", "updated_at"]
    search_fields = ["id", "owner__username", "owner__email"]


class ReviewDeck2Admin(admin.ModelAdmin):
    list_display = ["review", "deck", "created_at", "updated_at"]
    list_display_links = ["review", "deck"]
    search_fields = ["review__id", "deck__id"]


class ReviewGenericCardAdmin(admin.ModelAdmin):
    list_display = ["review", "card", "delta_score", "created_at", "updated_at"]
    list_display_links = ["review", "card"]
    search_fields = ["review__id", "card__id"]


class FeatureFlagAdmin(admin.ModelAdmin):
    list_display = ["name", "is_active", "get_except_users", "created_at", "updated_at"]
    search_fields = ["name"]

    def get_except_users(self, obj):
        return ", ".join([str(user) for user in obj.except_users.all()])
    get_except_users.short_description = 'Except Users'  # Sets column header


class EmbeddingModelAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "model_name",
        "provider",
        "dimensions",
        "created_at",
        "updated_at",
    ]
    search_fields = ["model_name", "provider"]


class UserEmbeddingPreferenceAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "user",
        "embedding_model",
        "is_active",
        "created_at",
        "updated_at",
    ]
    search_fields = ["user__username", "user__email", "embedding_model__model_name"]


class WordDefinitionAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "word",
        "word_def_idx",
        "created_at",
        "updated_at",
    ]
    search_fields = ["id", "word__word"]


class GeminiTextEmbedding004StoreAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "embedding_model",
        "content_type",
        "object_id",
        "created_at",
        "updated_at",
    ]
    search_fields = ["id", "embedding_model__model_name", "content_type__model", "object_id"]


class MistralEmbedStoreAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "embedding_model",
        "content_type",
        "object_id",
        "created_at",
        "updated_at",
    ]
    search_fields = ["id", "embedding_model__model_name", "content_type__model", "object_id"]


class WritingRevisionHistoryAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "owner",
        "created_at",
        "trigger_type",
        "original_text",
        "use_my_vocabulary",
        "selected_tone",
        "revisions",
    ]
    search_fields = [
        "id",
        "owner__username",
        "owner__email",
    ]


class WritingRevisionHistoryCardsAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "history",
        "card",
    ]
    search_fields = [
        "id",
        "history__id",
        "card__id",
    ]


# Admin configuration for CustomTone
class CustomToneAdmin(admin.ModelAdmin):
    list_display = ['id', 'owner', 'name', 'description', 'created_at', 'updated_at']
    search_fields = ['id', 'name', 'owner__username', 'owner__email']


admin.site.register(Word, WordAdmin)
admin.site.register(Card, CardAdmin)
admin.site.register(Deck, DeckAdmin)
admin.site.register(DeckCard, DeckCardAdmin)
admin.site.register(Review, ReviewAdmin)
admin.site.register(ReviewDeck, ReviewDeckAdmin)
admin.site.register(ReviewCard, ReviewCardAdmin)
admin.site.register(LLMModel, LLMModelAdmin)
admin.site.register(WordComparison, WordComparisonAdmin)
admin.site.register(WordInContext, WordInContextAdmin)
admin.site.register(GenericCard, GenericCardAdmin)
admin.site.register(DefinitionCard, DefinitionCardAdmin)
admin.site.register(ContextCard, ContextCardAdmin)
admin.site.register(Deck2, Deck2Admin)
admin.site.register(DeckCard2, DeckCard2Admin)
admin.site.register(Review2, Review2Admin)
admin.site.register(ReviewDeck2, ReviewDeck2Admin)
admin.site.register(ReviewGenericCard, ReviewGenericCardAdmin)
admin.site.register(FeatureFlag, FeatureFlagAdmin)
admin.site.register(EmbeddingModel, EmbeddingModelAdmin)
admin.site.register(UserEmbeddingPreference, UserEmbeddingPreferenceAdmin)
admin.site.register(WordDefinition, WordDefinitionAdmin)
admin.site.register(GeminiTextEmbedding004Store, GeminiTextEmbedding004StoreAdmin)
admin.site.register(MistralEmbedStore, MistralEmbedStoreAdmin)
admin.site.register(WritingRevisionHistory, WritingRevisionHistoryAdmin)
admin.site.register(WritingRevisionHistoryCards, WritingRevisionHistoryCardsAdmin)
admin.site.register(CustomTone, CustomToneAdmin) # Register CustomTone
