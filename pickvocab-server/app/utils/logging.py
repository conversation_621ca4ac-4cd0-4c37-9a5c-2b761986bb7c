"""
Logging utilities for the application.

This module provides helper functions to get appropriately configured loggers
for different parts of the application, ensuring consistency in logging patterns.
"""

import logging
import inspect
import sys
from typing import Optional, Any, Dict


def get_service_logger(service_name: Optional[str] = None) -> logging.Logger:
    """
    Get a logger for a service with the appropriate configuration.
    
    The logger will automatically determine the correct namespace based on the calling module
    if service_name is not provided. This ensures logs are properly categorized in the
    app.services hierarchy.
    
    Args:
        service_name: Optional explicit service name to use. If not provided,
                     will be automatically determined from the calling module.
    
    Returns:
        A properly configured logger for the service
    
    Examples:
        # In app/services/my_service.py:
        logger = get_service_logger()  # Returns logger for 'app.services.my_service'
        
        # Or explicitly:
        logger = get_service_logger("my_custom_service")  # Returns logger for 'app.services.my_custom_service'
    """
    if service_name is None:
        # Get calling frame (2 frames up from current)
        frame = inspect.currentframe()
        if frame is not None:
            try:
                frame = frame.f_back
                if frame is not None:
                    # Get the module of the caller
                    caller_module = inspect.getmodule(frame)
                    if caller_module is not None:
                        module_name = caller_module.__name__
                        # Extract service name from module path
                        if 'app.services' in module_name:
                            service_name = module_name
                        else:
                            parts = module_name.split('.')
                            if len(parts) > 0:
                                service_name = parts[-1]
            finally:
                # Clean up reference cycles
                del frame
    
    if service_name is None:
        # Fallback to __name__ if we couldn't determine the calling module
        service_name = "__name__"
    
    # If it's not already a full path and not already starting with app.services
    if service_name and not service_name.startswith('app.services'):
        # Check if it's a full module path inside the app package
        if service_name.startswith('app.'):
            logger_name = service_name
        else:
            # Otherwise, construct the proper app.services prefix
            logger_name = f"app.services.{service_name}"
    else:
        logger_name = service_name
    
    return logging.getLogger(logger_name)
