"""
Error handling utilities for the application.
"""

import traceback
from typing import Optional, Dict, Any


class AppError:
    """
    A typed representation of an application error with optional error code and exception details.
    
    This class provides benefits over raw strings:
    - Consistent error message format
    - Validation for error message content
    - Preservation of original exceptions and stack traces
    - Additional metadata like error codes
    - Future extensibility (severity levels, categorization, etc.)
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, exception: Optional[Exception] = None, details: Optional[Dict[str, Any]] = None):
        """
        Create a new application error.
        
        Args:
            message: The error message text
            error_code: Optional error code for categorization
            exception: Optional original exception that caused this error
            details: Optional dictionary with additional error details
        """
        if not message:
            raise ValueError("Error message cannot be empty")
        
        self.message = message
        self.error_code = error_code
        self.exception = exception
        self.details = details or {}
        
        # Capture stack trace if an exception is provided
        if exception:
            self.details['exception_type'] = type(exception).__name__
            self.details['stack_trace'] = ''.join(traceback.format_exception(type(exception), exception, exception.__traceback__))
    
    def __str__(self) -> str:
        """Convert to string representation for logging and display."""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def get_detailed_message(self) -> str:
        """
        Get a detailed error message including available stack trace.
        Useful for debug logging and developer-facing error messages.
        """
        result = str(self)
        if 'stack_trace' in self.details:
            result += f"\nStack trace:\n{self.details['stack_trace']}"
        elif self.details:
            # Add any other details if no stack trace is available
            details_str = ", ".join(f"{k}={v}" for k, v in self.details.items())
            result += f" (Details: {details_str})"
        return result
    
    @classmethod
    def from_exception(cls, exception: Exception, prefix: Optional[str] = None, details: Optional[Dict[str, Any]] = None) -> 'AppError':
        """
        Create an error from an exception, preserving stack trace and exception details.
        
        Args:
            exception: The exception to convert
            prefix: Optional prefix to add to the message
            details: Optional additional details to include
            
        Returns:
            An AppError instance with the exception information
        """
        message = str(exception)
        if prefix:
            message = f"{prefix}: {message}"
        
        # Extract error code from exception if available
        error_code = getattr(exception, 'code', None)
        
        # Create a new AppError with the exception
        return cls(message, error_code, exception, details)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to a dictionary representation, useful for serialization.
        
        Returns:
            A dictionary with error attributes
        """
        result = {
            'message': self.message,
            'details': self.details.copy()  # Create a copy to avoid mutation
        }
        
        if self.error_code:
            result['error_code'] = self.error_code
            
        return result 