from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation
from django.contrib.contenttypes.models import ContentType
import pgvector.django

# Create your models here.

class LLMModel(models.Model):
    class LLMProvider(models.TextChoices):
        OpenAI = 'OpenAI', _('OpenAI')
        Anthropic = 'Anthropic', _('Anthropic')
        Gemini = 'Gemini', _('Gemini')
        OpenRouter = 'OpenRouter', _('OpenRouter')
        ArliAI = 'ArliAI', _('ArliAI')
        Groq = 'Groq', _('Groq')
        Cerebras =  'Cerebras', _('Cerebras')
        SambaNova = 'SambaNova', _('SambaNova')
        Pickvocab = 'Pickvocab', _('Pickvocab')
        Mistral = 'Mistral', _('Mistral')
        Codestral = 'Codestral', _('Codestral')
        Chutes = 'Chutes', _('Chutes')
    name = models.CharField(max_length=200)
    label = models.CharField(max_length=200)
    provider = models.CharField(
        max_length=50,
        choices=LLMProvider
    )
    arena_score = models.SmallIntegerField(default=0)
    is_free = models.BooleanField(default=False)
    is_thinking = models.BooleanField(default=False)
    is_hidden = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class Word(models.Model):
    word = models.CharField(max_length=200)
    definitions = models.JSONField(default=list)
    creator = models.ForeignKey(
        get_user_model(), on_delete=models.SET_NULL, null=True
    )  # don't set on delete cascade here
    llm_model = models.ForeignKey(
        LLMModel, on_delete=models.SET_NULL, null=True
    ) # don't set on delete cascade here
    is_verified = models.BooleanField(default=False)
    slug=models.SlugField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["word", "slug"]),
        ]
        ordering = ["-id"]


class Card(models.Model):
    word = models.CharField(max_length=200)
    word_ref = models.ForeignKey(Word, on_delete=models.SET_NULL, null=True)
    word_def_idx = models.PositiveIntegerField(default=0)
    definition = models.JSONField()
    owner = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    progress_score = models.SmallIntegerField(default=3)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["word"]),
        ]
        ordering = ["-id"]


class Deck(models.Model):
    name = models.CharField(max_length=200)
    description = models.CharField(max_length=500, null=True, blank=True)
    owner = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    cards = models.ManyToManyField(Card, through="DeckCard", related_name="decks")
    is_demo = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["name"]),
            models.Index(fields=["is_demo"]),
        ]
        ordering = ["-id"]


class DeckCard(models.Model):
    deck = models.ForeignKey(Deck, on_delete=models.CASCADE)
    card = models.ForeignKey(Card, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class Review(models.Model):
    owner = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    cards = models.ManyToManyField(Card, through="ReviewCard")
    decks = models.ManyToManyField(Deck, through="ReviewDeck")
    is_master = models.BooleanField(default=False) # master deck (contains all cards)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class ReviewCard(models.Model):
    review = models.ForeignKey(Review, on_delete=models.CASCADE)
    card = models.ForeignKey(Card, on_delete=models.CASCADE)
    delta_score = models.SmallIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class ReviewDeck(models.Model):
    review = models.ForeignKey(Review, on_delete=models.CASCADE)
    deck = models.ForeignKey(Deck, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class WordComparison(models.Model):
    words = models.CharField(max_length=2000)
    content = models.CharField(max_length=20000)
    llm_model = models.ForeignKey(
        LLMModel, on_delete=models.SET_NULL, null=True
    )
    creator = models.ForeignKey(
        get_user_model(), on_delete=models.SET_NULL, null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["words"]),
        ]
        ordering = ["-id"]


class WordInContext(models.Model):
    word = models.CharField(max_length=2000)
    context = models.CharField(max_length=10000)
    offset = models.PositiveSmallIntegerField(default=0)
    # length = len(word)
    definition = models.JSONField(null=True)
    definition_short = models.JSONField(null=True)
    creator=models.ForeignKey(
        get_user_model(), on_delete=models.SET_NULL, null=True
    )
    llm_model = models.ForeignKey(
        LLMModel, on_delete=models.SET_NULL, null=True
    )
    created_at  = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    gemini_text_embedding_004_vector = GenericRelation(
        'GeminiTextEmbedding004Store',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='word_in_context'
    )
    mistral_embed_vector = GenericRelation(
        'MistralEmbedStore',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='word_in_context'
    )

    class Meta:
        indexes = [
            models.Index(fields=["word"]),
            models.Index(fields=["word", "context"]),
        ]
        ordering = ["-id"]


class GenericCard(models.Model):
    card = GenericForeignKey(
        "card_content_type", "card_object_id"
    )
    card_content_type = models.ForeignKey(
        ContentType, on_delete=models.CASCADE
    )
    card_object_id = models.PositiveIntegerField()
    owner = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    progress_score = models.SmallIntegerField(default=3)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class DefinitionCard(models.Model):
    word = models.CharField(max_length=200)
    word_ref = models.ForeignKey(Word, on_delete=models.SET_NULL, null=True)
    word_def_idx = models.PositiveIntegerField(default=0)
    definition = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    generic_card = GenericRelation(
        GenericCard,
        related_query_name='definition_card',
        content_type_field='card_content_type',
        object_id_field='card_object_id',
    )

    class Meta:
        indexes = [
            models.Index(fields=["word"]),
        ]
        ordering = ["-id"]


class ContextCard(models.Model):
    word_in_context = models.ForeignKey(WordInContext, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    generic_card = GenericRelation(
        GenericCard,
        related_query_name='context_card',
        content_type_field='card_content_type',
        object_id_field='card_object_id',
    )


class Deck2(models.Model):
    name = models.CharField(max_length=200)
    description = models.CharField(max_length=500, null=True, blank=True)
    owner = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    cards = models.ManyToManyField(GenericCard, through="DeckCard2", related_name="decks")
    is_demo = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["name"]),
            models.Index(fields=["is_demo"]),
        ]
        ordering = ["-id"]


class DeckCard2(models.Model):
    deck = models.ForeignKey(Deck2, on_delete=models.CASCADE)
    card = models.ForeignKey(GenericCard, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class Review2(models.Model):
    owner = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    cards = models.ManyToManyField(GenericCard, through="ReviewGenericCard")
    decks = models.ManyToManyField(Deck2, through="ReviewDeck2")
    is_master = models.BooleanField(default=False) # master deck (contains all cards)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class ReviewGenericCard(models.Model):
    review = models.ForeignKey(Review2, on_delete=models.CASCADE)
    card = models.ForeignKey(GenericCard, on_delete=models.CASCADE)
    delta_score = models.SmallIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class ReviewDeck2(models.Model):
    review = models.ForeignKey(Review2, on_delete=models.CASCADE)
    deck = models.ForeignKey(Deck2, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class FeatureFlag(models.Model):
    name = models.CharField(
        max_length=100,
        unique=True
    )
    is_active = models.BooleanField(default=False)
    except_users = models.ManyToManyField(
        get_user_model(),
        related_name='feature_flags',
        blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def is_enabled(cls, flag_name: str, user_id: int) -> bool:
        """
        Check if a feature flag is enabled for a specific user.
        Returns True if:
        - Flag doesn't exist (default to enabled)
        - Flag is active and user is not in except_users
        - Flag is inactive and user is in except_users
        """
        flag = cls.objects.filter(name=flag_name).first()
        if not flag:
            return False
            
        is_user_excepted = user_id in flag.except_users.values_list('id', flat=True)
        return flag.is_active != is_user_excepted


class EmbeddingModel(models.Model):
    class EmbeddingProvider(models.TextChoices):
        Gemini = 'Gemini', _('Gemini')
        Mistral = 'Mistral', _('Mistral')

    provider = models.CharField(
        max_length=50,
        choices=EmbeddingProvider
    )
    dimensions = models.PositiveIntegerField()
    model_name = models.CharField(max_length=200)
    api_key = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.model_name} ({self.provider})"

    class Meta:
        indexes = [
            models.Index(fields=["model_name"]),
        ]
        ordering = ["-id"]


class UserEmbeddingPreference(models.Model):
    user = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    embedding_model = models.ForeignKey(EmbeddingModel, on_delete=models.SET_NULL, null=True)
    is_active = models.BooleanField(default=True)
    # api_key?
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["user"]),
        ]


class WordDefinition(models.Model):
    word = models.ForeignKey(Word, on_delete=models.CASCADE)
    word_def_idx = models.PositiveIntegerField(default=0)
    gemini_text_embedding_004_vector = GenericRelation(
        'GeminiTextEmbedding004Store',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='word_definition_gemini_text_embedding_004'
    )
    mistral_embed_vector = GenericRelation(
        'MistralEmbedStore',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='word_definition_mistral_embed'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["word"]),
        ]
        ordering = ["-id"]

class GeminiTextEmbedding004Store(models.Model):
    embedding_model = models.ForeignKey(EmbeddingModel, on_delete=models.CASCADE)
    vector = pgvector.django.VectorField(dimensions=768)

    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
            pgvector.django.IvfflatIndex(
                name='gemini_vector_idx',
                fields=['vector'],
                lists=100,
                opclasses=['vector_cosine_ops']
            )
        ]
        ordering = ["-id"]


class MistralEmbedStore(models.Model):
    embedding_model = models.ForeignKey(EmbeddingModel, on_delete=models.CASCADE)
    vector = pgvector.django.VectorField(dimensions=1024)

    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
            pgvector.django.IvfflatIndex(
                name='mistral_vector_idx',
                fields=['vector'],
                lists=100,
                opclasses=['vector_cosine_ops']
            )
        ]
        ordering = ["-id"]

class WritingRevisionHistory(models.Model):
    owner = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    trigger_type = models.CharField(max_length=20)
    original_text = models.TextField()
    use_my_vocabulary = models.BooleanField()
    selected_tone = models.CharField(max_length=100)
    revisions = models.JSONField()

    class Meta:
        indexes = [
            models.Index(fields=["owner"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["trigger_type"]),
        ]
        ordering = ["-created_at"]

        
class WritingRevisionHistoryCards(models.Model):
    history = models.ForeignKey(WritingRevisionHistory, on_delete=models.CASCADE, related_name='cards')
    card = models.ForeignKey('GenericCard', on_delete=models.CASCADE)

    class Meta:
        indexes = [
            models.Index(fields=["history"]),
            models.Index(fields=["card"]),
        ]

# Custom Tones for Writing Feature
class CustomTone(models.Model):
    owner = models.ForeignKey(
        get_user_model(),
        on_delete=models.CASCADE,
        related_name='custom_tones'
    )
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['owner', 'name'], name='unique_tone_name_per_owner')
        ]
        indexes = [
            models.Index(fields=["owner"]),
            models.Index(fields=["name"]),
        ]
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.owner.username}'s tone: {self.name}"



