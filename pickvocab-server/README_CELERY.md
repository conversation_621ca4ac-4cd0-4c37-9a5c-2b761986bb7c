# Celery Setup for PickVocab

This document explains how to use Celery with the PickVocab server.

## Overview

Celery is used for handling asynchronous tasks in the PickVocab application. It allows us to run time-consuming operations in the background, such as computing embeddings, finding relevant words, and revising text.

## Components

The Celery setup consists of the following components:

1. **Celery Application**: Configured in `pickvocab/celery.py`
2. **Redis**: Used as the message broker
3. **Celery Worker**: Processes the tasks
4. **Celery Beat**: Schedules periodic tasks (optional)

## Running Celery

### Running Locally

To run Celery locally for development:

1. Start Redis:
   ```bash
   docker run -d -p 6379:6379 redis:7-alpine
   ```

2. Start the Celery worker:
   ```bash
   cd pickvocab-server
   ./scripts/run_celery.sh
   ```

3. (Optional) Start the Celery beat scheduler:
   ```bash
   cd pickvocab-server
   ./scripts/run_celery_beat.sh
   ```

## Creating Tasks

To create a new Celery task:

1. Add a function to `app/tasks.py` with the `@shared_task` decorator:
   ```python
   @shared_task
   def my_task(param):
       # Task implementation
       return result
   ```

2. Call the task asynchronously:
   ```python
   from app.tasks import my_task
   
   # Run the task asynchronously
   result = my_task.delay(param)
   
   # Get the task ID
   task_id = result.id
   ```

## Testing Celery

There are several ways to test the Celery setup:

### Using the Management Command

```bash
python manage.py test_celery "test parameter"
```

### Using the Test Script

```bash
python scripts/test_celery.py
```

Both methods will send a test task to Celery and print the task ID.

## Monitoring Celery

You can monitor Celery tasks using the Celery command-line tools:

```bash
celery -A pickvocab events
```

For a more comprehensive monitoring solution, you can use Flower:

```bash
poetry add flower
celery -A pickvocab flower
```

Then open http://localhost:5555 in your browser.

### Using Flower with Docker

When running in a Docker environment, you can use Flower by adding it to your Docker Compose configuration:

```yaml
flower:
  image: pickvocab-backend:latest
  build:
    context: ./pickvocab-server
  container_name: pickvocab-flower
  command: celery -A pickvocab flower --port=5555
  ports:
    - 5555:5555
  environment:
    - DB_NAME=pickvocab
    - DB_USER=duy
    - DB_PASSWORD=duy123
    - DB_HOST=db
    - DB_PORT=5432
    - CELERY_BROKER_URL=redis://redis:6379/0
    - CELERY_RESULT_BACKEND=django-db
  depends_on:
    - redis
    - celery_worker
  restart: unless-stopped
```

This will make Flower available at http://localhost:5555 when running with Docker Compose.

### Flower Features

Flower provides several useful features for monitoring Celery:

- Real-time monitoring of task progress
- Task history and statistics
- Worker status and control
- Task inspection and management
- API for programmatic access

### Securing Flower

For production environments, it's recommended to secure Flower with authentication:

```bash
celery -A pickvocab flower --basic-auth=username:password
```

Or in Docker Compose:

```yaml
command: celery -A pickvocab flower --port=5555 --basic-auth=username:password
```

## Troubleshooting

If you encounter issues with Celery:

1. Check that Redis is running:
   ```bash
   redis-cli ping
   ```
   It should respond with "PONG".

2. Check the Celery worker logs:
   ```bash
   celery -A pickvocab worker -l DEBUG
   ```

3. Make sure the `CELERY_BROKER_URL` and `CELERY_RESULT_BACKEND` settings are correct in `pickvocab/settings.py`.

## Further Reading

- [Celery Documentation](https://docs.celeryq.dev/)
- [Redis Documentation](https://redis.io/documentation)
- [Django Celery Documentation](https://docs.celeryq.dev/en/stable/django/) 