# Plan: Add Embedding Status Check Endpoint to GenericCardViewSet

## Goal

Add an endpoint to `GenericCardViewSet` in `pickvocab-server/app/views.py` to check if a specific `GenericCard` instance has had an embedding calculated for it. This check must consider the user's active embedding preference (`UserEmbeddingPreference`) and fall back to a default model if no preference is set.

## Initial Analysis & Incorrect Assumption

*   The initial thought was that the embedding vector (e.g., in `GeminiTextEmbedding004Store` or `MistralEmbedStore`) would be directly linked to the `GenericCard` instance via a `GenericForeignKey` (`content_object`).
*   This led to an initial plan involving adding `GenericRelation` fields to the `GenericCard` model itself for easy reverse querying.

## Information Gathering & Findings

1.  **Models (`pickvocab-server/app/models.py`):**
    *   Confirmed `GenericCard` uses `GenericForeignKey` (`card`) to link to either a `DefinitionCard` or a `ContextCard`.
    *   Confirmed `GenericCard` itself *lacks* direct `GenericRelation` fields pointing to embedding stores.
    *   Confirmed `UserEmbeddingPreference` links a `User` to an `EmbeddingModel`.
    *   Confirmed `EmbeddingModel` stores `provider` and `model_name`.
    *   Confirmed embedding stores (`GeminiTextEmbedding004Store`, `MistralEmbedStore`) use `GenericForeignKey` (`content_object`) to link back to the *source* of the embedding.
    *   Crucially, found `WordDefinition` and `WordInContext` *do* have `GenericRelation` fields pointing to the relevant embedding stores (e.g., `gemini_text_embedding_004_vector`, `mistral_embed_vector`, etc.). This hinted that embeddings might be associated with these models instead of `GenericCard`.
2.  **Embedding Task (`pickvocab-server/app/services/card_embedding/tasks.py`):**
    *   The `calculate_and_store_embedding_for_card` task takes a `GenericCard` ID.
    *   It retrieves the `GenericCard` instance.
    *   It delegates the main logic to `card_embedding_service.calculate_and_store_embedding_for_card(card)`.
3.  **Embedding Service Factory (`pickvocab-server/app/services/card_embedding/factory.py`):**
    *   The `get_card_embedding_service` function gets an underlying `embedding_service` (Gemini, Mistral) and injects it into a `CardEmbeddingService` instance.
4.  **Card Embedding Service (`pickvocab-server/app/services/card_embedding/service.py`):**
    *   **Key Finding:** The `calculate_and_store_embedding_for_card` method within `CardEmbeddingService` inspects the type of the `GenericCard.card` attribute (`DefinitionCard` or `ContextCard`).
    *   It then calls specific methods (`calculate_and_store_embedding_for_definition_card` or `calculate_and_store_embedding_for_context_card`).
    *   These methods calculate embeddings based on the *content* of the associated `WordDefinition` (derived from `DefinitionCard`) or `WordInContext` (derived from `ContextCard`).
    *   The `store_embedding_for_...` methods ultimately create the embedding entry in the relevant store (`GeminiTextEmbedding004Store`, `MistralEmbedStore`), linking it back (via `content_object`) to the **`WordDefinition` or `WordInContext` instance**, NOT the `GenericCard` instance.

## Conclusion & Refined Plan Rationale

The investigation revealed that checking for an embedding associated with a `GenericCard` requires determining the underlying card type (`DefinitionCard` or `ContextCard`), finding the related `WordDefinition` or `WordInContext` object, and then checking if *that* object has an embedding stored for the target `EmbeddingModel` (based on user preference or default).

---

# Refined Plan Implementation Details

Based on the analysis of `CardEmbeddingService`, embeddings are linked to `WordDefinition` or `WordInContext`, not directly to `GenericCard`.

**1. Model Check (No Changes Needed):**

*   Confirm `WordDefinition` has `GenericRelation` fields: `gemini_text_embedding_004_vector`, `mistral_embed_vector`. (Verified)
*   Confirm `WordInContext` has `GenericRelation` fields: `gemini_text_embedding_004_vector`, `mistral_embed_vector`. (Verified)

**2. Modify `pickvocab-server/app/views.py` (`GenericCardViewSet`)**

*   **Import necessary models and constants:**
    *   `django.db.models import Q`
    *   `django.contrib.contenttypes.models import ContentType`
    *   `rest_framework.decorators import action`
    *   `rest_framework.response import Response`
    *   `rest_framework import status`
    *   `..models import UserEmbeddingPreference, EmbeddingModel, GenericCard, WordDefinition, WordInContext, DefinitionCard, ContextCard`
    *   `..services.card_embedding.constants import DEFAULT_EMBEDDING_MODEL_INFO`
*   **Add a custom action `embedding_status`:**
    *   Use the `@action(detail=True, methods=['get'], url_path='embedding-status')` decorator.
    *   The method signature should be `def embedding_status(self, request, pk=None):`.
*   **Implement the logic inside `embedding_status`:**
    1.  Get the `GenericCard` instance: `card = self.get_object()`. Ensure standard permissions handle object ownership.
    2.  Determine the target `EmbeddingModel`:
        *   Try fetching `UserEmbeddingPreference` for `request.user`.
        *   If found, use `preference.embedding_model`.
        *   If not found or `preference.embedding_model` is None, fetch the default `EmbeddingModel` using `EmbeddingModel.objects.get(provider=DEFAULT_EMBEDDING_MODEL_INFO['provider'], model_name=DEFAULT_EMBEDDING_MODEL_INFO['model_name'])`. Include error handling (`try...except EmbeddingModel.DoesNotExist`) in case the default model isn't in the database, returning an appropriate error response (e.g., 500 Internal Server Error or 404 Not Found with a message).
    3.  Get the specific card instance (`DefinitionCard` or `ContextCard`) from `card.card`.
    4.  Determine the related content object (`WordDefinition` or `WordInContext`):
        *   If `isinstance(card.card, DefinitionCard)`, get `card.card.word_definition`.
        *   If `isinstance(card.card, ContextCard)`, get `card.card.word_in_context`.
        *   Handle potential `None` values if relationships are nullable (though likely not the case here).
    5.  Get the name of the `GenericRelation` field on the content object using a method on the target `EmbeddingModel` instance (assuming a method like `target_model.get_vector_field_name()` exists, which returns the correct field name string like `"gemini_text_embedding_004_vector"` or `"mistral_embed_vector"`). *Self-correction: Need to add this helper method to EmbeddingModel later if it doesn't exist.* 
    6.  Access the related manager on the content object (`word_definition` or `word_in_context`) using `getattr(content_object, vector_field_name)`.
    7.  Check if an embedding exists: `has_embedding = related_manager.exists()`.
    8.  Construct the response payload:
        ```json
        {
            "has_embedding": has_embedding,
            "model_provider": target_model.provider,
            "model_name": target_model.model_name
        }
        ```
    9.  Return `Response(payload, status=status.HTTP_200_OK)`.
