from django.contrib import admin
# from django.contrib.auth.admin import UserAdmin
from .models import CustomUser

# Register your models here.

class CustomUserAdmin(admin.ModelAdmin):
    list_display = ['id', 'username', 'email', 'is_superuser', 'is_staff', 'is_active', 'is_annonymous', 'date_joined', 'last_login']
    list_display_links = ['id', 'username']
    search_fields = ['username', 'email']

admin.site.register(CustomUser, CustomUserAdmin)
