from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.oauth2.client import OAuth2Client
from dj_rest_auth.registration.views import SocialLoginView, RegisterView
from dj_rest_auth.app_settings import api_settings
from dj_rest_auth.models import get_token_model
from dj_rest_auth.serializers import TokenSerializer
from random_username.generate import generate_username
from rest_framework.response import Response
from rest_framework import status
import environ
import os

from accounts.serializers import CustomUserDetailsSerializer
from accounts.utils import generate_password

env = environ.Env()
environ.Env.read_env(os.path.join(os.path.dirname(__file__), '../pickvocab/.env'))

class GoogleLogin(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    callback_url = env("GOOGLE_AUTHORIZED_REDIRECT_URI")
    client_class = OAuth2Client


class ExtensionGoogleLogin(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    callback_url = env("EXTENSION_GOOGLE_AUTHORIZED_REDIRECT_URI")
    client_class = OAuth2Client


class AnnonymousRegisterView(RegisterView):
    def create(self, request, *args, **kwargs):
        # We create an annonymous user here
        username = generate_username()[0]
        password = generate_password()
        data = {
            "username": username,
            "password1": password,
            "password2": password,
            "is_annonymous": True
        }

        # copy code from dj-rest-auth.views.RegisterView
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        user = self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        data = self.get_response_data(user)

        # Custom create token logic by looking at dj-rest-auth.views.LoginView
        token_model = get_token_model()
        if token_model:
            if data is None:
                data = {}
            data["user"] = CustomUserDetailsSerializer(user).data
            data["key"] = TokenSerializer(api_settings.TOKEN_CREATOR(token_model, user, serializer)).data['key']

        if data:
            response = Response(
                data,
                status=status.HTTP_201_CREATED,
                headers=headers,
            )
        else:
            response = Response(status=status.HTTP_204_NO_CONTENT, headers=headers)

        return response
