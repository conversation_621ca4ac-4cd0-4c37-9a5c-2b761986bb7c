from rest_framework import serializers
from dj_rest_auth.registration.serializers import RegisterSerializer
from dj_rest_auth.serializers import UserDetailsSerializer
from allauth.socialaccount.models import SocialAccount

# https://stackoverflow.com/questions/********/how-to-add-custom-user-fields-of-dj-rest-auth-package
class CustomRegisterSerializer(RegisterSerializer):
    is_annonymous = serializers.BooleanField(default=False)

    def get_cleaned_data(self):
        data_dict = super().get_cleaned_data()
        data_dict['is_annonymous'] = self.validated_data.get('is_annonymous', '')
        return data_dict


class SocialAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = SocialAccount
        fields = ('provider', 'uid', 'extra_data')


class CustomUserDetailsSerializer(UserDetailsSerializer):
    social_accounts = SocialAccountSerializer(many=True, read_only=True)

    class Meta(UserDetailsSerializer.Meta):
        fields = UserDetailsSerializer.Meta.fields + ("id", "is_annonymous", 'social_accounts',)

    def get_social_accounts(self, obj):
        return SocialAccountSerializer(obj.socialaccount_set.all(), many=True).data

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        rep['social_accounts'] = self.get_social_accounts(instance)
        return rep
