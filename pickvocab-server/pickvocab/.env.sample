# Database configuration
DB_NAME=pickvocab
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432

# Google authentication
GOOGLE_AUTHORIZED_REDIRECT_URI=http://localhost:3000/auth/google/
EXTENSION_GOOGLE_AUTHORIZED_REDIRECT_URI=https://your-extension-id.chromiumapp.org/

# Pagination
PAGE_SIZE=20

# Backup directory (uncomment and set if needed)
# BACKUP_DIR=/path/to/backup/directory

# Celery configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=django-db

# API Keys
GOOGLE_API_KEY=your_google_api_key
MISTRAL_API_KEY=your_mistral_api_key

# Development settings
DEVELOPMENT=True
DEBUG=True
