import os
from celery import Celery
from celery.signals import task_prerun, task_postrun, setup_logging
import logging
from celery._state import get_current_task

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pickvocab.settings')

app = Celery('pickvocab')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()


# Add task_id to the logger context
class TaskFilter(logging.Filter):
    def filter(self, record):
        task = get_current_task()
        if task and task.request:
            record.task_id = task.request.id
        else:
            record.task_id = "no-task-id"
        return True


@setup_logging.connect
def setup_celery_logging(**kwargs):
    """Set up custom Celery logging."""
    # Get the root logger
    root_logger = logging.getLogger()
    
    # Add task filter to all handlers
    for handler in root_logger.handlers:
        handler.addFilter(TaskFilter())


@task_prerun.connect
def task_prerun_handler(task_id, task, *args, **kwargs):
    """Handler called before a task is run."""
    logger = logging.getLogger('celery.task')
    logger.info(f"Task {task.name}[{task_id}] started with args: {args} kwargs: {kwargs}")


@task_postrun.connect
def task_postrun_handler(task_id, task, *args, retval=None, state=None, **kwargs):
    """Handler called after a task is run."""
    logger = logging.getLogger('celery.task')
    logger.info(f"Task {task.name}[{task_id}] completed with state: {state}")


@app.task(bind=True, ignore_result=True)
def debug_task(self):
    logger = logging.getLogger('celery.task')
    logger.info(f'Request: {self.request!r}') 