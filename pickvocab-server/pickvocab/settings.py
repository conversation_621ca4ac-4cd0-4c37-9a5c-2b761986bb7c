"""
Django settings for pickvocab project.

Generated by 'django-admin startproject' using Django 5.0.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
import environ
import os

env = environ.Env(
    # Set DEBUG=False as default (production)
    DEBUG=(bool, False),
    # Set DEVELOPMENT=False as default (production)
    DEVELOPMENT=(bool, False),
)
environ.Env.read_env()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-tgmpv&f-l)u&_*#u-o*@m94ny9l+4*q+rg4n2!i=a2dqs1f&b+"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env('DEBUG')

# Check if we're in development mode
DEVELOPMENT = env('DEVELOPMENT')

ALLOWED_HOSTS = ["pickvocab.com", "localhost", "127.0.0.1", "api.pickvocab.com", "app.pickvocab.com"]


# Application definition

INSTALLED_APPS = [
    "daphne",

    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.sites",

    # 3rd party apps
    "django_extensions",
    "django.contrib.postgres",

    "rest_framework",
    'django_filters',
    "corsheaders",
    "rest_framework.authtoken",
    "dj_rest_auth",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "dj_rest_auth.registration",
    "allauth.socialaccount.providers.google",

    # Celery
    "django_celery_results",

    # Local
    # "accounts.apps.AccountsConfig",
    "accounts",
    "app.apps.AppConfig",
]

# Add django-silk in development mode only
if DEVELOPMENT:
    INSTALLED_APPS += ["silk"]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "allauth.account.middleware.AccountMiddleware",
]

# Add django-silk middleware in development mode only
if DEVELOPMENT:
    MIDDLEWARE.insert(0, "silk.middleware.SilkyMiddleware")

CORS_ALLOWED_ORIGINS = (
    "http://pickvocab.com",
    "https://pickvocab.com",
    "http://app.pickvocab.com",
    "https://app.pickvocab.com",
    "http://localhost",
    "https://localhost",
    "http://localhost:4173",
    "http://localhost:5173",
    "http://localhost:3000",
    "http://localhost:8000",
)

CSRF_TRUSTED_ORIGINS = ["https://api.pickvocab.com"]

ROOT_URLCONF = "pickvocab.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "django.template.context_processors.request",
            ],
        },
    },
]

WSGI_APPLICATION = "pickvocab.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": env("DB_NAME"),
        "USER": env("DB_USER"),
        "PASSWORD": env("DB_PASSWORD"),
        "HOST": env("DB_HOST"),
        "PORT": env("DB_PORT"),
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = BASE_DIR / "static"

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        # "rest_framework.authentication.SessionAuthentication",
        "rest_framework.authentication.TokenAuthentication",  # new
    ],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": env("PAGE_SIZE"),
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend']
}

AUTH_USER_MODEL = "accounts.CustomUser"

EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

SITE_ID = 1

REST_AUTH = {
    "TOKEN_SERIALIZER": "app.serializers.AppTokenSerializer",  # import path to CustomTokenSerializer defined above.
    "USER_DETAILS_SERIALIZER": "accounts.serializers.CustomUserDetailsSerializer",
    "REGISTER_SERIALIZER": "accounts.serializers.CustomRegisterSerializer",
}

ACCOUNT_ADAPTER = "accounts.adapter.CustomAccountAdapter"

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'custom': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'celery_formatter': {
            'style': '{',
            'format': '{asctime} - {name} - {levelname} - [{task_id}] - {message}',
            'datefmt': '%Y-%m-%d %H:%M:%S',
            'class': 'pickvocab.formatters.SafeTaskFormatter',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG' if DEVELOPMENT else 'INFO',  # DEBUG in development, INFO in production
            'class': 'logging.StreamHandler',
            'formatter': 'custom'
        },
        'file': {
            'level': 'DEBUG' if DEVELOPMENT else 'INFO',  # DEBUG in development, INFO in production
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'app.log',
            'formatter': 'custom',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 3,  # Keep 3 backup files
        },
        'celery_console': {
            'level': 'DEBUG' if DEVELOPMENT else 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'celery_formatter',
        },
        'celery_file': {
            'level': 'DEBUG' if DEVELOPMENT else 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'celery.log',
            'formatter': 'celery_formatter',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 3,  # Keep 3 backup files
        },
    },
    'loggers': {
        # Django Request Logger
        'django.request': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        # Django SQL Logger
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'INFO',  # Set to INFO to avoid too many SQL logs
            'propagate': False,
        },
        # App loggers - this will catch all logs from app modules
        'app': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': True,
        },
        # All services logger with combined handlers for both regular and celery contexts
        'app.services': {
            'handlers': ['console', 'file', 'celery_console', 'celery_file'],
            'level': 'DEBUG',
            'propagate': False,  # Don't propagate to app to avoid duplicate logs
        },
        # Celery task logger
        'celery.task': {
            'handlers': ['celery_console', 'celery_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        # Celery worker logger
        'celery.worker': {
            'handlers': ['celery_console', 'celery_file'],
            'level': 'INFO',
            'propagate': False,
        },
        # Root logger
        '': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
        },
    },
}

ASGI_APPLICATION = "pickvocab.asgi.application"

# Celery Configuration
CELERY_BROKER_URL = os.environ.get("CELERY_BROKER_URL", "redis://redis:6379/0")
CELERY_RESULT_BACKEND = os.environ.get("CELERY_RESULT_BACKEND", "django-db")
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = "UTC"
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60  # 30 minutes

# Django Silk Configuration - only in development mode
if DEVELOPMENT:
    # Ensure silk_profiles directory exists for profiler results
    SILK_PROFILER_DIR = BASE_DIR / 'silk_profiles'
    if not SILK_PROFILER_DIR.exists():
        os.makedirs(SILK_PROFILER_DIR, exist_ok=True)

    SILKY_PYTHON_PROFILER = True
    SILKY_PYTHON_PROFILER_BINARY = True
    SILKY_META = True
    SILKY_INTERCEPT_PERCENT = 100  # Log all requests

    # Only allow superusers to access the Silk interface
    SILKY_PERMISSIONS = lambda user: user.is_superuser

    # Limit request/response body sizes to avoid performance issues
    SILKY_MAX_REQUEST_BODY_SIZE = 1024  # 1KB
    SILKY_MAX_RESPONSE_BODY_SIZE = 1024  # 1KB

    # Garbage collection settings
    SILKY_MAX_RECORDED_REQUESTS = 10000
    SILKY_MAX_RECORDED_REQUESTS_CHECK_PERCENT = 10

    # Store profiler results in silk_profiles folder
    SILKY_PYTHON_PROFILER_RESULT_PATH = SILK_PROFILER_DIR # Use the defined variable

