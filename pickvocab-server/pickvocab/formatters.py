"""
Custom formatters for logging.
"""
import logging


class SafeTaskFormatter(logging.Formatter):
    """
    A formatter that handles task_id values safely.
    
    If a log record doesn't have a task_id attribute, it will use a default value
    instead of raising an error.
    """
    
    def __init__(self, fmt=None, datefmt=None, style='{', defaults=None):
        super().__init__(fmt, datefmt, style, defaults)
    
    def format(self, record):
        # Add a default task_id if not present
        if not hasattr(record, 'task_id'):
            record.task_id = 'no-task-id'
        return super().format(record) 