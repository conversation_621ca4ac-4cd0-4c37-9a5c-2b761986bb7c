# Builder stage
FROM python:3.11-slim AS builder

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

RUN apt-get update && apt-get install --no-install-recommends -y curl build-essential git libpq-dev

RUN pip install --no-cache-dir poetry==1.7.1

WORKDIR /app

COPY pyproject.toml poetry.lock ./

RUN poetry config virtualenvs.in-project true && poetry install --no-dev # Install only production dependencies

# Runner stage
FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

RUN apt-get update && apt-get install --no-install-recommends -y libpq-dev

WORKDIR /app

COPY --from=builder /app/.venv ./.venv
ENV PATH="/app/.venv/bin:${PATH}"

COPY . .

EXPOSE 8000

# Default command is to run the Django server
# This can be overridden in docker-compose.yml for Celery workers and beat
<PERSON><PERSON> ["daphne", "-b", "0.0.0.0", "-p", "8000", "pickvocab.asgi:application"]