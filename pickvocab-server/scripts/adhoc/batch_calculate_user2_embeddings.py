#!/usr/bin/env python
"""
Script to calculate and store embeddings for all cards of user with ID 2.

Run this script with:
    python scripts/adhoc/batch_calculate_user2_embeddings.py
"""
import os
import sys
import time
import django
from typing import List, <PERSON><PERSON>

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pickvocab.settings')
django.setup()

# Import models and services
from app.models import GenericCard
from app.services.card_embedding import get_card_embedding_service, CardEmbeddingService
from app.services.card_embedding.prefetching import prefetch_related_objects


USER_ID = 2
BATCH_SIZE = 100

def get_generic_cards(offset: int = 0, limit: int = BATCH_SIZE) -> List[GenericCard]:
    """Get a batch of generic cards of the specified user with related objects prefetched."""
    generic_cards = GenericCard.objects.filter(
        owner_id=USER_ID
    ).select_related('card_content_type').order_by('id')[offset:offset+limit]

    # Prefetch related objects for better performance
    generic_cards = prefetch_related_objects(list(generic_cards))

    return generic_cards

def calculate_and_store_embeddings(
    cards: List[GenericCard], 
    card_embedding_service: CardEmbeddingService
) -> Tuple[int, int]:
    """Calculate and store embeddings for a list of cards.
    
    Returns:
        Tuple containing (number of successful cards, number of failed cards)
    """
    total_cards = len(cards)
    print(f"Calculating embeddings for {total_cards} cards")

    # Use batch processing for better performance
    print("Processing cards in batch...")
    start_time = time.time()

    try:
        batch_result = card_embedding_service.calculate_and_store_embeddings_for_multiple_cards(cards)
        if isinstance(batch_result, tuple) and len(batch_result) == 4:
            successful_count, error_map, definition_map, context_map = batch_result
        else:
            print(f"Unexpected result format: {type(batch_result)}")
            successful_count, error_map, definition_map, context_map = 0, {}, {}, {}
    except Exception as e:
        print(f"Error processing cards: {str(e)}")
        successful_count, error_map, definition_map, context_map = 0, {getattr(card, 'id', i): str(e) for i, card in enumerate(cards)}, {}, {}

    end_time = time.time()
    duration = end_time - start_time

    print(f"Successfully processed {successful_count} cards in {duration:.2f} seconds")
    if successful_count > 0:
        print(f"Average time per successful card: {duration/successful_count:.4f} seconds")

    # Print summary statistics
    new_def_count = sum(1 for _, _, is_new in definition_map.values() if is_new)
    existing_def_count = len(definition_map) - new_def_count
    new_context_count = sum(1 for _, _, is_new in context_map.values() if is_new)
    existing_context_count = len(context_map) - new_context_count

    print(f"Definition cards: {new_def_count} new embeddings, {existing_def_count} existing embeddings")
    print(f"Context cards: {new_context_count} new embeddings, {existing_context_count} existing embeddings")

    # Log any failures
    if error_map:
        print(f"Warning: {len(error_map)} cards failed to process")

    return successful_count, len(error_map)

def run() -> None:
    """Main function to run the script."""
    print(f"Starting batch card embedding calculation for user ID: {USER_ID}")

    try:
        # Initialize the card embedding service
        print("Initializing embedding service...")
        card_embedding_service = get_card_embedding_service()

        offset = 0
        total_processed = 0
        total_successful = 0
        total_failed = 0
        
        while True:
            # Get a batch of generic cards
            print(f"Fetching cards at offset {offset} (batch size: {BATCH_SIZE})...")
            generic_cards = get_generic_cards(offset, BATCH_SIZE)
            
            if not generic_cards:
                print("No more cards to process.")
                break
                
            batch_count = len(generic_cards)
            print(f"Found {batch_count} cards in this batch")
            
            # Process batch
            successful, failed = calculate_and_store_embeddings(generic_cards, card_embedding_service)
            
            # Update stats
            total_processed += batch_count
            total_successful += successful
            total_failed += failed
            
            # Move to next batch
            offset += BATCH_SIZE
            
            print(f"Batch completed. Progress: {total_processed} cards processed\n")

        print(f"Processing completed. Total: {total_processed} cards processed")
        print(f"Success: {total_successful} cards, Failed: {total_failed} cards")

    except Exception as e:
        print(f"An error occurred: {e}")
        raise

if __name__ == "__main__":
    run() 