from django.contrib.contenttypes.models import ContentType
from app.models import Card, DeckCard2, Generic<PERSON>ard, DefinitionCard, Deck, Deck2

# TODO: Move decks to deck2s
def up():
    deck_mapping = {}

    # DefinitionCard
    DefinitionCard._meta.get_field('created_at').auto_now_add = False
    DefinitionCard._meta.get_field('updated_at').auto_now = False

    # GenericCard
    GenericCard._meta.get_field('created_at').auto_now_add = False
    GenericCard._meta.get_field('updated_at').auto_now = False

    # Deck2
    Deck2._meta.get_field('created_at').auto_now_add = False
    Deck2._meta.get_field('updated_at').auto_now = False

    # DeckCard2
    DeckCard2._meta.get_field('created_at').auto_now_add = False
    DeckCard2._meta.get_field('updated_at').auto_now = False

    for card in Card.objects.prefetch_related('decks').order_by('created_at').iterator(chunk_size=300):
        print(f"Processing Card: \"{card.word}\" (ID: {card.id})")
        definition_card = DefinitionCard.objects.create(
            word=card.word,
            word_ref=card.word_ref,
            word_def_idx=card.word_def_idx,
            definition=card.definition,
            created_at=card.created_at,
            updated_at=card.updated_at
        )
        print(f"  Created DefinitionCard: \"{definition_card.word}\" (ID: {definition_card.id})")

        generic_card = GenericCard.objects.create(
            card_content_type=ContentType.objects.get_for_model(definition_card),
            card_object_id=definition_card.id,
            owner=card.owner,
            progress_score=card.progress_score,
            created_at=card.created_at,
            updated_at=card.updated_at
        )
        print(f"  Created GenericCard: ID {generic_card.id} for DefinitionCard {definition_card.id}")

        decks = card.decks.all()
        for deck in decks:
            if deck.id not in deck_mapping:
                deck2 = Deck2.objects.create(
                    name=deck.name,
                    description=deck.description,
                    owner=deck.owner,
                    is_demo=deck.is_demo,
                    created_at=deck.created_at,
                    updated_at=deck.updated_at
                )
                deck_mapping[deck.id] = deck2
                print(f"  Created Deck2: {deck2.name} (ID: {deck2.id})")
            else:
                deck2 = deck_mapping[deck.id]

            DeckCard2.objects.create(
                deck=deck2,
                card=generic_card,
                created_at=card.created_at,
                updated_at=card.updated_at
            )
            print(f"  Created DeckCard2 linking Deck2 {deck2.id} and GenericCard {generic_card.id}")

        # DefinitionCard
    DefinitionCard._meta.get_field('created_at').auto_now_add = True
    DefinitionCard._meta.get_field('updated_at').auto_now = True

    # GenericCard
    GenericCard._meta.get_field('created_at').auto_now_add = True
    GenericCard._meta.get_field('updated_at').auto_now = True

    # Deck2
    Deck2._meta.get_field('created_at').auto_now_add = True
    Deck2._meta.get_field('updated_at').auto_now = True

    # DeckCard2
    DeckCard2._meta.get_field('created_at').auto_now_add = True
    DeckCard2._meta.get_field('updated_at').auto_now = True


def down():
    DeckCard2.objects.all().delete()
    Deck2.objects.all().delete()
    GenericCard.objects.all().delete()
    DefinitionCard.objects.all().delete()


def run():
    up()