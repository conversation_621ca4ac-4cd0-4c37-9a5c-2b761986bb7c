#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to calculate and store embeddings for cards.

This script queries the first 5 generic cards belonging to user with ID 132,
then calculates and stores embeddings for them.

Run this script with:
    python scripts/adhoc/calculate_card_embeddings.py
"""
import os
import sys
import django
from typing import List, Union

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pickvocab.settings')
django.setup()

# Import models and services
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model
from app.models import GenericCard, DefinitionCard, ContextCard
from app.services.card_embedding import get_card_embedding_service, CardEmbeddingService
from app.services.card_embedding.prefetching import prefetch_related_objects
from app.services.word_embedding_service import WordEmbeddingService

User = get_user_model()

USER_ID = 132
NUM_CARDS = 100
TEST_QUERY = "I think we should release the patch to temporarily fix the issue first, then in the meantime, we can try to find a more permanent solution."
TEST_LIMIT = 15


def get_generic_cards() -> List[GenericCard]:
    """Get the first NUM_CARDS generic cards of the specified user with related objects prefetched."""
    # Query GenericCards that belong to the specified user
    # Use select_related for card_content_type to avoid additional queries
    generic_cards = GenericCard.objects.filter(
        owner_id=USER_ID
    ).select_related('card_content_type').order_by('-id')[:NUM_CARDS]

    # Prefetch related objects for better performance
    generic_cards = prefetch_related_objects(list(generic_cards))

    return generic_cards


def calculate_and_store_embeddings(
    cards: List[Union[GenericCard, DefinitionCard, ContextCard]],
    card_embedding_service: CardEmbeddingService
) -> None:
    """Calculate and store embeddings for a list of cards."""
    total_cards = len(cards)
    print(f"Calculating embeddings for {total_cards} cards")

    # Use batch processing for better performance
    print("Processing cards in batch...")
    start_time = __import__('time').time()

    try:
        batch_result = card_embedding_service.calculate_and_store_embeddings_for_multiple_cards(cards)
        if isinstance(batch_result, tuple) and len(batch_result) == 4:
            successful_count, error_map, definition_map, context_map = batch_result
        else:
            print(f"Unexpected result format: {type(batch_result)}")
            successful_count, error_map, definition_map, context_map = 0, {}, {}, {}
    except Exception as e:
        print(f"Error processing cards: {str(e)}")
        successful_count, error_map, definition_map, context_map = 0, {getattr(card, 'id', i): str(e) for i, card in enumerate(cards)}, {}, {}

    end_time = __import__('time').time()
    duration = end_time - start_time

    print(f"Successfully processed {successful_count} cards in {duration:.2f} seconds")
    if successful_count > 0:
        print(f"Average time per successful card: {duration/successful_count:.4f} seconds")

    # Print details for each card
    for i, card in enumerate(cards):
        card_type = card.__class__.__name__
        card_id = getattr(card, 'id', f'unknown-{i}')

        if card_id in error_map:
            status = f"Failed: {error_map[card_id]}"
        else:
            if card_id in definition_map:
                word_def, _, is_new = definition_map[card_id]
                word = word_def.word.word if word_def.word else "unknown"
                new_status = "New" if is_new else "Existing"
                status = f"Success - Definition: {word} ({new_status} embedding)"
            elif card_id in context_map:
                word_in_context, _, is_new = context_map[card_id]
                word = word_in_context.word if word_in_context else "unknown"
                new_status = "New" if is_new else "Existing"
                status = f"Success - Context: {word} ({new_status} embedding)"
            else:
                status = "Success"

        print(f"Card {i+1}/{total_cards}: {card_type} (ID: {card_id}) - {status}")

    # Count new vs existing embeddings
    new_def_count = sum(1 for _, _, is_new in definition_map.values() if is_new)
    existing_def_count = len(definition_map) - new_def_count
    new_context_count = sum(1 for _, _, is_new in context_map.values() if is_new)
    existing_context_count = len(context_map) - new_context_count

    print(f"Definition cards: {new_def_count} new embeddings, {existing_def_count} existing embeddings")
    print(f"Context cards: {new_context_count} new embeddings, {existing_context_count} existing embeddings")

    # Log any failures
    if error_map:
        print(f"Warning: {len(error_map)} cards failed to process")
        print("Failure details:")
        for card_id, reason in error_map.items():
            print(f"  Card ID {card_id}: {reason}")


def test_similarity_search(card_embedding_service: CardEmbeddingService, user_id: int) -> None:
    """Test similarity search functionality with the given query."""
    print(f"\nTesting similarity search with query: '{TEST_QUERY}'")

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        print(f"Error: User with ID {user_id} does not exist")
        return

    # Perform similarity search
    print(f"Searching for cards similar to '{TEST_QUERY}' (limit: {TEST_LIMIT})...")

    try:
        results = card_embedding_service.similarity_search(
            query=TEST_QUERY,
            user=user,
            limit=TEST_LIMIT
        )

        # Print results
        print(f"Found {len(results)} similar cards")

        if results:
            print("\nTop results:")
            for i, result in enumerate(results):
                generic_card = result['generic_card']
                score = result['similarity_score']

                # Get card details
                card = generic_card.card
                card_type = card.__class__.__name__

                # Get word details based on card type
                if card_type == "DefinitionCard":
                    word = card.word_ref.word if card.word_ref else card.word
                elif card_type == "ContextCard":
                    word = card.word_in_context.word if card.word_in_context else "unknown"
                else:
                    word = "unknown"

                print(f"{i+1}. {word} ({card_type}, ID: {generic_card.id}) - Score: {score:.4f}")
        else:
            print("No similar cards found")

    except Exception as e:
        print(f"Error performing similarity search: {str(e)}")


def run():
    """Main function to run the script."""
    print(f"Starting card embedding calculation for user ID: {USER_ID}")

    try:
        # Initialize the card embedding service
        print("Initializing embedding service...")
        card_embedding_service = get_card_embedding_service()

        # Get generic cards
        print(f"Fetching first {NUM_CARDS} generic cards...")
        generic_cards = get_generic_cards()
        print(f"Found {len(generic_cards)} generic cards")

        if not generic_cards:
            print(f"No cards found for user ID {USER_ID}")
            return

        # Process all cards
        print("Processing cards...")
        calculate_and_store_embeddings(generic_cards, card_embedding_service)

        # Test similarity search functionality
        test_similarity_search(card_embedding_service, USER_ID)

        print("Script execution completed successfully")

    except Exception as e:
        print(f"An error occurred: {e}")
        raise


if __name__ == "__main__":
    run()