from app.models import Word, VerifiedWord

def up():
    for verified_word in VerifiedWord.objects.iterator():
        print(f"Moving {verified_word.word}")
        word = Word.objects.create(
            word=verified_word.word,
            definitions=verified_word.definitions,
            creator=verified_word.creator,
            llm_model=verified_word.llm_model,
            is_verified=True,
        )
        word.save()
        print(f"Done {verified_word.word}")


def down():
    raise Exception('To be implemented')


def run():
    up()