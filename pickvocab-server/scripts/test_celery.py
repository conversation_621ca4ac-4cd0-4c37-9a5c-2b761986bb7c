#!/usr/bin/env python
"""
Scrip<PERSON> to test the Celery setup.
Run this script with:
    python scripts/test_celery.py
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pickvocab.settings')
django.setup()

# Import the tasks
from app.tasks import example_task
from app.services.card_embedding.tasks import calculate_and_store_embedding_for_card

def test_celery():
    """Test the Celery setup by running a task."""
    print("Sending task to Celery...")
    result = example_task.delay("test parameter")
    print(f"Task sent with ID: {result.id}")
    print("Check the Celery worker logs to see the task execution.")

def test_card_embedding():
    """Test the card embedding task for a specific generic card."""
    card_id = 91
    print(f"Sending card embedding task for generic card ID: {card_id}...")
    
    result = calculate_and_store_embedding_for_card.delay(
        card_id=card_id,
    )
    
    print(f"Task sent with ID: {result.id}")
    print("Check the Celery worker logs to see the task execution.")
    

if __name__ == "__main__":
    # Uncomment the function you want to test
    # test_celery()
    test_card_embedding() 