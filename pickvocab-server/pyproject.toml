[tool.poetry]
name = "pickvocab"
version = "0.1.0"
description = ""
authors = ["phu<PERSON><PERSON><PERSON><PERSON><PERSON> <phu<PERSON><PERSON><PERSON><PERSON><EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
djangorestframework = "^3.15.1"
django = "^5.0.4"
django-environ = "^0.11.2"
psycopg = "^3.1.18"
dj-rest-auth = {extras = ["with-social"], version = "^6.0.0"}
django-cors-headers = "^4.3.1"
psycopg2 = "^2.9.9"
django-extensions = "^3.2.3"
random-username = "^1.0.2"
django-filter = "^24.3"
python-slugify = "^8.0.4"
daphne = "^4.1.2"
celery = "^5.4.0"
redis = "^5.2.1"
django-celery-results = "^2.5.1"
flower = "^2.0.1"
pgvector = "^0.4.0"
django-silk = "^5.3.2"
langchain = "^0.3.21"
langchain-community = "^0.3.20"
langchain-google-genai = "^2.1.0"
langchain-mistralai = "^0.2.8"


[tool.poetry.group.dev.dependencies]
ipython = "^8.26.0"
ipdb = "^0.13.13"
django-types = "^0.19.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
indent-width = 4