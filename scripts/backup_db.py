import os
import subprocess
import datetime
import glob
import environ
import argparse

# pg_dump -U postgres -d my_database -t my_table -f table_backup.sql

env = environ.Env()
environ.Env.read_env(os.path.join(os.path.dirname(__file__), '.env'))
current_dir = os.path.dirname(os.path.abspath(__file__))

# Configuration
DB_NAME=env("DB_NAME")
DB_USER=env("DB_USER")
DB_CONTAINER_NAME=env("DB_CONTAINER_NAME", default="pickvocab-db")

BACKUP_DIR=env("BACKUP_DIR", default="/backup")
BACKUP_DIR_PATH = os.path.join(current_dir, BACKUP_DIR)
REMOTE_BACKUP_PATH=env("REMOTE_BACKUP_PATH", default="onedrive:/backup")

MIN_BACKUPS = 3

# Get current date and time for backup file name
date_str = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
backup_filename = f"{DB_NAME}-backup-{date_str}.sql"
backup_filepath = os.path.join(BACKUP_DIR_PATH, backup_filename)

# Create backup using pg_dump
def create_backup(use_docker=False):
    try:
        if use_docker:
            print(f"Create backup in Docker: {backup_filename}")
            subprocess.run(["docker", "exec", "-t", DB_CONTAINER_NAME, "pg_dump", "-U", DB_USER, "-d", DB_NAME, "-f", backup_filepath], check=True)
        else:
            print(f"Creating backup: {backup_filename}")
            subprocess.run(["pg_dump", "-U", DB_USER, "-d", DB_NAME, "-f", backup_filepath], check=True)

        # Compress the backup file
        subprocess.run(["gzip", backup_filepath], check=True)
        print(f"Backup created: {backup_filename}.gz")
    except subprocess.CalledProcessError as e:
        print(f"Error creating backup: {e}")
        return False
    return True


# Delete oldest backups if there are more than MIN_BACKUPS
def clean_old_backups():
    # List all backups in the directory, sorted by creation time
    backup_files = sorted(
        glob.glob(os.path.join(BACKUP_DIR_PATH, "*.sql.gz")),
        key=os.path.getctime
    )

    # Check if there are more backups than MIN_BACKUPS
    while len(backup_files) > MIN_BACKUPS:
        oldest_backup = backup_files.pop(0)
        try:
            print(f"Deleting oldest backup: {oldest_backup}")
            os.remove(oldest_backup)
        except Exception as e:
            print(f"Error deleting {oldest_backup}: {e}")
            raise e


def sync_backups():
    # Sync backups to OneDrive using rclone
    try:
        subprocess.run(["rclone", "sync", BACKUP_DIR_PATH, REMOTE_BACKUP_PATH], check=True)
        print(f"Backups synced to remote location: {REMOTE_BACKUP_PATH}")
    except subprocess.CalledProcessError as e:
        print(f"Error syncing backups to remote location: {e}")
        raise e


# Main function
if __name__ == "__main__":
    # Set up argument parser
    parser = argparse.ArgumentParser(description="PostgreSQL Backup Script")
    parser.add_argument('--docker', action='store_true', help="Run pg_dump in Docker")
    args = parser.parse_args()

    if create_backup(args.docker):
        clean_old_backups()
        sync_backups()
