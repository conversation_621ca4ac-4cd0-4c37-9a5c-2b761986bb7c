# Deploy

## Production

- Up versions
- Tag versions
- Build images
- Save images to tar

### On fresh machine
```sh
$ ansible-playbook -i inventory.ini site.yml
$ ansible-playbook -i inventory.ini playbooks/empty_database.yml
$ ansible-playbook -i inventory.ini playbooks/load_db_backup.yml
```

**Important:** Ensure that the VPS IP address is allowed in the Cloudflare firewall rules. This is necessary for requests from the client server to the backend server (both hosted on the same VPS).

- For guidance, refer to this [Cloudflare Community post](https://community.cloudflare.com/t/how-to-whitelist-my-own-bot/398299/2).
- It is recommended to allowlist the origin host/web server IP in Cloudflare by navigating to **Security → WAF → Tools → IP Access Rules** and setting the action to "allow" for your website.

### Deploy next version
- Build docker images on local
- Adjust the `docker_images` var (if you want to deploy next version of the client only, comment the other ones)

```sh
$ ansible-playbook -i inventory.ini site.yml --limit ovh-vps --tags deploy
$ ansible-playbook -i inventory.ini site.yml --limit ovh-vps --tags containers
```

- Revert the `docker_images` var to the original

## Local
On x86, build x86 images:
```sh
$ docker compose build
```

On Mac, build ARM images:
```sh
$ docker compose -f docker-compose-arm.yml build
```

Save images to tar files:
```sh
$ docker save -o pickvocab-"<service-name>".tar pickvocab-"<service-name>":"<version>"
# example:
$ docker save -o pickvocab-backend.tar pickvocab-backend:v1.6.10
```

```sh
$ vagrant up
# Then same commands as production but with --limit nodes
# Example:
$ ansible-playbook -i inventory.ini site.yml --limit nodes --tags deploy
```

SSH to local VM to check:
```sh
$ ssh -i ~/.ssh/id_ed25519 duy@127.0.0.1 -p 2222
$ docker ps -a
```

Add these lines to your local machine `/etc/hosts`:
```
127.0.0.1 pickvocab.com
127.0.0.1 app.pickvocab.com
127.0.0.1 api.pickvocab.com
127.0.0.1 flower.pickvocab.com
```

Then open browser, go to pickvocab.com to check