---
- name: Ensure target directories exist
  ansible.builtin.file:
    path: "{{ app_path }}"
    state: directory
    owner: "{{ user }}"
    group: "{{ user }}"
    mode: "{{ app_dir_mode }}"
  become: true
  tags: ['setup']

- name: Deploy Docker images
  block:
    - name: Copy Docker image files
      ansible.builtin.copy:
        src: "{{ pickvocab_dir_path }}/{{ item }}.tar"
        dest: "{{ app_path }}/{{ item }}.tar"
        owner: "{{ user }}"
        group: "{{ user }}"
        mode: '0644'
      loop: "{{ docker_images }}"
      register: image_copy
      tags: ['images']

    - name: Load Docker images
      command: "docker load -i {{ app_path }}/{{ item }}.tar"
      loop: "{{ docker_images }}"
      when: image_copy.changed
      changed_when: true
      tags: ['images']

    - name: Clean up image tar files
      ansible.builtin.file:
        path: "{{ app_path }}/{{ item }}.tar"
        state: absent
      loop: "{{ docker_images }}"
      tags: ['cleanup']
  become: true
  become_user: "{{ user }}"
  tags: ['deploy'] 