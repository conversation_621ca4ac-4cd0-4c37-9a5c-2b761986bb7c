---
# Database backup role tasks

- name: Ensure backup directory exists
  file:
    path: "{{ backup_dir }}"
    state: directory
    owner: "{{ user }}"
    group: "{{ user }}"
    mode: '0755'
  become: true

- name: Ensure backup log directory exists
  file:
    path: "{{ backup_log_dir }}"
    state: directory
    owner: "{{ user }}"
    group: "{{ user }}"
    mode: '0755'
  become: true

- name: Ensure backup scripts are executable
  file:
    path: "{{ item }}"
    mode: '0755'
    state: file
  loop:
    - "{{ app_path }}/scripts/backup.sh"
    - "{{ app_path }}/scripts/backup_db.py"
  become: true

- name: Create test .env file for verification
  template:
    src: backup-test.env.j2
    dest: "{{ app_path }}/scripts/.env"
    owner: "{{ user }}"
    group: "{{ user }}"
    mode: '0600'
  become: true

- name: Run backup script immediately to verify functionality
  command: "{{ app_path }}/scripts/backup.sh"
  register: backup_result
  become: true
  become_user: "{{ user }}"

- name: Display backup script output
  debug:
    msg: "{{ backup_result.stdout_lines }}"

- name: Check if backup file was created
  find:
    paths: "{{ backup_dir }}"
    patterns: "*.sql.gz"
    age: "-10m"
  register: recent_backups

- name: Verify backup was successful
  debug:
    msg: "Backup successful! Found {{ recent_backups.files | length }} recent backup files."
  failed_when: recent_backups.files | length == 0

- name: Create production .env file for database backup
  template:
    src: backup.env.j2
    dest: "{{ app_path }}/scripts/.env"
    owner: "{{ user }}"
    group: "{{ user }}"
    mode: '0600'
  become: true

- name: Set up cron job for database backup
  cron:
    name: "Database backup for {{ app_name }}"
    user: "{{ user }}"
    job: "{{ app_path }}/scripts/backup.sh >> {{ backup_log_dir }}/backup_$(date +\\%Y\\%m\\%d).log 2>&1"
    hour: "{{ backup_hour }}"
    minute: "{{ backup_minute }}"
    state: present
  become: true

- name: Set up cron job for backup cleanup
  cron:
    name: "Clean old database backups for {{ app_name }}"
    user: "{{ user }}"
    job: "find {{ backup_dir }} -name '*.sql.gz' -type f -mtime +{{ backup_retention_days }} -delete >> {{ backup_log_dir }}/cleanup_$(date +\\%Y\\%m\\%d).log 2>&1"
    hour: "4"
    minute: "0"
    state: present
  become: true

- name: Set up log rotation for backup logs
  template:
    src: backup-logrotate.j2
    dest: /etc/logrotate.d/{{ app_name }}-backup
    owner: root
    group: root
    mode: '0644'
  become: true

- name: Verify cron jobs are set up correctly
  shell: crontab -l -u {{ user }}
  register: crontab_output
  become: true
  changed_when: false
  
- name: Display configured cron jobs
  debug:
    msg: "{{ crontab_output.stdout_lines }}"
    
- name: Verify backup cron job exists
  assert:
    that: "crontab_output.stdout is search('Database backup for {{ app_name }}')"
    fail_msg: "Backup cron job was not found in crontab"
    success_msg: "Backup cron job was successfully configured"
    
- name: Verify cleanup cron job exists
  assert:
    that: "crontab_output.stdout is search('Clean old database backups for {{ app_name }}')"
    fail_msg: "Cleanup cron job was not found in crontab"
    success_msg: "Cleanup cron job was successfully configured" 