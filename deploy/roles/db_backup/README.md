# Database Backup Role

This role sets up automated database backups for the {{ app_name }} application.

## What this role does

1. Creates the backup directory if it doesn't exist
2. Creates a log directory for backup logs
3. Creates the `.env` file with necessary environment variables for the backup scripts
4. Ensures backup scripts are executable
5. Sets up a cron job to run the backup script at the configured time
6. Sets up a cron job to clean up old backups based on retention policy
7. Configures log rotation for backup logs

## Requirements

- The backup scripts (`backup.sh` and `backup_db.py`) must already exist in the `{{ app_path }}/scripts/` directory
- Docker must be installed and running
- The database container must be running

## Variables

| Variable | Default | Description |
|----------|---------|-------------|
| backup_hour | 3 | Hour to run the backup (24-hour format) |
| backup_minute | 0 | Minute to run the backup |
| backup_retention_days | 7 | Number of days to keep backups |
| backup_dir | /backup | Directory to store backups |
| backup_log_dir | /var/log/{{ app_name }}/backups | Directory to store backup logs |
| db_name | pickvocab | Database name |
| db_user | duy | Database user |
| db_container_name | pickvocab-db | Database container name |

## Usage

Include this role in your playbook:

```yaml
- name: Set up database backup
  hosts: your_hosts
  roles:
    - role: db_backup
      tags: ['backup', 'database']
```

## Customization

You can override any of the default variables in your playbook:

```yaml
- name: Set up database backup
  hosts: your_hosts
  roles:
    - role: db_backup
      backup_hour: "1"
      backup_minute: "30"
      backup_retention_days: 14
      backup_log_dir: "/var/log/custom/backups"
      tags: ['backup', 'database']
```

## Logs

Backup logs are stored in the `backup_log_dir` directory with filenames based on the date:
- `backup_YYYYMMDD.log` - Output from the backup script
- `cleanup_YYYYMMDD.log` - Output from the cleanup process

Log rotation is configured to keep logs for 14 days, compressed after the first day. 