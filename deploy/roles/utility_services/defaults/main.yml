---
# ACME.sh configuration
acme_local_config_path: "/Users/<USER>/.acme.sh"  # Local path to ACME.sh configuration
acme_remote_config_path: ".acme.sh"                # Remote path relative to user's home
acme_dir_mode: '0700'                             # Directory permissions for ACME.sh

# Database backup defaults
db_container_name: pickvocab-db
db_name: pickvocab
db_user: duy
backup_dir: /home/<USER>/pickvocab/backups
container_temp_dir: /tmp

# Pyenv configuration
pyenv_python_version: "3.11.9"                    # Python version to install with pyenv

# Note: Rclone configuration has been moved to group_vars/all/rclone.yml