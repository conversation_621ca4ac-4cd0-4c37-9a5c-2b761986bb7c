# rclone configuration file
# Generated by Ansible

{% if rclone_onedrive is defined %}
[onedrive]
type = {{ rclone_onedrive.type }}
client_id = {{ rclone_onedrive.client_id }}
client_secret = {{ rclone_onedrive.client_secret }}
token = {{ rclone_onedrive.token }}
drive_id = {{ rclone_onedrive.drive_id }}
drive_type = {{ rclone_onedrive.drive_type }}
{% endif %}

{# Add more remote configurations as needed #}
{% if rclone_gdrive is defined %}
[gdrive]
type = {{ rclone_gdrive.type }}
client_id = {{ rclone_gdrive.client_id }}
client_secret = {{ rclone_gdrive.client_secret }}
token = {{ rclone_gdrive.token }}
{% endif %} 