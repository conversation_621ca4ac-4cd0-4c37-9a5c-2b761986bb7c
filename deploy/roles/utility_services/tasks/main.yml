---
- name: Configure ACME.sh
  block:
    - name: Create ACME.sh directory
      ansible.builtin.file:
        path: "/home/<USER>/{{ acme_remote_config_path }}"
        state: directory
        owner: "{{ user }}"
        group: "{{ user }}"
        mode: "{{ acme_dir_mode }}"

    - name: Compress ACME.sh configuration locally
      ansible.builtin.shell: tar -czf /tmp/acme_config.tar.gz -C {{ acme_local_config_path }} .
      delegate_to: localhost
      changed_when: false
      become: false

    - name: Copy compressed ACME.sh configuration
      ansible.builtin.copy:
        src: /tmp/acme_config.tar.gz
        dest: "/home/<USER>/acme_config.tar.gz"
        owner: "{{ user }}"
        group: "{{ user }}"
        mode: "0644"

    - name: Extract ACME.sh configuration
      ansible.builtin.shell: tar -xzf "/home/<USER>/acme_config.tar.gz" -C "/home/<USER>/{{ acme_remote_config_path }}"
      become_user: "{{ user }}"

    - name: Remove temporary archive
      ansible.builtin.file:
        path: "/home/<USER>/acme_config.tar.gz"
        state: absent

    - name: Remove local temporary archive
      ansible.builtin.file:
        path: /tmp/acme_config.tar.gz
        state: absent
      delegate_to: localhost
      changed_when: false
      become: false
    - name: Set correct ownership
      ansible.builtin.file:
        path: "/home/<USER>/{{ acme_remote_config_path }}"
        state: directory
        owner: "{{ user }}"
        group: "{{ user }}"
        recurse: yes
  become: true
  tags: ['acme', 'ssl']


- name: Install ACME.sh dependencies
  apt:
    name:
      - curl
      - socat
    state: present
    update_cache: yes
  become: true
  tags: ['acme', 'ssl', 'dependencies']

- name: Install ACME.sh script
  ansible.builtin.shell: >
    curl https://get.acme.sh | sh -s email={{ cloudflare_email }}
  args:
    creates: "/home/<USER>/.acme.sh/acme.sh"
  become: true
  become_user: "{{ user }}"
  changed_when: false # Installation script manages its own change status
  tags: ['acme', 'ssl', 'install']

- name: Issue certificate using ACME.sh and Cloudflare DNS
  ansible.builtin.command: >
    /home/<USER>/.acme.sh/acme.sh --issue --dns dns_cf -d pickvocab.com -d '*.pickvocab.com'
  args:
    creates: "/home/<USER>/.acme.sh/pickvocab.com/pickvocab.com.cer"
  environment:
    CF_Key: "{{ cloudflare_api_key }}"
    CF_Email: "{{ cloudflare_email }}"
  become: true
  become_user: "{{ user }}"
  changed_when: true # Assume changed if command runs (idempotency via 'creates')
  no_log: true # Hide sensitive environment variables from logs
  tags: ['acme', 'ssl', 'issue']


- name: Install pyenv and Python 3.11.9
  include_tasks: install_pyenv.yml
  tags: ['python', 'pyenv']

- name: Setup rclone with OneDrive configuration
  include_tasks: setup_rclone.yml
  tags: ['rclone', 'cloud'] 