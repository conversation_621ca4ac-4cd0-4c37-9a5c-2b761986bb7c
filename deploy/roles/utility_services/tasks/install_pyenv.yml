---
# Tasks for installing pyenv and Python versions

- name: Install pyenv dependencies
  apt:
    name:
      - make
      - build-essential
      - libssl-dev
      - zlib1g-dev
      - libbz2-dev
      - libreadline-dev
      - libsqlite3-dev
      - wget
      - curl
      - llvm
      - libncurses5-dev
      - libncursesw5-dev
      - xz-utils
      - tk-dev
      - libffi-dev
      - liblzma-dev
      - python3-openssl
      - git
    state: present
    update_cache: yes
  become: true

- name: Check if pyenv is installed
  stat:
    path: "/home/<USER>/.pyenv"
  register: pyenv_dir
  become: true

- name: Clone pyenv repository
  git:
    repo: https://github.com/pyenv/pyenv.git
    dest: "/home/<USER>/.pyenv"
    version: master
  become: true
  become_user: "{{ user }}"
  when: not pyenv_dir.stat.exists

- name: Set pyenv ownership
  file:
    path: "/home/<USER>/.pyenv"
    state: directory
    owner: "{{ user }}"
    group: "{{ user }}"
    recurse: yes
  become: true
  when: not pyenv_dir.stat.exists

- name: Add pyenv to .bashrc
  blockinfile:
    path: "/home/<USER>/.bashrc"
    block: |
      export PYENV_ROOT="$HOME/.pyenv"
      export PATH="$PYENV_ROOT/bin:$PATH"
      eval "$(pyenv init --path)"
      eval "$(pyenv init -)"
    marker: "# {mark} ANSIBLE MANAGED BLOCK - PYENV"
    create: yes
  become: true
  become_user: "{{ user }}"

- name: Add pyenv to .zshrc if exists
  blockinfile:
    path: "/home/<USER>/.zshrc"
    block: |
      export PYENV_ROOT="$HOME/.pyenv"
      export PATH="$PYENV_ROOT/bin:$PATH"
      eval "$(pyenv init --path)"
      eval "$(pyenv init -)"
    marker: "# {mark} ANSIBLE MANAGED BLOCK - PYENV"
    create: no
  become: true
  become_user: "{{ user }}"
  ignore_errors: yes

- name: Check if Python {{ pyenv_python_version }} is installed
  shell: |
    export PYENV_ROOT="/home/<USER>/.pyenv"
    export PATH="$PYENV_ROOT/bin:$PATH"
    eval "$($PYENV_ROOT/bin/pyenv init --path)"
    eval "$($PYENV_ROOT/bin/pyenv init -)"
    $PYENV_ROOT/bin/pyenv versions | grep {{ pyenv_python_version }}
  register: python_version_check
  changed_when: false
  failed_when: false
  become: true
  become_user: "{{ user }}"

- name: Install Python {{ pyenv_python_version }}
  shell: |
    export PYENV_ROOT="/home/<USER>/.pyenv"
    export PATH="$PYENV_ROOT/bin:$PATH"
    eval "$($PYENV_ROOT/bin/pyenv init --path)"
    eval "$($PYENV_ROOT/bin/pyenv init -)"
    $PYENV_ROOT/bin/pyenv install {{ pyenv_python_version }}
  become: true
  become_user: "{{ user }}"
  when: python_version_check.rc != 0

- name: Set Python {{ pyenv_python_version }} as global version
  shell: |
    export PYENV_ROOT="/home/<USER>/.pyenv"
    export PATH="$PYENV_ROOT/bin:$PATH"
    eval "$($PYENV_ROOT/bin/pyenv init --path)"
    eval "$($PYENV_ROOT/bin/pyenv init -)"
    $PYENV_ROOT/bin/pyenv global {{ pyenv_python_version }}
  become: true
  become_user: "{{ user }}"
  when: python_version_check.rc != 0

- name: Install django-environ Python package
  shell: |
    export PYENV_ROOT="/home/<USER>/.pyenv"
    export PATH="$PYENV_ROOT/bin:$PATH"
    eval "$($PYENV_ROOT/bin/pyenv init --path)"
    eval "$($PYENV_ROOT/bin/pyenv init -)"
    $PYENV_ROOT/bin/pyenv exec pip install django-environ
  become: true
  become_user: "{{ user }}" 
