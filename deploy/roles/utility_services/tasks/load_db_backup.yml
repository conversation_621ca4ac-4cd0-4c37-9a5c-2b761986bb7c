---
- name: Check if database container is running
  ansible.builtin.shell:
    cmd: docker ps --filter "name={{ db_container_name }}" --format '{% raw %}{{.Names}}{% endraw %}'
  register: container_check
  changed_when: false
  become: true
  become_user: "{{ user }}"

- name: Fail if database container is not running
  ansible.builtin.fail:
    msg: "Database container '{{ db_container_name }}' is not running. Please ensure the container is deployed and running first."
  when: db_container_name not in container_check.stdout

- name: Ensure backups directory exists
  ansible.builtin.file:
    path: "{{ backup_dir }}"
    state: directory
    owner: "{{ user }}"
    group: "{{ user }}"
    mode: '0755'
  become: true

- name: Copy backup from local to host
  ansible.builtin.copy:
    src: "{{ backup_file }}"
    dest: "{{ backup_dir }}/{{ backup_file | basename }}"
    owner: "{{ user }}"
    group: "{{ user }}"
  become: true

- name: Copy backup to container
  ansible.builtin.shell:
    cmd: docker cp {{ backup_dir }}/{{ backup_file | basename }} {{ db_container_name }}:{{ container_temp_dir }}/{{ backup_file | basename }}
  args:
    chdir: /home/<USER>/pickvocab
  become: true
  become_user: "{{ user }}"

- name: Load backup into database
  ansible.builtin.shell:
    cmd: |
      docker exec {{ db_container_name }} psql -U {{ db_user }} -d {{ db_name }} -f {{ container_temp_dir }}/{{ backup_file | basename }}
  args:
    chdir: /home/<USER>/pickvocab
  become: true
  become_user: "{{ user }}"
  register: db_restore_output

- name: Display database restore output
  ansible.builtin.debug:
    msg: "{{ db_restore_output.stdout_lines }}"

- name: Display database restore errors (if any)
  ansible.builtin.debug:
    msg: "{{ db_restore_output.stderr_lines }}"
  when: db_restore_output.stderr_lines is defined and db_restore_output.stderr_lines | length > 0

- name: Cleanup container backup
  ansible.builtin.shell:
    cmd: docker exec {{ db_container_name }} rm -f {{ container_temp_dir }}/{{ backup_file | basename }}
  changed_when: false
  become: true
  become_user: "{{ user }}" 