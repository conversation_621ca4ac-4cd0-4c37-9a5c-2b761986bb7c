---
- name: Confirm database emptying
  ansible.builtin.pause:
    prompt: "WARNING: This will delete ALL data in the database '{{ db_name }}'. Are you sure? (yes/no)"
  register: confirmation
  when: not (auto_approve | default(false) | bool)

- name: Check confirmation response
  ansible.builtin.fail:
    msg: "Database emptying cancelled by user"
  when: not (auto_approve | default(false) | bool) and confirmation.user_input != "yes"

- name: Check if database container is running
  ansible.builtin.shell:
    cmd: docker ps --filter "name={{ db_container_name }}" --format '{% raw %}{{.Names}}{% endraw %}'
  register: container_check
  changed_when: false
  become: true
  become_user: "{{ user }}"

- name: Fail if database container is not running
  ansible.builtin.fail:
    msg: "Database container '{{ db_container_name }}' is not running. Please ensure the container is deployed and running first."
  when: db_container_name not in container_check.stdout

- name: Drop and recreate database
  ansible.builtin.shell:
    cmd: |
      docker exec {{ db_container_name }} psql -U {{ db_user }} -d postgres -c 'DROP DATABASE IF EXISTS {{ db_name }};' &&
      docker exec {{ db_container_name }} psql -U {{ db_user }} -d postgres -c 'CREATE DATABASE {{ db_name }} WITH OWNER {{ db_user }};'
  become: true
  become_user: "{{ user }}"
  register: db_recreate_output

- name: Display operation output
  ansible.builtin.debug:
    msg: "{{ db_recreate_output.stdout_lines }}"

- name: Display operation errors (if any)
  ansible.builtin.debug:
    msg: "{{ db_recreate_output.stderr_lines }}"
  when: db_recreate_output.stderr_lines is defined and db_recreate_output.stderr_lines | length > 0

- name: Verify database is accessible after operation
  ansible.builtin.shell:
    cmd: docker exec {{ db_container_name }} psql -U {{ db_user }} -d {{ db_name }} -c "SELECT 1"
  changed_when: false
  become: true
  become_user: "{{ user }}"
  register: db_check

- name: Display success message
  ansible.builtin.debug:
    msg: "Database successfully recreated."
  when: db_check.rc == 0 