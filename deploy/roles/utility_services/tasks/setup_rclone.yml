---
- name: Install rclone
  ansible.builtin.package:
    name: rclone
    state: present
  become: true

- name: Create rclone config directory
  ansible.builtin.file:
    path: "/home/<USER>/.config/rclone"
    state: directory
    owner: "{{ user }}"
    group: "{{ user }}"
    mode: "{{ rclone_config_dir_mode }}"
  become: true

- name: Copy rclone configuration
  ansible.builtin.template:
    src: rclone.conf.j2
    dest: "/home/<USER>/.config/rclone/rclone.conf"
    owner: "{{ user }}"
    group: "{{ user }}"
    mode: "{{ rclone_config_file_mode }}"
  become: true 