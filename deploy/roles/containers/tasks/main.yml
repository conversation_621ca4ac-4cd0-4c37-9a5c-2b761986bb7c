---
- name: Update application code
  block:
    - name: Pull latest changes from git repository
      ansible.builtin.git:
        repo: "{{ app_repo }}"
        dest: "{{ app_path }}"
        version: "{{ app_branch }}"
        update: yes
      register: git_update
  become: true
  become_user: "{{ user }}"
  tags: ['update']

- name: Manage containers
  block:
    - name: Stop running containers
      community.docker.docker_compose_v2:
        project_src: "{{ app_path }}"
        files:
          - "{{ docker_compose_file }}"
        state: absent
        remove_orphans: yes
      register: compose_down
      ignore_errors: yes  # Continue if no containers are running

    - name: Start containers
      community.docker.docker_compose_v2:
        project_src: "{{ app_path }}"
        files:
          - "{{ docker_compose_file }}"
        state: present
      register: compose_up

    - name: Display Docker Compose stdout
      debug:
        var: compose_up.stdout
      when: compose_up.stdout is defined

    - name: Display Docker Compose stderr
      debug:
        var: compose_up.stderr
      when: compose_up.stderr is defined

    - name: Wait for backend container to be ready
      ansible.builtin.wait_for:
        timeout: "{{ container_wait_timeout }}"
      when: compose_up.changed

    - name: Run database migrations
      ansible.builtin.command:
        cmd: "docker exec {{ backend_container_name }} {{ migration_command }}"
        chdir: "{{ app_path }}"
      register: migration_result
      changed_when: "'No migrations to apply.' not in migration_result.stdout"
      when: compose_up.changed or git_update.changed
  become: true
  become_user: "{{ user }}"
  tags: ['containers'] 