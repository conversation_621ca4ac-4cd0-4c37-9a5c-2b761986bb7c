---
- name: Update package cache
  apt:
    update_cache: yes
    cache_valid_time: 3600  # Only update if cache is older than 1 hour
  become: true
  tags: ['system', 'packages']

- name: Install required system packages
  apt:
    name: "{{ required_packages }}"
    state: present
  become: true
  tags: ['system', 'packages']

- name: Configure user and SSH
  block:
    - name: Create user {{ user }}
      user:
        name: "{{ user }}"
        state: present
        shell: "{{ user_shell }}"
        groups: "{{ user_groups }}"
        append: yes

    - name: Setup SSH directory structure
      ansible.builtin.file:
        path: "/home/<USER>/.ssh"
        state: directory
        owner: "{{ user }}"
        group: "{{ user }}"
        mode: "{{ ssh_dir_mode }}"

    - name: Configure SSH keys
      ansible.builtin.copy:
        src: "{{ item.src }}"
        dest: "/home/<USER>/.ssh/{{ item.dest }}"
        owner: "{{ user }}"
        group: "{{ user }}"
        mode: "{{ item.mode }}"
      loop: "{{ ssh_key_files }}"
  become: true
  tags: ['user', 'ssh']

- name: Add GitHub to known_hosts
  ansible.builtin.known_hosts:
    path: "/home/<USER>/.ssh/known_hosts"
    name: github.com
    key: "{{ lookup('pipe', 'ssh-keyscan -t rsa github.com') }}"
    state: present
  become: true
  become_user: "{{ user }}"
  tags: ['ssh', 'github']

- name: Configure Docker
  block:
    - name: Install Docker Compose
      include_tasks: docker_compose.yml

    - name: Ensure Docker service is running and enabled
      service:
        name: docker
        state: started
        enabled: yes
  become: true
  tags: ['docker']

- name: Setup application directory
  block:
    - name: Remove existing application directory
      ansible.builtin.file:
        path: "{{ app_path }}"
        state: absent

    - name: Create fresh application directory
      ansible.builtin.file:
        path: "{{ app_path }}"
        state: directory
        owner: "{{ user }}"
        group: "{{ user }}"
        mode: "{{ app_dir_mode }}"

    - name: Clone application repository
      ansible.builtin.git:
        repo: "{{ app_repo }}"
        dest: "{{ app_path }}"
        clone: yes
        depth: "{{ app_repo_depth }}"
      become_user: "{{ user }}"
  become: true
  tags: ['application'] 