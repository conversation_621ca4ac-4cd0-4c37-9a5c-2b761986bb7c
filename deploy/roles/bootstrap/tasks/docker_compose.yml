---
- name: Check if Docker Compose plugin is installed
  command: docker compose version
  register: compose_version
  changed_when: false
  failed_when: false
  check_mode: no
  become: true

- name: Install Docker Compose V2 plugin
  block:
    - name: Create Docker CLI plugins directory
      ansible.builtin.file:
        path: /usr/local/lib/docker/cli-plugins
        state: directory
        mode: '0755'
      when: compose_version.rc != 0

    - name: Get latest Docker Compose release version
      uri:
        url: https://api.github.com/repos/docker/compose/releases/latest
        return_content: yes
      register: compose_releases
      when: compose_version.rc != 0

    - name: Download and install Docker Compose V2
      get_url:
        url: "https://github.com/docker/compose/releases/download/{{ compose_releases.json.tag_name }}/docker-compose-{{ ansible_system | lower }}-{{ ansible_architecture }}"
        dest: /usr/local/lib/docker/cli-plugins/docker-compose
        mode: '0755'
        force: yes
      when: compose_version.rc != 0
  become: true
  notify: Verify docker compose installation 