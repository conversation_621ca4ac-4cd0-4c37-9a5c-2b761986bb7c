---
# User configuration
user_shell: /bin/bash
user_groups: ['docker']

# SSH configuration
ssh_dir_mode: '0700'
ssh_key_files:
  - { src: '~/.ssh/id_ed25519', dest: 'id_ed25519', mode: '0600' }
  - { src: '~/.ssh/id_ed25519.pub', dest: 'id_ed25519.pub', mode: '0644' }
  - { src: '~/.ssh/id_ed25519.pub', dest: 'authorized_keys', mode: '0600' }

# Application configuration
app_name: pickvocab
app_dir_mode: '0755'
app_repo: **************:phuongduyphan/pickvocab.git
app_repo_depth: 1

# System packages
required_packages:
  - acl
  - git
  - docker.io 