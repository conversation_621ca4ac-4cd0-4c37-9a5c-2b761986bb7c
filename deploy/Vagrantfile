# -*- mode: ruby -*-
# vi: set ft=ruby :

# All Vagrant configuration is done below. The "2" in Vagrant.configure
# configures the configuration version (we support older styles for
# backwards compatibility). Please don't change it unless you know what
# you're doing.
Vagrant.configure("2") do |config|
  config.vm.box = "bento/ubuntu-22.04-arm64"

  config.vm.network "private_network", ip: "*************"

  config.vm.provider :vmware_desktop do |vmware|
    vmware.vmx["memsize"] = "2048"  # Set memory to 2048 MB
    vmware.vmx["numvcpus"] = "2"    # Set number of CPUs to 2
  end

  config.vm.provision "ansible" do |ansible|
    ansible.verbose = "v"
    ansible.playbook = "playbooks/example_playbook.yml"
  end

  config.vm.network "forwarded_port", guest: 443, host: 443
  config.vm.network "forwarded_port", guest: 5432, host: 5433
end
