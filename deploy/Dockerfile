FROM ubuntu:latest

# Update and install necessary packages - minimal for a managed node
RUN apt-get update && apt-get install -y openssh-server python3 python3-pip sudo net-tools iproute2

# Set empty password for root user (INSECURE - FOR LEARNING ONLY)
RUN echo 'root:' | chpasswd -e

# Configure SSH to allow root login with password authentication
RUN mkdir -p /run/sshd
RUN echo 'PermitRootLogin yes' >> /etc/ssh/sshd_config
RUN sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config
RUN sed -i 's/#PermitEmptyPasswords no/PermitEmptyPasswords yes/' /etc/ssh/sshd_config # Explicitly allow empty passwords

# Expose SSH port
EXPOSE 22

# Start SSH server
CMD ["/usr/sbin/sshd", "-D"]
