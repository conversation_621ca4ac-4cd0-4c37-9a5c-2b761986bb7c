---
# rclone configuration variables
# These could be encrypted with ansible-vault in production

# rclone file permissions
rclone_config_dir_mode: '0700'
rclone_config_file_mode: '0600'

# OneDrive configuration
rclone_onedrive:
  type: onedrive
  client_id: 3d1495ef-1460-4237-a305-fc2878ca2c93
  client_secret: ****************************************
  token: >-
    {"access_token":"EwB4BMl6BAAUBKgm8k1UswUNwklmy2v7U/S+1fEAAYtOo4Pe6Ot+ZJz0U1iHQv56vFafgVUAPS4WllKDK4KMZPVvD6jWySRTYIoThN8rr8agUt3cBbIByCz1P+3v2ht1TVGef2+ZC360x/WMxkwV7ByeKHik8SV1cDfQqCwpmSUqOFz2o6voO04CSqWmud9nERCEFVxuMII/kG9d5ty83g0BEgs74OV3Gca546F7LEgFiYzbRayB5hC/7W6IFOhNbL12xafBLKlJjh5P5KqwQljWG+IfhMTkDRxThxn1fMDbV24jXkzKL/nVlppkd0ga2U60LViq2smBqwP5V3kBmrg4pyqD3EqWH2t0jRWKPPoZ6y1P8Alm1WpiB/iPGo8QZgAAEOZjLUgaMtELSBAZMdwVfvxAA8fjioBgrGHWUAw1YWH7EiXVK4XMtbwy6x8uBM4XEKEBM57RGsKzRZOVzi26URt82bIocIgE6fCvX73hhkZ1IW02dG9EFUatWL/o9AxRS+NiBApqaSj0Ufcb3LEWCrhIRdoed3OVmuIWhI3GJ3L3Z3zeFWDtPGpqyY3gVW7Zt9bjLderYYw0ACK6nQAiDkxAzG0uEi8Ve1KpZzCnboGDMCU2oKJyssXQ2hLCgAbQy7h+1Pe44qgFes+0iY/TQPHLIFPJ0AGH46LN86Wz1aJpyB2DVYW3IXHLzqIDH/Z7wK0Biyfokr2G9qFC5+MRGpA7eiBUYN5q/g0RdhOV8Zxi3dqKB6WWcVWTreWMKsUNjE7ol0NWZzyuSiEQlpAlAvgSh/qTAUSvS6v8/S086DvP3MncBQ5cAH9W5Y9TV4MGCs6s1dG5DXjWPpqzvYUjmlJmrP3GJKiXvDOTrivRSYllg76DqaSnoRi9c+swlkg8D/Z4LGtySOttYljjSibc5EMdo1YTAD/2MXYNVAnQ1Jp9x177C+OikK5jDzH28KrWGf+6vHdQAU0WN3t51QQZFXwa8lJNdG+0X9IpqWwgnv8sNT1dijAWieZI/DUyHwwELjU/lDYMtEKSCk9UME/1Z4YUz3aioc+O+bxZg6+Qb3THxYHv52vJChzkUBmz+mGnxDbXbZzYkSRSUxIUa3BrKk8yZi0/t8/q86+4zgdTngTyiZxoPU7VK4skwGzXHi5rsiMArLbArV4RcxhA/vwRX8eTh4xC7YVZWae7XWYJtBzwy9H72NM1i4+dtb4f74IRmVKOxr2/S5eDLqSAxuhjCl+H4b/dpJCPwR0sFU2yFGN+sqd0p5njN2eZzUpp+L4vGEIMjvqRZ0anW6Bgs7zYa+toIz30MMJaVhlJ0d/EbIs3E7v0rI8YM50Gaa5733wcYqf/CxE5sQj5pxEsql4G+qnBk9rg8JnKbxk4b0OX4xvHPj0a9Oc+nqSpvvuGvUDQZ3m+22qjn3BYPs2V6Zti9EgE2goDPrz2r86GvYJPoIGscZJT7djaK8YP6GF0PQmxuKnzC8Q7lh1smrE5Qf5byxpOMesYCmVjSlsjksafgcFPP29vAw==","token_type":"Bearer","refresh_token":"M.C509_BL2.0.U.-CjtvUA7nhvtJd818Cl8I297Ao0t1AwoIBdxSZ7frKKR2!MKAPFPaol8RyE1i*F55xo8tvbchap8odx0VOtFhVf37ngzYkInXTHvo*ZNY3b6UnrQsKDm9GrHaQ43*As1HjplrRxDfK0tWdlbWwqWq7y2ZRmqnaped46w3qbyC8eLVvQ1ZrQmk0Iyvc54H0o!9UU0KRBzvV5Bw2vO2uWfrrPPhClsmevOacyr!!mV!NRyZQt8iU0ce86U7JvIe7Reeg6k1Kulb!BjrlbAM!1UmLyGAfIF90IaYQdh5D4Bv26!uPrJsLkIs5NL1BkGsCXzyo4AME5wBQp4LYGTbd2HnDveCgW*6LMorZ0ynNNRZa5l4lSpYjI46vXYKgkH1xp*SpA$$","expiry":"2025-03-01T01:00:29.757249848Z"}
  drive_id: a5fb9abfd0a383b9
  drive_type: personal

# You can add more rclone remote configurations here as needed
# For example:
# rclone_gdrive:
#   type: drive
#   client_id: your_client_id
#   client_secret: your_client_secret 