---
# Application configuration
app_name: pickvocab
app_repo: **************:phuongduyphan/pickvocab.git
app_branch: master
app_dir_mode: '0755'
app_repo_depth: 1
app_path: "/home/<USER>/{{ app_name }}"
pickvocab_dir_path: /Users/<USER>/code/pickvocab

# ACME.sh configuration
acme_local_config_path: "/Users/<USER>/.acme.sh"
acme_remote_config_path: ".acme.sh"
acme_dir_mode: '0700'

# System packages
required_packages:
  - acl
  - git
  - docker.io

# Database configuration
db_name: pickvocab
db_user: duy
backup_dir: /backup
remote_backup_path: onedrive:/backup
remote_backup_test_path: onedrive:/backup_test