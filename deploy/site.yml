---
- name: Deploy application
  hosts: all
  vars:
    # Only define variables that are specific to this playbook
    # or that need to be overridden for all environments
    deployment_version: "1.0.0"
    
  pre_tasks:
    - name: Verify minimum Ansible version
      assert:
        that: "ansible_version.full is version_compare('2.9', '>=')"
        msg: "This playbook requires Ansible 2.9 or later"

  roles:
    - role: bootstrap
      tags: ['bootstrap']
    
    - role: utility_services
      tags: ['utilities']
    
    - role: deploy
      tags: ['deploy']
      
    - role: containers
      tags: ['containers']
      
    - role: db_backup
      tags: ['backup', 'database']