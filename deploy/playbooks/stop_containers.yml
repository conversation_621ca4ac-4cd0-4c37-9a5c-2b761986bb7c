---
- name: Stop Application Containers
  hosts: all
  become: true
  become_user: "{{ user }}"

  tasks:
    - name: Stop running containers
      community.docker.docker_compose_v2:
        project_src: "{{ app_path }}"
        files:
          - "{{ docker_compose_file }}"
        state: absent
        remove_orphans: yes
      register: compose_down
      ignore_errors: yes  # Continue if no containers are running 

    - name: Display Docker Compose stdout
      debug:
        var: compose_down.stdout
      when: compose_down.stdout is defined

    - name: Display Docker Compose stderr
      debug:
        var: compose_down.stderr
      when: compose_down.stderr is defined 