export interface MessagingService {
  sendMessage(type: string, data: any, target: string): Promise<any>;
}

// Global messaging service instance for use without injection
let _globalMessagingService: MessagingService | null = null;

// Function to set global messaging service
export function setGlobalMessagingService(service: MessagingService) {
  _globalMessagingService = service;
}

// Function to get the current messaging service
export function getMessagingService(): MessagingService {
  if (_globalMessagingService) return _globalMessagingService;
  
  // Fallback - create a new service based on best guess
  try {
    return createMessagingService('popup');
  } catch (e) {
    return createMessagingService('content');
  }
}

export const createMessagingService = (context: 'content' | 'popup'): MessagingService => {
  if (context === 'content') {
    return {
      async sendMessage(type, data, target) {
        const { sendMessage } = await import('webext-bridge/content-script');
        return sendMessage(type, data, target);
      }
    };
  } else {
    return {
      async sendMessage(type, data, target) {
        const { sendMessage } = await import('webext-bridge/popup');
        return sendMessage(type, data, target);
      }
    };
  }
}; 