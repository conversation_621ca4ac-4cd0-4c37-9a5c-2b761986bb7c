import { PartOfSpeech } from "pickvocab-dictionary";
import slugify from "slugify";

export function stylesForPartOfSpeech(partOfSpeech: PartOfSpeech) {
  switch (partOfSpeech) {
    case 'verb':
      return ['border-red-400', 'text-red-400'];

    case 'noun':
      return ['border-green-400', 'text-green-400'];

    case 'adjective':
      return ['border-blue-400', 'text-blue-400'];

    default:
      return ['border-purple-400', 'text-purple-400'];
  }
}

export function slugifyText(text: string) {
  // const textWithoutHyphen = text.replace(/-/g, '_');
  // return slugify(textWithoutHyphen);
  return slugify(text.replaceAll(/['()/!]/g, '-'), { strict: true, lower: true });
}