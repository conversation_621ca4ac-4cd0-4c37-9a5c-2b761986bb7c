import { defineConfig } from 'wxt';
import devtools from 'vite-plugin-vue-devtools';

// See https://wxt.dev/api/config.html
export default defineConfig({
  extensionApi: 'chrome',
  modules: ['@wxt-dev/module-vue'],
  vite: () => ({
    plugins: [
      devtools({
        appendTo: /\/entrypoints\/(popup|options)\/main\.ts$/,
      }),
    ],
  }),
  manifest: ({ browser }) => ({
    icons: {
      16: 'pickvocab-16.png',
      32: 'pickvocab-32.png',
      48: 'pickvocab-48.png',
      128: 'pickvocab-128.png',
    },
    permissions: ['storage', 'contextMenus', 'activeTab', 'identity'],
    host_permissions: [
      `${import.meta.env.WXT_API_URL}/`
    ],
    commands: {
      "_execute_action": {
        "suggested_key": {
          "default": "Ctrl+Shift+K",
          "mac": "Command+Shift+K"
        },
        "description": "Open Pickvocab popup"
      }
    },
    web_accessible_resources: [
      {
        resources: ['pickvocab.svg'],
        matches: ['<all_urls>']
      }
    ],
    ...(browser === 'chrome' ? {
      key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA75ajjughlro2U1AE8qep39Kq3K9JReEuUNVuU/+tE+c5hfLKuRlCEyI3J2w7NpR1kwYMN5HaLbTM8arOEIOMRQANwjO5zXBXiuogkvLexjxaXzOBCgKicPmKK1vueBve2aBZZCCNeZzsupDf7k7SYotHeXvVZOGXr9+eG90l+ipe3NK8QRcsHdZ+oBhnNYLxC4YAyDSRxnFvAK1IFRkRt0MDrra7gZ7vq3XwuMQsZkkGLD3U2MTDBqS9WYYxmv8o3VpY945rAounjTfXY22rbaMhPnyNdle1p33AXavgs7g2L1VR3/g3+9Ix15p+qEerg6rUI1sWh9DEhoMRIK2OuQIDAQAB",
    } : {}),
    oauth2: {
      client_id: "1062383996574-bjuibq4tqq1ho90qabvtsh0035l9ncts.apps.googleusercontent.com",
      scopes: [
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
      ],
    }
  }),
});
