<script setup lang="ts">
import type { WordInContextEntry } from "pickvocab-dictionary";
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';
// @ts-ignore
import IconChevronDown from '@tabler/icons-vue/dist/esm/icons/IconChevronDown.mjs';
import { languages } from '@/utils/languages';

const props = withDefaults(
  defineProps<{
    wordEntry: WordInContextEntry;
  }>(),
  {}
);

const selectedLanguage = defineModel<string>("selectedLanguage", {
  default: "English",
});
</script>

<template>
  <div v-if="wordEntry.definitionShort">
    <div class="mt-8 relative w-48">
      <select
        v-model="selectedLanguage"
        class="w-full pl-3 pr-8 py-2 border rounded-md text-gray-900 bg-white relative appearance-none"
      >
        <option v-for="language in languages" :key="language.value" :value="language.value">
          {{ language.name }}
        </option>
      </select>
      <icon-chevron-down class="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-600 pointer-events-none" />
    </div>
    <div class="mt-8">
      <p class="text-xl text-gray-700 font-semibold">Explanation</p>
      <p
        v-if="selectedLanguage === 'English'"
        class="text-base text-gray-600 mt-2"
      >
        {{ wordEntry.definitionShort.explanation }}
      </p>
      <p
        v-else-if="wordEntry.definitionShort.languages && wordEntry.definitionShort.languages[selectedLanguage].explanation"
        class="text-base text-gray-600 mt-2"
      >
        {{ wordEntry.definitionShort.languages[selectedLanguage].explanation }}
      </p>
    </div>
    <div class="mt-8 border-t">
      <a
        :href="`https://pickvocab.com/app/dictionary?word=${wordEntry.word}`"
        rel="nofollow"
        class="mt-4 font-semibold text-blue-800 flex items-center hover:underline"
        target="_blank"
      >
        <span>See other definitions of "{{ wordEntry.word }}"</span>
        <icon-arrow-right class="w-4 h-4 ml-1"></icon-arrow-right>
      </a>
    </div>
  </div>
</template> 