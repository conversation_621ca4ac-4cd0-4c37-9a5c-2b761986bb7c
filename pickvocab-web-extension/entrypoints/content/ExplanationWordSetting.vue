<script setup lang="ts">
import type { WordInContextEntry } from 'pickvocab-dictionary';
import { Switch } from '@/components/ui/switch';
import { SubmitButton } from '@/components/ui/submit-button';
import { ref } from 'vue';

const props = withDefaults(defineProps<{
  wordEntry?: WordInContextEntry,
  llmModel?: any,
  isLoading?: boolean,
}>(), {
  isLoading: false,
});

const isAddCardLoading = ref(false);
const isDetailed = defineModel<boolean>('isDetailed', { default: false });
const emit = defineEmits(['addCard', 'refresh']);

function handleAddCard() {
  if (!props.wordEntry) return;
  
  isAddCardLoading.value = true;
  // Pass a callback that will be called when operation completes
  emit('addCard', props.wordEntry, () => {
    isAddCardLoading.value = false;
  });
}
</script>

<template>
  <div class="flex items-center">
    <button class="flex items-center mr-2">
      <Switch v-model:checked="isDetailed" class="data-[state=checked]:bg-blue-600 focus-visible:ring-blue-600" />
      <p class="ml-1 text-sm text-gray-800">Detailed</p>
    </button>
    <SubmitButton 
      text="Save" 
      loading-text="Saving..."
      :is-loading="isAddCardLoading" 
      @click="handleAddCard" 
      is-primary
      class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-1.5 focus:outline-none ml-auto"
    />
  </div>
</template> 