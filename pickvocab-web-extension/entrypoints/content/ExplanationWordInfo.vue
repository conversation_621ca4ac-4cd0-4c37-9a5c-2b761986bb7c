<script setup lang="ts">
import type { WordInContextEntry } from 'pickvocab-dictionary';
// @ts-ignore
import IconReload from '@tabler/icons-vue/dist/esm/icons/IconReload.mjs';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useLLMStore } from '../popup/stores/llm';

const props = withDefaults(defineProps<{
  wordEntry?: WordInContextEntry,
  llmModel?: any,
}>(), {});

const llmStore = useLLMStore();
const emit = defineEmits(['refresh']);

// Update to look for both potential shadow root names
const shadowRoot = 
  document.querySelector('floating-popup-container')?.shadowRoot?.querySelector('#pickvocab-popup-container') || 
  document.querySelector('definition-popup')?.shadowRoot?.querySelector('#pickvocab-popup-container');
// const shadowRoot = undefined;
</script>

<template>
  <TooltipProvider :delay-duration="100">
    <Tooltip>
      <TooltipTrigger>
        <icon-reload @click="emit('refresh')" stroke-width="2.5"
          class="inline-block text-gray-400 w-4 h-4 cursor-pointer hover:text-blue-500"/>
      </TooltipTrigger>
      <TooltipContent :teleportTo="shadowRoot as HTMLElement" class="bg-white border-gray-200">
        <div v-if="wordEntry" class="flex items-center text-sm font-medium">
          <span v-if="llmModel" class="flex items-center">
            <img :src="llmStore.providerMap[llmModel.provider].logo" alt="llm-model-img" class="w-4 h-4">
            <span class="ml-1">{{ llmModel.name }}&nbsp;</span>
          </span>
          <span v-if="wordEntry.updatedAt">{{ new Date(wordEntry.updatedAt).toLocaleString() }}</span>
        </div>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
</template> 