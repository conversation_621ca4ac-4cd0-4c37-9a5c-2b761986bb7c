<script setup lang="ts">
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';
import type { WordInContextEntry } from "pickvocab-dictionary";
import { stylesForPartOfSpeech } from '@/utils/utils';

const props = withDefaults(
  defineProps<{
    wordEntry: WordInContextEntry;
    showContext?: boolean;
  }>(),
  {
    showContext: false,
  }
);
</script>

<template>
  <div v-if="wordEntry.definition">
    <div class="flex mt-8 items-centers">
      <p class="text-base text-gray-600">
        <span
          class="inline-block text-sm border rounded-xl px-2 align-middle"
          :class="stylesForPartOfSpeech(wordEntry.definition.partOfSpeech)"
        >
          {{ wordEntry.definition.partOfSpeech }}
        </span>
        <span class="ml-1">{{ wordEntry.definition.definition }}</span>
      </p>
    </div>

    <div class="mt-8">
      <p class="text-xl text-gray-700 font-semibold">Explanation</p>
      <p class="text-base text-gray-600 mt-2">
        {{ wordEntry.definition.explanation }}
      </p>
    </div>

    <div class="mt-8">
      <p class="text-xl text-gray-700 font-semibold">Examples</p>
      <ol class="list-decimal list-inside text-base text-gray-600">
        <li v-for="example in wordEntry.definition.examples" class="p-1 mt-2">
          <blockquote
            class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic"
          >
            {{ example.example }}
          </blockquote>
          <p class="mt-4">{{ example.explanation }}</p>
        </li>
      </ol>
    </div>

    <div class="mt-8">
      <div class="flex items-center">
        <p class="text-xl text-gray-700 font-semibold">Synonyms</p>
      </div>
      <ol class="text-base text-gray-600 mt-4">
        <li v-for="synonym in wordEntry.definition.synonyms" class="mt-4">
          <a
            :href="`https://pickvocab.com/app/dictionary?word=${synonym.synonym}`"
            rel="nofollow"
            class="font-semibold underline text-blue-800"
            target="_blank"
          >
            {{ synonym.synonym }}
          </a>
          <blockquote
            class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic"
          >
            {{ synonym.example }}
          </blockquote>
          <p class="mt-4">{{ synonym.explanation }}</p>
        </li>
      </ol>
    </div>

    <div class="mt-8 border-t">
      <a
        :href="`https://pickvocab.com/app/dictionary?word=${wordEntry.word}`"
        rel="nofollow"
        class="mt-4 font-semibold text-blue-800 flex items-center hover:underline"
        target="_blank"
      >
        <span>See other definitions of "{{ wordEntry.word }}"</span>
        <icon-arrow-right class="w-4 h-4 ml-1"></icon-arrow-right>
      </a>
    </div>
  </div>
</template> 