import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useContentStore = defineStore('content', () => {
  const word = ref('');
  const context = ref('');
  const offset = ref(0);
  const selectedAction = ref('menu'); // Initial state is 'menu'

  function setWord(newWord: string, newContext: string, newOffset: number) {
    word.value = newWord;
    context.value = newContext;
    offset.value = newOffset;
  }

  function setInitialData(newWord: string, newContext: string, newOffset: number, initialAction: 'menu' | 'lookup' | 'fix writing') {
    word.value = newWord;
    context.value = newContext;
    offset.value = newOffset;
    selectedAction.value = initialAction;
  }

  function setSelectedAction(action: 'menu' | 'lookup' | 'fix writing') {
    selectedAction.value = action;
  }

  return {
    word,
    context,
    offset,
    selectedAction,
    setWord,
    setInitialData,
    setSelectedAction
  };
});
