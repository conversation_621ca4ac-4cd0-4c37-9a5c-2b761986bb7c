import { createApp, watch } from "vue";
import { create<PERSON><PERSON> } from "pinia";
import {
  computePosition,
  flip,
  shift,
  offset,
  autoUpdate,
} from "@floating-ui/dom";
import { useContentStore } from "./store";
import Popup from "./DefinitionPopup.vue";
import { ContentScriptContext } from "wxt/client";
import { debounce } from 'lodash-es';
import FloatingPopupContainer from "./FloatingPopupContainer.vue";

// Cache for storing font sizes by container element
const fontSizeCache = new WeakMap<Element, number>();

export function getSelectedFontSizes(container: Element | null): number | undefined {
  if (!container) return undefined;

  // Check if we have a cached value for this container
  if (fontSizeCache.has(container)) {
    return fontSizeCache.get(container);
  }

  // Check if we can directly use the container if it's a text-containing element
  if (container.textContent?.trim()) {
    const fontSize = parseFloat(window.getComputedStyle(container).fontSize);
    fontSizeCache.set(container, fontSize);
    return fontSize;
  }

  // Only create TreeWalker if necessary
  const textNode = document.createTreeWalker(
    container,
    NodeFilter.SHOW_TEXT,
    { acceptNode: node => node.textContent?.trim() ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP }
  ).nextNode() as Text | null;

  const element = textNode?.parentElement || container;
  const fontSize = parseFloat(window.getComputedStyle(element).fontSize);

  // Cache the result
  fontSizeCache.set(container, fontSize);
  return fontSize;
}

export function getSurroundingPassage(selection: Selection): {
  passage: string;
  offset: number;
  fontSize?: number;
  targetElement?: HTMLInputElement | HTMLTextAreaElement | null;
} {
  const range = selection.getRangeAt(0);
  const container = range.commonAncestorContainer;
  const anchorNode = selection.anchorNode;
  const focusNode = selection.focusNode;

  // Helper function to find the target input/textarea from a node
  const findElementFromNode = (node: Node | null): HTMLInputElement | HTMLTextAreaElement | null => {
      if (!node) return null;
      if (node instanceof HTMLInputElement || node instanceof HTMLTextAreaElement) {
          return node;
      }
      if (node.nodeType === Node.TEXT_NODE && node.parentElement && (node.parentElement instanceof HTMLInputElement || node.parentElement instanceof HTMLTextAreaElement)) {
          return node.parentElement;
      }
      return null;
  };

  let targetElement: HTMLInputElement | HTMLTextAreaElement | null = null;

  // --- Helper function to check if an element contains multiple block-level children ---
  const containsMultipleBlockChildren = (element: Element | null): boolean => {
    if (!element || !element.children) return false;
    const blockTags = new Set(['DIV', 'P', 'LI', 'ARTICLE', 'SECTION', 'BLOCKQUOTE', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6']);
    let blockChildCount = 0;
    for (let i = 0; i < element.children.length; i++) {
      if (blockTags.has(element.children[i].tagName)) {
        blockChildCount++;
      }
      if (blockChildCount > 1) {
        return true; // Found more than one block child
      }
    }
    return false;
  };
  // --- End helper function ---

  // Find potential targets from anchor and focus nodes
  const anchorTarget = findElementFromNode(anchorNode);
  const focusTarget = findElementFromNode(focusNode);

  // Use the target only if both anchor and focus point to the *same* element
  if (anchorTarget && anchorTarget === focusTarget) {
      targetElement = anchorTarget;
  }

  // Fallback Check: If anchor/focus didn't yield a result, check commonAncestorContainer
  if (!targetElement) {
      const container = range.commonAncestorContainer;
      let elementToQuery: Element | null = null;
      if (container.nodeType === Node.ELEMENT_NODE) {
          elementToQuery = container as Element;
      } else if (container.parentElement) {
          elementToQuery = container.parentElement;
      }

      // --- Check if the container likely spans multiple blocks BEFORE querying for inputs --- 
      const isLikelyMultiBlockContainer = containsMultipleBlockChildren(elementToQuery);

      // Only query for input/textarea if it's NOT likely a multi-block container
      if (elementToQuery && !isLikelyMultiBlockContainer) {
          // Prioritize textarea, then visible inputs
          let potentialTextArea = elementToQuery.querySelector('textarea') as HTMLTextAreaElement | null;
          if (potentialTextArea) {
              targetElement = potentialTextArea;
          } else {
              // Use a separate variable for the input query
              let potentialInput = elementToQuery.querySelector('input:not([type="hidden"])') as HTMLInputElement | null;
              if (potentialInput) {
                  targetElement = potentialInput;
              }
          }
      }
  }

  if (targetElement) {
    const passage = targetElement.value;
    const offset = range.startOffset;
    const computedStyle = window.getComputedStyle(targetElement);
    const fontSize = parseFloat(computedStyle.fontSize);
    // Return path 1: Found input/textarea
    return {
      passage,
      offset,
      fontSize: isNaN(fontSize) ? undefined : fontSize,
      targetElement, // Type matches annotation
    };
  }

  // *** Existing logic for non-input/textarea elements ***
  let passageContainer: Node | null = container;
  while (passageContainer && passageContainer.nodeType !== Node.ELEMENT_NODE) {
    passageContainer = passageContainer.parentNode;
  }
  if (passageContainer && ["SPAN", "A", "EM", "STRONG", "I", "B", "U", "MARK"].includes((passageContainer as Element).tagName)) {
    passageContainer = passageContainer.parentNode;
  }
  while (passageContainer && !["P", "DIV", "ARTICLE", "SECTION", "BLOCKQUOTE", "LI", "H1", "H2", "H3", "H4", "H5", "H6"].includes((passageContainer as Element).tagName)) {
    passageContainer = passageContainer.parentNode;
  }

  if (!passageContainer) {
    // Return path 2: Fallback
    return { passage: selection.toString(), offset: 0, targetElement: null }; // Type matches annotation
  }

  const fontSize = getSelectedFontSizes(passageContainer as Element);
  const passage = passageContainer.textContent || "";
  const selectedText = selection.toString();
  const offsets = [];
  let index = passage.indexOf(selectedText);
  while (index !== -1) { offsets.push(index); index = passage.indexOf(selectedText, index + 1); }

  const selectionStart = range.startOffset;
  const selectionContainerText = range.startContainer.textContent || "";
  const containerOffset = passage.indexOf(selectionContainerText);
  let relativeSelectionStart = selectionStart;
  if (containerOffset !== -1) { relativeSelectionStart = containerOffset + selectionStart; }

  let bestOffset = 0;
  if (offsets.length > 0) {
    let minDistance = Infinity;
    for (const offset of offsets) {
      const distance = Math.abs(offset - relativeSelectionStart);
      if (distance < minDistance) { minDistance = distance; bestOffset = offset; }
    }
  }

  // Return path 3: Non-input/textarea success
  return {
    passage,
    offset: bestOffset,
    fontSize,
    targetElement: null, // Type matches annotation
  };
}

export function generateTailwindFontSizes(fontSize: number) {
  // Default values based on the provided structure and Tailwind's defaults
  const baseFontSize = fontSize;
  const baseLineHeight = 1.5 * baseFontSize;

  // Font size multipliers based on Tailwind's scale
  const fontSizes: Record<string, number> = {
    xs: 0.75,
    sm: 0.875,
    base: 1,
    lg: 1.125,
    xl: 1.25,
    "2xl": 1.5,
    "3xl": 1.875,
    "4xl": 2.25,
    "5xl": 3,
    "6xl": 3.75,
    "7xl": 4.5,
    "8xl": 6,
    "9xl": 8,
  };

  // Line-height multipliers, calculated dynamically or from defaults
  const lineHeights: Record<string, number> = {
    xs: 1,
    sm: 1.25,
    base: 1.5,
    lg: 1.75,
    xl: 1.75,
    "2xl": 2,
    "3xl": 2.25,
    "4xl": 2.5,
    "5xl": 1, // Tight line-height for larger sizes
    "6xl": 1, // Tight line-height
    "7xl": 1, // Tight line-height
    "8xl": 1, // Tight line-height
    "9xl": 1, // Tight line-height
  };

  // Generate CSS
  let cssOutput = "";
  for (const size in fontSizes) {
    const fontSizePx = fontSizes[size] * baseFontSize;
    const lineHeightMultiplier = lineHeights[size];
    const lineHeightPx =
      lineHeightMultiplier === 1
        ? fontSizePx
        : lineHeightMultiplier * baseFontSize;

    cssOutput += `
.text-${size} {
  font-size: ${fontSizePx}px;
  line-height: ${lineHeightPx}px;
}
`;
  }

  return cssOutput;
}

let positioningDisabled = false;
// Global scaling factor for UI elements
const BUTTON_SCALE = 0.7;

// Store button position and style data for requestAnimationFrame
let buttonStyleData: {
  x: number;
  y: number;
  fontSize: number;
  display: string;
  needsUpdate: boolean;
} | null = null;

export async function createDefinitionPopup(
  ctx: ContentScriptContext,
  word: string,
  context: string,
  contextOffset: number,
  fontSize?: number
) {
  // Store the initial selection range for tracking
  let initialSelection: Selection | null = window.getSelection();
  let initialRange: Range | null = initialSelection && initialSelection.rangeCount > 0 ?
    initialSelection.getRangeAt(0).cloneRange() : null;

  // Save the text nodes and offsets to maintain position during scroll/zoom
  let anchorNode = initialSelection?.anchorNode;
  let anchorOffset = initialSelection?.anchorOffset;
  let focusNode = initialSelection?.focusNode;
  let focusOffset = initialSelection?.focusOffset;

  const ui = await createShadowRootUi(ctx, {
    name: "definition-popup",
    position: "inline",
    anchor: document.body,
    onMount: async (uiContainer) => {
      const popup = document.createElement("div");
      popup.id = "wxt-dictionary-popup";

      // Mount Vue app
      const pinia = createPinia();
      const popupApp = createApp(Popup);
      popupApp.use(pinia);
      popupApp.mount(popup);
      // Set the definition in the store
      const contentStore = useContentStore();
      contentStore.setWord(word, context, contextOffset);

      // Create a virtual element that tracks the original selection
      // This will update correctly during scroll and zoom
      const getVirtualElement = () => ({
        getBoundingClientRect: () => {
          // Try to get the current position of the original selection
          try {
            // If we have the original range, use it directly
            if (initialRange) {
              // This will reflect current scroll/zoom state
              return initialRange.getBoundingClientRect();
            }
            // If we lost the range but have the nodes, try to recreate it
            else if (anchorNode && focusNode) {
              const tempRange = document.createRange();
              tempRange.setStart(anchorNode, anchorOffset || 0);
              tempRange.setEnd(focusNode, focusOffset || 0);
              return tempRange.getBoundingClientRect();
            }
          } catch (e) {
            console.log('Error getting selection position:', e);
          }

          // Fallback to a default position
          return {
            width: 0,
            height: 0,
            x: 0,
            y: 0,
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            toJSON: () => {},
          }
        },
      });

      // popup.style.minWidth = "100px";
      popup.style.display = "block";
      popup.style.position = "absolute";
      popup.style.left = "0px";
      popup.style.top = "0px";
      popup.style.zIndex = "1000";

      const cleanup = autoUpdate(getVirtualElement(), popup, async () => {
        const { x, y } = await computePosition(getVirtualElement(), popup, {
          placement: "top",
          strategy: "absolute", // Position relative to the viewport
          middleware: [
            offset(15),
            flip(),
            shift({ padding: 5 }),
            // arrow({ element: arrowElement }), // If you have an arrow
          ],
        });

        Object.assign(popup.style, {
          left: `${x}px`,
          top: `${y}px`,
          // transform: "translateX(-50%)",
        });
      });

      if (fontSize) {
        const style = document.createElement("style");
        style.textContent = generateTailwindFontSizes(Math.min(fontSize, 16));
        uiContainer.appendChild(style);
      }
      uiContainer.append(popup);

      // Add event listener to close the popup when clicking outside
      const closePopup = (event: MouseEvent) => {
        // Only close if clicking outside the popup and its shadow host
        if (!popup.contains(event.target as Node) && event.target !== ui.shadowHost) {
          positioningDisabled = false;
          ui.remove();
          document.removeEventListener("click", closePopup, true);
          cleanup();
        }
      };
      document.addEventListener("click", closePopup, true);
    },
  });

  ui.mount();
  return ui;
}

export function createFloatingLookupButton(
  ctx: ContentScriptContext,
  onLookup: (selectedText: string, passage: string, offset: number, fontSize?: number) => void
): () => void {
  // Create button element directly on the body for maximum reliability
  const button = document.createElement('button');
  button.id = 'wxt-floating-lookup-button';
  button.setAttribute('aria-label', 'Show Pickvocab options'); // Accessibility
  button.setAttribute('title', 'Show Pickvocab options'); // Tooltip

  // Create logo image element
  const logoImg = document.createElement('img');
  // Use chrome.runtime.getURL to access the extension's assets
  logoImg.src = chrome.runtime.getURL('pickvocab.svg');
  logoImg.alt = 'Pickvocab';

  // Only append the icon (no text)
  button.innerHTML = '';
  button.appendChild(logoImg);

  // Initially position offscreen but visible so we can measure it
  button.style.position = 'fixed';
  button.style.left = '-9999px';
  button.style.top = '0';
  button.style.display = 'flex';
  button.style.visibility = 'hidden';

  // Add to body directly
  document.body.appendChild(button);

  // Add hover effect with direct event listeners
  button.addEventListener('mouseover', () => {
    // button.style.backgroundColor = '#f4f4f5';
    button.style.transform = 'scale(1.05) translateX(-50%)';
    logoImg.style.border = 'none';
    logoImg.style.filter = 'none';
  });

  button.addEventListener('mouseout', () => {
    // button.style.backgroundColor = 'white';
    button.style.transform = 'translateX(-50%)';
    logoImg.style.border = 'none';
    logoImg.style.filter = 'none';
  });

  // Track state
  let positioningDisabled = false;
  let isPositioningInProgress = false;
  let lastSelectionText = '';
  let lastSelectionRect: { top: number; left: number } | null = null;
  let isPopupOpen = false;

  // Function to position the button
  const positionButton = () => {
    if (positioningDisabled || isPositioningInProgress) return;

    try {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) {
        button.style.display = 'none';
        return;
      }

      const selectedText = selection.toString().trim();

      if (selectedText.length === 0) {
        button.style.display = 'none';
        return;
      }

      const range = selection.getRangeAt(0);

      // Get context data FIRST, including the potential targetElement
      const { passage, offset, fontSize, targetElement } = getSurroundingPassage(selection);

      // Start positioning process - set flag
      isPositioningInProgress = true;
      // Hide button while measuring/positioning
      button.style.visibility = 'hidden';
      button.style.display = 'none'; // Also set display none initially

      const completePositioning = (rect: DOMRect | null) => {
          if (!rect || (rect.width === 0 && rect.height === 0)) {
              console.warn('[PickVocab] Positioning aborted, invalid target rect:', rect);
              button.style.display = 'none';
              isPositioningInProgress = false;
              return;
          }

          const scrollX = window.scrollX;
          const scrollY = window.scrollY;
          const detectedFontSize = fontSize || 14;

          applyButtonStyles(button, logoImg, detectedFontSize);
          void button.offsetHeight; // Force reflow
          const buttonHeight = button.offsetHeight;

          const buttonX = rect.left + scrollX + (rect.width / 2);
          const extraSpacing = Math.min(5, detectedFontSize * 1.2);
          const buttonY = rect.top + scrollY - buttonHeight - extraSpacing;

          button.style.left = buttonX + 'px';
          button.style.top = buttonY + 'px';
          button.style.transform = 'translateX(-50%)';
          button.style.display = 'flex';
          button.style.visibility = 'visible';

          const currentSelectedText = selection.toString().trim(); // Use fresh selection text
          button.onclick = async function(e) {
              positioningDisabled = true;
              isPopupOpen = true;
              e.preventDefault();
              e.stopPropagation();
              button.style.display = 'none';
              // Re-fetch selection details just before creating popup for max accuracy?
              // Might be overkill, but consider if passage/offset issues arise.
              // const freshSelection = window.getSelection();
              // const freshText = freshSelection?.toString().trim();
              // const { passage: freshPassage, offset: freshOffset, fontSize: freshFontSize } = getSurroundingPassage(freshSelection);

              await createFloatingPopup(
                  ctx,
                  currentSelectedText, // Use current text
                  passage, // Note: passage/offset might still be inaccurate from initial getSurroundingPassage
                  offset,
                  fontSize,
                  'menu',
                  () => {
                      isPopupOpen = false;
                      positioningDisabled = false;
                  },
                  targetElement instanceof HTMLElement ? targetElement : undefined
              );
          };

          isPositioningInProgress = false;
      };

      // Determine the primary element for positioning reference if needed (mainly for targetElement case)
      let positioningTargetElement: Element | null = null;
      if (targetElement) {
          positioningTargetElement = targetElement;
      } else {
          let startContainer = range.startContainer;
          if (startContainer) {
              if (startContainer.nodeType === Node.ELEMENT_NODE) {
                  positioningTargetElement = startContainer as Element;
              } else if (startContainer.nodeType === Node.TEXT_NODE && startContainer.parentElement) {
                  positioningTargetElement = startContainer.parentElement;
              }
          }
      }

      // --- Call RAF with simplified parameters --- 
      requestAnimationFrame(() => {
          calculateAndCompletePositioning(
              range, 
              targetElement, // Pass only the specific input/textarea if found
              positioningTargetElement, // Pass potential container element
              completePositioning
          );
      });

    } catch (error) {
      console.error("Error in positionButton:", error);
      isPositioningInProgress = false;
      isPopupOpen = false;
      positioningDisabled = false;
      button.style.display = 'none'; // Ensure button is hidden on error
    }
  };

  // Add event listeners with proper debouncing
  const handleMouseUp = debounce((e: MouseEvent) => {
    if (isPopupOpen) return;

    const popupShadowHost = document.getElementById('wxt-floating-popup-container')?.shadowRoot?.host;
    if (popupShadowHost && popupShadowHost.contains(e.target as Node)) {
      return;
    }
    if (button.contains(e.target as Node)) {
       return;
    }
    positionButton();
  }, 100);

  const handleSelectionChange = debounce(() => {
    if (isPopupOpen) return;
    if (!positioningDisabled) {
      positionButton();
    }
  }, 200);

  const handleMouseDown = (e: MouseEvent) => {
    if (isPopupOpen) return;

    const popupShadowHost = document.getElementById('wxt-floating-popup-container')?.shadowRoot?.host;
    if (button.contains(e.target as Node) || (popupShadowHost && popupShadowHost.contains(e.target as Node))) {
      return;
    }
    button.style.display = 'none';
  };

  document.addEventListener('mouseup', handleMouseUp);
  document.addEventListener('selectionchange', handleSelectionChange);
  document.addEventListener('mousedown', handleMouseDown);

  // Return cleanup function
  return () => {
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('selectionchange', handleSelectionChange);
    document.removeEventListener('mousedown', handleMouseDown);
    button.remove();
  };
}

/**
 * Helper function to apply responsive styles to the lookup button based on detected font size
 */
function applyButtonStyles(button: HTMLButtonElement, logoImg: HTMLImageElement, fontSize: number) {
  // Constrain font size within reasonable bounds
  const boundedFontSize = Math.max(12, Math.min(18, fontSize)) * BUTTON_SCALE;

  // Scale various properties based on the font size
  const paddingV = Math.max(4, boundedFontSize * 0.4);
  const paddingH = Math.max(8, boundedFontSize * 0.8); // Increased horizontal padding for better clickability
  const borderRadius = Math.max(3, boundedFontSize * 0.25);
  const logoSize = Math.max(18, boundedFontSize * 1.7); // Slightly larger for icon-only
  const shadowSize = Math.max(4, boundedFontSize * 0.4);

  // Add back transition speed but ONLY for hover effects, not position
  const transitionSpeed = Math.max(0.15, 0.3 - (boundedFontSize * 0.01)); // Faster transitions for larger fonts

  // Apply logo styles
  logoImg.setAttribute('style', `
    width: ${logoSize}px;
    height: ${logoSize}px;
    margin: 0;
    display: block;
    border: none;
    border-radius: 3px;
    filter: none;
  `);

  // Apply button styles
  button.setAttribute('style', `
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    z-index: 2147483647;
    padding: ${paddingV}px ${paddingH}px;
    background-color: white;
    border: 0;
    border-radius: ${borderRadius}px;
    box-shadow: 0 ${shadowSize}px ${shadowSize * 2}px rgba(0, 0, 0, 0.1);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    font-size: ${boundedFontSize}px;
    font-weight: bold;
    color: #111827;
    cursor: pointer;
    transform: translateX(-50%);
    transform-origin: center center;
    user-select: none;
    min-width: ${logoSize * 2}px;
    min-height: auto;
    margin: 0;
    outline: none;
    transition: background-color ${transitionSpeed}s ease, transform ${transitionSpeed}s ease, box-shadow ${transitionSpeed}s ease;
  `);
}

export async function createFloatingPopup(
  ctx: ContentScriptContext,
  word: string,
  context: string,
  contextOffset: number,
  fontSize?: number,
  initialAction: 'menu' | 'lookup' | 'fix writing' = 'menu',
  onCloseCallback?: () => void,
  anchorElement?: HTMLElement | null
) {
  // Store the initial selection range for tracking
  let initialSelection: Selection | null = window.getSelection();
  let initialRange: Range | null = initialSelection && initialSelection.rangeCount > 0 ?
    initialSelection.getRangeAt(0).cloneRange() : null;

  // Save the text nodes and offsets
  let anchorNode = initialSelection?.anchorNode;
  let anchorOffset = initialSelection?.anchorOffset;
  let focusNode = initialSelection?.focusNode;
  let focusOffset = initialSelection?.focusOffset;

  const ui = await createShadowRootUi(ctx, {
    name: "floating-popup-container",
    position: "inline",
    anchor: document.body,
    onMount: async (uiContainer) => {
      const popup = document.createElement("div");
      popup.id = "wxt-floating-popup-container";

      const pinia = createPinia();
      const popupApp = createApp(FloatingPopupContainer);
      popupApp.use(pinia);
      const contentStore = useContentStore(pinia);
      contentStore.setInitialData(word, context, contextOffset, initialAction);
      popupApp.mount(popup);

      let cleanupFloatingUI: (() => void) | null = null;
      const floatingZIndex = "1000"; // Define z-index for floating state

      // --- Helper function to set up floating behavior ---
      const setupFloatingBehavior = () => {
        // Define getVirtualElement here, capturing necessary scope variables
        const getVirtualElement = () => ({
           getBoundingClientRect: () => {
            // Prioritize anchorElement if available (e.g., for input/textarea)
            if (anchorElement) return anchorElement.getBoundingClientRect();
            // Fallback to selection range/nodes
            try {
              if (initialRange) return initialRange.getBoundingClientRect();
              if (anchorNode && focusNode) {
                const tempRange = document.createRange();
                // Use non-null assertion carefully, ensure nodes exist if range doesn't
                tempRange.setStart(anchorNode!, anchorOffset || 0);
                tempRange.setEnd(focusNode!, focusOffset || 0);
                return tempRange.getBoundingClientRect();
              }
            } catch (e) { console.log('Error getting selection position:', e); }
            // Final fallback
            return { width: 0, height: 0, x: 0, y: 0, top: 0, left: 0, right: 0, bottom: 0, toJSON: () => {} };
          },
        });

        // Apply absolute positioning styles
        Object.assign(popup.style, {
          position: "absolute",
          left: "0px", // Reset before compute
          top: "0px",  // Reset before compute
          transform: "none", // Remove any previous transform
          zIndex: floatingZIndex,
          display: "block",
        });

        // Stop existing cleanup if any before starting new one
        if (cleanupFloatingUI) cleanupFloatingUI();

        // Start autoUpdate
        cleanupFloatingUI = autoUpdate(getVirtualElement(), popup, async () => {
          const { x, y } = await computePosition(getVirtualElement(), popup, {
            placement: "top",
            strategy: "absolute",
            middleware: [ offset(15), flip(), shift({ padding: 5 }) ],
          });
          Object.assign(popup.style, { left: `${x}px`, top: `${y}px` });
        });
      };
      // --- End Helper Function ---

      // --- Initial Positioning ---
      if (initialAction === 'fix writing') {
        Object.assign(popup.style, { // Apply fixed styles
          position: 'fixed', top: '50%', left: '50%',
          transform: 'translate(-50%, -50%)', zIndex: '2147483647', display: 'block',
        });
      } else {
        setupFloatingBehavior(); // Setup floating for 'menu' or 'lookup'
      }

      // --- Watcher for Action Change --- 
      watch(() => contentStore.selectedAction, (newAction) => {
        if (newAction === 'fix writing') {
          // Apply fixed styles
          Object.assign(popup.style, {
            position: 'fixed', top: '50%', left: '50%',
            transform: 'translate(-50%, -50%)', zIndex: '2147483647',
          });
          // Clean up floating UI if it was active
          if (cleanupFloatingUI) {
            cleanupFloatingUI();
            cleanupFloatingUI = null;
          }
        } else {
          // Action changed AWAY from 'fix writing'
          // Only re-setup floating if it's not currently active (cleanupFloatingUI is null)
          if (cleanupFloatingUI === null) {
             setupFloatingBehavior(); // Re-enable floating behavior
          }
        }
      });
      // --- End Watcher ---

      if (fontSize) {
        const style = document.createElement("style");
        style.textContent = generateTailwindFontSizes(Math.min(fontSize, 16)); // Ensure generateTailwindFontSizes exists
        uiContainer.appendChild(style);
      }
      uiContainer.append(popup);

      const closePopup = (event: MouseEvent) => {
        if (!popup.contains(event.target as Node) && event.target !== ui.shadowHost) {
          positioningDisabled = false; // Reset global state if needed
          if (onCloseCallback) {
            onCloseCallback();
          }
          ui.remove();
          document.removeEventListener("click", closePopup, true);
          // Cleanup floating UI if it exists
          if (cleanupFloatingUI) {
            cleanupFloatingUI();
          }
        }
      };
      document.addEventListener("click", closePopup, true);
    },
    onRemove: (uiContainer) => {
        // Cleanup might be needed here too if removed externally before click
        // Though closePopup usually handles it.
    }
  });

  ui.mount();
}

// --- Define Helper Function for RAF Callback (Simplified) --- 
const calculateAndCompletePositioning = (
  currentRange: Range,
  pTargetElement: HTMLInputElement | HTMLTextAreaElement | null | undefined, // Explicit input/textarea
  pPosTargetElement: Element | null, // Potential container element (startContainer parent)
  onComplete: (rect: DOMRect | null) => void 
) => {
    let calculationRect: DOMRect | null = null;

    try {
      // Priority 1: Use the specific input/textarea element if it exists
      if (pTargetElement) {
          calculationRect = pTargetElement.getBoundingClientRect();
          if (!calculationRect || (calculationRect.width === 0 && calculationRect.height === 0)) {
             console.warn("[PickVocab] Target element rect is invalid, falling back to range rect.");
             calculationRect = null; // Force fallback
          }
      }

      // Priority 2 (or fallback): Use the selection range's bounding box directly
      if (!calculationRect) {
         calculationRect = currentRange.getBoundingClientRect();
      }

      // Final Check: If rangeRect is also invalid, maybe use container as last resort?
      // For now, let's rely on rangeRect being valid in most non-targetElement cases.
      if (!calculationRect || (calculationRect.width === 0 && calculationRect.height === 0)) {
          console.warn("[PickVocab] Range bounding rect is invalid. Attempting positioning target element fallback.");
          // As a last resort, try the positioningTargetElement (start container's parent) if range failed
          if(pPosTargetElement) {
            calculationRect = pPosTargetElement.getBoundingClientRect();
          }
          // If still invalid, onComplete will handle the null/zero rect
      }

    } catch (e) {
       console.error("[PickVocab] Error getting bounding rect in RAF:", e);
       calculationRect = null; // Ensure it's null on error
    }

    // Pass the chosen rect (primarily rangeRect or targetElement rect) to completePositioning
    onComplete(calculationRect);
};

