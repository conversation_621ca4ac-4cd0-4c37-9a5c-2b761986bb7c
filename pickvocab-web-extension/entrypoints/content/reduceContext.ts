import { split } from 'sentence-splitter';

export function reduceContext(
  text: string,
  selectedText: string,
  offset: number,
  windowSize = 1
): {
  text: string,
  selectedText: string,
  offset: number
} {
  const sentences = split(text).filter((ele) => ele.type === 'Sentence');

  let startIdx = 0;
  for (let i = sentences.length - 1; i >= 0; i -= 1) {
    if (sentences[i].range[0] <= offset) {
      startIdx = i;
      break;
    }
  }

  const endIdx = sentences.findIndex((sentences) => sentences.range[1] >= offset + selectedText.length);

  const from = Math.max(0, startIdx - windowSize);
  const to = Math.min(endIdx + windowSize, sentences.length - 1);
  return {
    text: text.slice(sentences[from].range[0], sentences[to].range[1]),
    selectedText,
    offset: offset - sentences[from].range[0]
  }
}
