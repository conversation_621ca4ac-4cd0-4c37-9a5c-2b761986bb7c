<script setup lang="ts">
// @ts-ignore
import IconReload from "@tabler/icons-vue/dist/esm/icons/IconReload.mjs";
// @ts-ignore
import IconCheck from "@tabler/icons-vue/dist/esm/icons/IconCheck.mjs";
import { storeToRefs } from "pinia";
import { useLLMStore } from "../popup/stores/llm";
import { useContentStore } from "./store";
import {
  Dictionary,
  DictionarySource,
  WordInContextEntry,
} from "pickvocab-dictionary";
import { sendMessage } from "webext-bridge/content-script";
import FunctionalExplanationView from "./FunctionalExplanationView.vue";
import Spinner from "@/components/Spinner.vue";
import DictionaryWordViewInfoAlert from "@/components/lookup/DictionaryWordViewInfoAlert.vue";
import DictionaryWordViewErrorAlert from "@/components/lookup/DictionaryWordViewErrorAlert.vue";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { reduceContext } from "./reduceContext";
import { BaseContextCard } from "@/utils/card";
import { useAuthStore } from "../popup/stores/auth";
import { Switch } from '@/components/ui/switch'

const contentStore = useContentStore();
const llmStore = useLLMStore();
const authStore = useAuthStore();
llmStore.setExtensionUI("content-script");

const shadowRoot = useTemplateRef("shadowRoot");
const { word, context, offset } = storeToRefs(contentStore);
const wordEntry = ref<WordInContextEntry | undefined>(undefined);
const isLoading = ref(false);
const errorMessage = ref("");

const currentView: Ref<"definition" | "addCard"> = ref("definition");
const addCardError = ref("");
const cardUrl = ref("");
const isSaved = ref(false);
const isDetailed = ref(false);

const dictionary = computed(() => {
  if (!llmStore.isHydrated) return undefined;
  let sources: DictionarySource[] = [llmStore.pickvocabDictionarySource];
  if (llmStore.activeUserModel) {
    sources = [
      llmStore.createDictionarySource(llmStore.activeUserModel),
      ...sources,
    ];
  }
  const dictionary = new Dictionary(sources);
  return dictionary;
});

const llmModel = computed(() => {
  return wordEntry.value
    ? llmStore.getModelById(wordEntry.value?.llm_model)
    : undefined;
});

watch(isDetailed, () => {
  if (word.value === undefined || context.value === undefined || offset.value === undefined) return;
  if (isDetailed.value) {
    if (wordEntry.value?.definition) return;
    refresh();
  } else {
    if (wordEntry.value?.definitionShort) return;
    refresh();
  }
});

const reducedContext = computed(() => {
  return reduceContext(context.value, word.value, offset.value, 3);
});

async function handleLookup() {
  if (dictionary.value === undefined) throw new Error("Expected dictionary");

  try {
    wordEntry.value = undefined;
    isLoading.value = true;
    const base = isDetailed.value ? await dictionary.value.getMeaningInContext(
      reducedContext.value.selectedText,
      reducedContext.value.text,
      reducedContext.value.offset,
    ) : await dictionary.value.getMeaningInContextShort(
      reducedContext.value.selectedText,
      reducedContext.value.text,
      reducedContext.value.offset,
    );
    // wordEntry.value = await api.create(base);
    wordEntry.value = (await sendMessage(
      "wordInContext:create",
      { base: base as any },
      "background"
    )) as any as WordInContextEntry;
    isLoading.value = false;
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
  }
}

async function refresh() {
  if (dictionary.value === undefined) throw new Error("Expected dictionary");

  try {
    const oldEntry = wordEntry.value!;
    wordEntry.value = undefined;
    isLoading.value = true;
    const base = isDetailed.value ? await dictionary.value.getMeaningInContext(
      reducedContext.value.selectedText,
      reducedContext.value.text,
      reducedContext.value.offset,
    ) : await dictionary.value.getMeaningInContextShort(
      reducedContext.value.selectedText,
      reducedContext.value.text,
      reducedContext.value.offset,
    );
    const newEntry = {
      ...oldEntry,
      ...base,
    };
    wordEntry.value = (await sendMessage(
      "wordInContext:put",
      { word: newEntry },
      "background"
    )) as any as WordInContextEntry;
    isLoading.value = false;
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
  }
}

onMounted(async () => {
  await Promise.allSettled([llmStore.hydrate(), authStore.hydrate()]);
  await handleLookup();
});

const showInfoMessage = computed(() => {
  return llmStore.shouldShowAPIKeyAlert();
});

function setupApiKey() {
  sendMessage("openOptionsPage", {}, "background");
}

async function hideApiKeyAlert() {
  llmStore.lastShowAPIKeyAlert = Date.now();
}

async function addCard(wordEntry: WordInContextEntry) {
  if (!authStore.isAuthenticated) {
    addCardError.value = "You need to sign in to save this word";
    currentView.value = "addCard";
    return;
  }
  const baseCard: BaseContextCard = {
    wordInContext: wordEntry,
  };
  const card = (await sendMessage(
    "contextCard:create",
    { card: baseCard as any },
    "background"
  )) as any as WordInContextEntry;

  cardUrl.value = `${import.meta.env.WXT_CLIENT_API_URL}/app/cards/${card.id}`;
  currentView.value = "addCard";
  isSaved.value = true;

  // sendMessage("openTab", {
  //   url: `${import.meta.env.WXT_CLIENT_API_URL}/app/cards/${card.id}`
  // }, "background");
}

async function signIn() {
  await sendMessage('googleSignIn', {}, 'background') as string;
  // reload new user data from storage
  authStore.isHydrated = false;
  await authStore.hydrate();
}

async function addCardSignIn() {
  await signIn();
  await addCard(wordEntry.value!);
}

function backToDefinitionView(status = false) {
  currentView.value = "definition";
  addCardError.value = "";
  cardUrl.value = "";
  isSaved.value = status;
}
</script>

<template>
  <div
    ref="shadowRoot"
    class="!visible bg-white min-w-[450px] max-w-[600px] max-h-[400px] overflow-auto p-6 border border-gray-200 shadow-2xl rounded"
  >
    <div v-if="currentView === 'definition'">
      <DictionaryWordViewInfoAlert
        v-if="wordEntry && showInfoMessage"
        class="mb-4"
        @setup="setupApiKey()"
        @dismiss="hideApiKeyAlert()"
      >
      </DictionaryWordViewInfoAlert>
      <DictionaryWordViewErrorAlert
        class="mb-4"
        v-if="errorMessage"
        @retry="refresh()"
        @setup="setupApiKey()"
        :message="errorMessage"
        :is-active-user-model="llmStore.activeUserModel ? true : false"
      />
      <FunctionalExplanationView v-if="wordEntry" :word-entry="wordEntry" :show-view-detailed="isDetailed">
        <template #wordInfo>
          <TooltipProvider :delay-duration="100">
            <Tooltip>
              <TooltipTrigger>
                <icon-reload
                  @click="refresh()"
                  stroke-width="2.5"
                  class="inline-block text-gray-400 w-4 h-4 cursor-pointer hover:text-blue-500"
                ></icon-reload>
              </TooltipTrigger>
              <TooltipContent :teleportTo="shadowRoot as any">
                <div
                  v-if="wordEntry"
                  class="flex items-center font-medium text-sm bg-white"
                >
                  <span v-if="llmModel" class="flex items-center">
                    <img
                      :src="llmStore.providerMap[llmModel.provider].logo"
                      alt="llm-model-img"
                      class="w-4 h-4"
                    />
                    <span class="ml-1">{{ llmModel.name }}&nbsp;</span>
                  </span>
                  <span v-if="wordEntry.createdAt">{{
                    new Date(wordEntry.createdAt).toLocaleString()
                  }}</span>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </template>

        <template #settings>
          <div class="flex items-center">
            <button class="flex items-center mr-2">
              <Switch v-model:checked="isDetailed" class="data-[state=checked]:bg-blue-600 focus-visible:ring-blue-600" />
              <p class="ml-1 text-sm">Detailed</p>
            </button>
            <button
              type="button"
              v-if="!isSaved"
              class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-1.5 focus:outline-none ml-auto"
              @click="addCard(wordEntry)"
            >
              Save
            </button>
            <button
              type="button"
              v-else
              class="flex text-gray-300 border border-gray-300 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-2 py-1.5 ml-auto"
              :disabled="true"
            >
              <icon-check class="w-4 h-4 mr-1" />
              <span>Saved</span>
            </button>
          </div>
        </template>

        <!-- <template #settings>
          <Dropdown
            :offsetDistance="-10"
            :offsetSkidding="-70"
            class="ml-auto sm:hidden"
            :hide="!wordEntry"
          >
            <template #trigger>
              <div class="p-2 hover:rounded hover:bg-gray-100 cursor-pointer">
                <icon-dots-vertical class="w-5 h-5"></icon-dots-vertical>
              </div>
            </template>
            <template #body>
              <div
                v-if="wordEntry"
                class="z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600"
              >
                <div class="px-4 py-3" role="none">
                  <div v-if="llmModel" class="flex items-center">
                    <img
                      :src="llmStore.providerMap[llmModel.provider].logo"
                      alt="llm-model-img"
                      class="w-4 h-4"
                    />
                    <p
                      class="ml-1 text-sm font-medium text-gray-900 dark:text-white"
                      role="none"
                    >
                      {{ llmModel.name }}
                    </p>
                  </div>
                  <p
                    v-if="wordEntry && wordEntry.createdAt"
                    class="mt-1 text-sm text-gray-600 truncate dark:text-gray-300"
                    role="none"
                  >
                    {{ new Date(wordEntry.createdAt).toLocaleString() }}
                  </p>
                </div>
                <ul class="py-1" role="none">
                  <li>
                    <div
                      @click="addCard(wordEntry)"
                      class="flex items-center px-5 py-2 text-sm text-gray-600 hover:bg-gray-100 cursor-pointer"
                      role="menuitem"
                    >
                      <icon-cards class="inline-block w-4 h-4"></icon-cards>
                      <span class="ml-2">Save</span>
                    </div>
                  </li>
                  <li>
                    <div
                      @click="refresh()"
                      class="flex items-center px-5 py-2 text-sm text-gray-600 hover:bg-gray-100 cursor-pointer"
                      role="menuitem"
                    >
                      <icon-reload class="inline-block w-4 h-4"></icon-reload>
                      <span class="ml-2">Re-lookup</span>
                    </div>
                  </li>
                </ul>
              </div>
            </template>
          </Dropdown>

          <button
            type="button"
            class="hidden sm:block text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-1.5 focus:outline-none ml-auto"
            @click="addCard(wordEntry)"
          >
            Save
          </button>
        </template> -->
      </FunctionalExplanationView>
      <div
        v-else-if="!errorMessage"
        class="w-full h-full flex items-center justify-center"
      >
        <Spinner :size="6" />
      </div>
    </div>
    <div v-else-if="currentView === 'addCard'">
      <div
        v-if="isSaved"
        class="flex flex-col items-center justify-center"
      >
        <p class="text-lg font-semibold text-gray-800">
          Word saved successfully!
        </p>
        <a
          :href="cardUrl"
          target="_blank"
          class="text-blue-700 hover:text-blue-800 mt-2"
        >
          View card
        </a>
        <div class="flex mt-4 space-x-2">
          <button type="button"
            @click="backToDefinitionView(true)"
            class="text-blue-800 bg-transparent border border-blue-800 hover:bg-blue-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:hover:bg-blue-600 dark:border-blue-600 dark:text-blue-400 dark:hover:text-white dark:focus:ring-blue-800"
            data-dismiss-target="#alert-additional-content-1" aria-label="Close">
            Back
          </button>
        </div>
      </div>
      <div v-else>
        <p class="text-lg font-semibold text-gray-800">{{ addCardError }}</p>
        <div class="flex mt-4 space-x-2">
          <button type="button"
            @click="addCardSignIn()"
            class="text-white bg-blue-800 hover:bg-blue-900 focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
            Sign In
          </button>
          <button type="button"
            @click="backToDefinitionView(false)"
            class="text-blue-800 bg-transparent border border-blue-800 hover:bg-blue-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:hover:bg-blue-600 dark:border-blue-600 dark:text-blue-400 dark:hover:text-white dark:focus:ring-blue-800"
            data-dismiss-target="#alert-additional-content-1" aria-label="Close">
            Back
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
