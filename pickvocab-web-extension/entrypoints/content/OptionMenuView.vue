<script setup lang="ts">
import { useContentStore } from "./store";
import { Button } from "@/components/ui/button";
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
// @ts-ignore
import IconEdit from '@tabler/icons-vue/dist/esm/icons/IconEdit.mjs';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';

const contentStore = useContentStore();

function selectLookup() {
  contentStore.setSelectedAction('lookup');
}

function handleFixWriting() {
  contentStore.setSelectedAction('fix writing');
}
</script>

<template>
  <div id="pickvocab-option-menu"
    class="!visible bg-white min-w-[280px] max-w-[320px] rounded-lg shadow-lg overflow-hidden">
    <div class="flex flex-col">
      <!-- Option buttons -->
      <Button
        variant="ghost"
        size="default"
        class="w-full flex items-center py-3 px-4 justify-start hover:bg-gray-100"
        @click="selectLookup"
      >
        <IconSearch class="mr-2 h-5 w-5 text-gray-700" />
        <span class="flex-1 text-left font-semibold text-gray-700">Lookup</span>
      </Button>

      <Button
        variant="ghost"
        size="default"
        class="w-full flex items-center py-3 px-4 justify-start text-gray-500 hover:bg-gray-100"
        @click="handleFixWriting"
      >
        <IconEdit class="mr-2 h-5 w-5" />
        <span class="flex-1 text-left font-semibold text-gray-700">Fix writing</span>
      </Button>
    </div>
  </div>
</template>

<style scoped>
/* Override disabled button styles to match the design */
button[disabled] {
  opacity: 0.7;
  cursor: not-allowed;
}
</style>