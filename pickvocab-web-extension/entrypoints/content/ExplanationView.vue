<script setup lang="ts">
import { computed } from 'vue';
import type { WordInContextEntry } from 'pickvocab-dictionary';
import ExplanationWordInfo from './ExplanationWordInfo.vue';
import ExplanationWordSetting from './ExplanationWordSetting.vue';
import ExplanationHeader from './ExplanationHeader.vue';
import ExplanationSimple from './ExplanationSimple.vue';
import ExplanationFull from './ExplanationFull.vue';
import Spinner from '@/components/Spinner.vue';

const props = withDefaults(defineProps<{
  word?: string,
  wordEntry?: WordInContextEntry,
  llmModel?: any,
  isLoading?: boolean,
}>(), {
  isLoading: false,
});

const isDetailed = defineModel<boolean>('isDetailed', { default: false });
const selectedSimpleViewLanguage = defineModel<string>('selectedSimpleViewLanguage', {
  default: 'English'
});

const emit = defineEmits<{
  (e: 'update:isDetailed', isDetailed: boolean): void;
  (e: 'update:selectedSimpleViewLanguage', language: string | undefined): void;
  (e: 'addCard', wordEntry: WordInContextEntry, callback?: () => void): void;
  (e: 'refresh', language?: string): void;
  (e: 'simpleLookupForLanguage', language: string): void;
}>();

const isMac = computed(() => {
  return /Mac|iPhone|iPad|iPod/i.test(navigator.userAgent);
});
</script>

<template>
  <div>
    <ExplanationHeader v-if="word" :word="word">
      <template #wordInfo>
        <ExplanationWordInfo 
          :word-entry="wordEntry" 
          :llm-model="llmModel" 
          @refresh="emit('refresh', isDetailed ? 'English' : selectedSimpleViewLanguage)" 
        />
      </template>

      <template #settings>
        <ExplanationWordSetting 
          :word-entry="wordEntry" 
          :llm-model="llmModel" 
          :is-loading="isLoading"
          @add-card="(wordEntry, callback) => emit('addCard', wordEntry, callback)" 
          v-model:is-detailed="isDetailed" 
        />
      </template>
    </ExplanationHeader>

    <Spinner v-if="isLoading" class="mt-6 mb-6" 
      :show-shortcut-tip="true" 
      :shortcut-tip="isMac ? 'Cmd+Shift+H' : 'Ctrl+Shift+H'"
      shortcut-message="to quickly look up any selected text"
    />

    <ExplanationSimple
      v-if="wordEntry && !isDetailed"
      :word-entry="wordEntry"
      v-model:selected-language="selectedSimpleViewLanguage"
      class="mt-4"
    />
    
    <ExplanationFull
      v-if="wordEntry && isDetailed"
      :word-entry="wordEntry"
      class="mt-4"
    />
  </div>
</template>