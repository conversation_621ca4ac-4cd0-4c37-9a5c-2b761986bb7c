# Development Plan: Introduce Option Menu

## Introduction

This document outlines the plan to modify the PickVocab web extension's floating popup functionality. The goal is to introduce an intermediate options menu that appears upon text selection or shortcut activation, allowing users to choose an action (like "Lookup", "Fix writing", etc.) before the corresponding view is displayed.

## Background Context

Currently, when a user selects text on a webpage and clicks the floating lookup button, the extension directly fetches and displays the definition of the selected word in a `DefinitionPopup.vue` component. This process is initiated via the `createDefinitionPopup` function in `utils.ts`.

To accommodate future features like grammar checking and writing fixes, we need to insert a step between the trigger (button click/shortcut) and the action view. This step will involve displaying a new `OptionMenuView.vue` component. Users will first see this menu and select an action. If they choose "Lookup", the existing `DefinitionPopup.vue` will be shown. Importantly, this new menu will only appear when the floating button is clicked; triggers definition lookup via keyboard shortcut or the context menu will directly display the definition lookup view as before.

## Implementation Plan

This plan outlines the steps to introduce an option menu before showing the definition popup. The implementation will follow these key principles:

- **Component Reuse**: Leverage existing shadcn UI components for consistency
- **Styling Consistency**: Match the existing popup styling
- **Accessibility**: Ensure keyboard navigation and screen reader support
- **Smooth Transitions**: Add animations between different views
- **Focused Changes**: Don't modify code that's irrelevant to the task at hand

1.  **Create `OptionMenuView.vue`:**
    *   Create `pickvocab-web-extension/entrypoints/content/OptionMenuView.vue`.
    *   It will display buttons: "Lookup", "Fix writing (disabled)", "Check grammar (disabled)".
    *   Use shadcn UI components for consistency:
        *   Import and use `Button` from `@/components/ui/button` for the option buttons.
        *   Apply appropriate variants (e.g., `variant="default"` for Lookup, `variant="outline"` for disabled options).
        *   Use consistent sizing (e.g., `size="sm"`) for all buttons to match the extension's UI.
    *   On clicking "Lookup", it will call an action in the Pinia store to set the state to 'lookup'.
    *   Add appropriate styling to match the existing popup design (padding, spacing, etc.).

2.  **Create `FloatingPopupContainer.vue`:**
    *   Create `pickvocab-web-extension/entrypoints/content/FloatingPopupContainer.vue`.
    *   This component will be the root component mounted by the popup creation logic.
    *   It will use the Pinia store (`useContentStore`) to get the current action state (`selectedAction`).
    *   It will conditionally render `<OptionMenuView />` if `selectedAction === 'menu'` (or similar).
    *   It will conditionally render `<DefinitionPopup />` if `selectedAction === 'lookup'`.
    *   It will import both `OptionMenuView.vue` and `DefinitionPopup.vue`.
    *   Apply consistent styling using the same CSS classes as the existing popup:
        *   Use the same background, border, shadow, and rounded corners.
        *   Maintain the same max-width and max-height constraints.
        *   Ensure proper overflow handling for scrollable content.

3.  **Update Pinia Store (`store.ts`):**
    *   Modify `pickvocab-web-extension/entrypoints/content/store.ts`.
    *   Add a new state property, e.g., `selectedAction: 'menu' | 'lookup' | 'fix' | 'grammar' = 'menu'`.
    *   Add a new action, e.g., `setSelectedAction(action: string)`.
    *   Modify or create an action (e.g., `setInitialData`) that accepts `word`, `context`, `offset`, and an `initialAction` parameter ('menu' or 'lookup'). This action will update the relevant data state properties and set `selectedAction` based on the `initialAction` parameter received.

4.  **Create Generic Popup Function (`utils.ts`):**
    *   In `pickvocab-web-extension/entrypoints/content/utils.ts`, create a new function `createFloatingPopup`.
    *   Signature: `createFloatingPopup(ctx, word, context, contextOffset, fontSize, initialAction: 'menu' | 'lookup')`.
    *   This function will:
        *   Mount `FloatingPopupContainer.vue`.
        *   Call the Pinia store action (e.g., `setInitialData`) to store `word`, `context`, `offset`, and set the initial `selectedAction` based on the `initialAction` argument.
        *   Handle shadow DOM, Floating UI setup (`autoUpdate`), and cleanup logic similar to `createDefinitionPopup`.
    *   Refer to the existing `createDefinitionPopup` function in `utils.ts` for guidance on mounting, shadow DOM setup, Floating UI integration (`autoUpdate`), and cleanup logic.

5.  **Update Callers (`content.ts`):**
    *   Modify `pickvocab-web-extension/entrypoints/content.ts`.
    *   In the `createFloatingLookupButton` callback: Replace the call to `createDefinitionPopup` with a call to `createFloatingPopup(..., initialAction: 'menu')`.
    *   In the `onMessage("showPopupDefinition")` handler and the `keydown` listener: Replace calls to `createDefinitionPopup` with calls to `createFloatingPopup(..., initialAction: 'lookup')`. Ensure all necessary arguments (`word`, `context`, etc.) are passed correctly.
