<script setup lang="ts">
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';
// @ts-ignore
import IconBookmark from '@tabler/icons-vue/dist/esm/icons/IconBookmark.mjs';
import type { WordInContextEntry } from 'pickvocab-dictionary';

const props = withDefaults(defineProps<{
  wordEntry: WordInContextEntry,
  showContext?: boolean,
  showViewDetailed?: boolean,
}>(), {
  showContext: false,
  showViewDetailed: true,
});

const emit = defineEmits(['save']);
const selectedLanguage = ref<string | undefined>('English');

function getHighlightedContext(wordEntry: WordInContextEntry) {
  const context = wordEntry.context;
  // replace the selected text with a mark bg-yellow-100
  const selectedText = context.substring(wordEntry.offset, wordEntry.offset + wordEntry.word.length);
  const highlightedContext = context.replace(selectedText, `<mark class="bg-[#fff8c5]">${selectedText}</mark>`);
  return highlightedContext;
}
</script>

<template>
  <div>
    <div class="flex items-center py-2 border-b">
      <p class="text-3xl text-gray-700 font-semibold">{{ wordEntry.word }}</p>

      <div v-if="$slots.wordInfo" class="ml-2">
        <slot name="wordInfo"></slot>
      </div>

      <div class="ml-auto" v-if="$slots.settings">
        <slot name="settings"></slot>
      </div>
    </div>

    <div v-if="showContext" class="mt-4">
      <blockquote class="text-gray-600 text-sm">
        <TiptapEditor
          :text="wordEntry.context"
          :selected-text="wordEntry.word"
          :offset="wordEntry.offset"
          :css-classes="'tiptap prose prose-sm sm:prose-base lg:prose-lg xl:prose-2xl focus:outline-none w-full max-h-[300px] overflow-auto !text-gray-600 !bg-gray-50 p-4'"
          :show-options="false"
          :show-bubble-menu="false"
        />
      </blockquote>
      <!-- <blockquote class="px-4 border-l-4 border-gray-200 border-solid italic text-gray-600 text-sm" v-html="getHighlightedContext(wordEntry)"></blockquote> -->
      <!-- <p class="text-gray-600 text-sm">The fact that the battery life is on par with the macbook air 15 while the vivobook has a 120Hz screen and two fans is plain bonkers for a windows machine</p> -->
    </div>

    <div class="mt-8">
      <fwb-select :disabled="true" class="sm:w-48" v-model="selectedLanguage" :options="languages" placeholder="Select Language" />
    </div>

    <template v-if="showViewDetailed && wordEntry.definition">
      <div class="flex mt-8 items-centers">
        <p class="text-base text-gray-600">
          <span class="inline-block text-sm border rounded-xl px-2 align-middle"
            :class="stylesForPartOfSpeech(wordEntry.definition.partOfSpeech)">
            {{ wordEntry.definition.partOfSpeech }}
          </span>
          <span class="ml-1">{{  wordEntry.definition.definition }}</span>
        </p>
        <!-- <div v-if="!showContext" @click="emit('save')"
          class="ml-1 text-gray-700">
          <div class="p-1 hover:rounded hover:bg-gray-100">
            <icon-bookmark class="w-4 h-4 cursor-pointer flex-shrink-0"></icon-bookmark>
          </div>
        </div> -->
      </div>

      <div class="mt-8">
        <p class="text-xl text-gray-700 font-semibold">Explanation</p>
        <p class="text-base text-gray-600 mt-2">{{  wordEntry.definition.explanation }}</p>
      </div>

      <div class="mt-8">
        <p class="text-xl text-gray-700 font-semibold">Examples</p>
        <ol class="list-decimal list-inside text-base text-gray-600">
          <li v-for="example in wordEntry.definition.examples" class="p-1 mt-2">
            <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">{{ example.example }}</blockquote>
            <p class="mt-4">{{ example.explanation }}</p>
          </li>
        </ol>
      </div>

      <div class="mt-8">
        <div class="flex items-center">
          <p class="text-xl text-gray-700 font-semibold">Synonyms</p>
        </div>
        <ol class="text-base text-gray-600 mt-4">
          <li v-for="synonym in wordEntry.definition.synonyms" class="mt-4">
            <!-- <NuxtLink :to="{ name: 'app-dictionary', query: { word: synonym.synonym } }"
              rel="nofollow"
              class="font-semibold underline text-blue-800">{{ synonym.synonym }}</NuxtLink> -->
            <a
              :href="`https://pickvocab.com/app/dictionary?word=${synonym.synonym}`"
              rel="nofollow"
              class="font-semibold underline text-blue-800"
              target="_blank">{{ synonym.synonym }}</a>
            <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">{{ synonym.example }}</blockquote>
            <p class="mt-4">{{  synonym.explanation }}</p>
          </li>
        </ol>
      </div>
    </template>
    <template v-else-if="!showViewDetailed && wordEntry.definitionShort">
      <div class="mt-8">
        <p class="text-xl text-gray-700 font-semibold">Explanation</p>
        <p class="text-base text-gray-600 mt-2">{{  wordEntry.definitionShort.explanation }}</p>
      </div>
    </template>

    <div class="mt-8 border-t">
      <!-- <NuxtLink :to="{ name: 'app-dictionary', query: { word: wordEntry.word } }"
        rel="nofollow"
        class="mt-4 font-semibold text-blue-800 flex items-center hover:underline">
        <span>See other definitions of "{{ wordEntry.word }}"</span>
        <icon-arrow-right class="w-4 h-4 ml-1"></icon-arrow-right>
      </NuxtLink> -->
      <a
        :href="`https://pickvocab.com/app/dictionary?word=${wordEntry.word}`"
        rel="nofollow"
        class="mt-4 font-semibold text-blue-800 flex items-center hover:underline"
        target="_blank">
        <span>See other definitions of "{{ wordEntry.word }}"</span>
        <icon-arrow-right class="w-4 h-4 ml-1"></icon-arrow-right>
      </a>
    </div>
  </div>
</template>

<style></style>
