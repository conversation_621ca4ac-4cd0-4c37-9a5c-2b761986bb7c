<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useContentStore } from "./store";
import OptionMenuView from "./OptionMenuView.vue";
import DefinitionPopup from "./DefinitionPopup.vue";
import WritingAssistantView from "@/components/write/WritingAssistantView.vue";
import ManageCustomTonesView from "@/components/write/ManageCustomTonesView.vue";
import { useWritingStore } from "@/components/write/stores/writingStore";

const contentStore = useContentStore();
const { selectedAction } = storeToRefs(contentStore);

const writingStore = useWritingStore();
const { writingAssistantSubView } = storeToRefs(writingStore);
</script>

<template>
  <div id="pickvocab-popup-container" class="!visible bg-white border border-gray-200 shadow-[0_1px_2px_rgba(0,0,0,0.05)] rounded">
    <transition name="fade" mode="out-in">
      <OptionMenuView v-if="selectedAction === 'menu'" />
      <DefinitionPopup v-else-if="selectedAction === 'lookup'" />
      <template v-else-if="selectedAction === 'fix writing'">
        <WritingAssistantView v-if="writingAssistantSubView === 'main'" />
        <ManageCustomTonesView v-else-if="writingAssistantSubView === 'manage_tones'" />
      </template>
    </transition>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>