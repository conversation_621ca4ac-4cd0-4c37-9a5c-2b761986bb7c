import { authenticateGoogleUser } from "@/api/auth";

function launchAuthFlow(authUrl: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const options = {
      url: authUrl,
      interactive: true,
    };

    browser.identity.launchWebAuthFlow(options, (redirectUrl) => {
      const error = browser.runtime.lastError;
      if (error || !redirectUrl) {
        return reject(
          new Error(
            `Authentication flow failed: ${
              error?.message || "No redirect URL received"
            }`
          )
        );
      }
      resolve(redirectUrl);
    });
  });
}

function extractAuthCode(redirectUrl: string): string {
  const urlParams = new URLSearchParams(new URL(redirectUrl).search);
  const authCode = urlParams.get("code");

  if (!authCode) {
    throw new Error("Authorization code not found in redirect URL");
  }

  return authCode;
}

async function exchangeCodeForToken(authCode: string): Promise<string> {
  try {
    const { key } = await authenticateGoogleUser(authCode);
    return key;
  } catch (error) {
    throw new Error(
      `Token exchange failed: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

export async function onLaunchWebFlow() {
  try {
    const googleRedirectUri = import.meta.env.WXT_PUBLIC_GOOGLE_REDIRECT_URL;
    const googleClientId = import.meta.env.WXT_PUBLIC_GOOGLE_CLIENT_ID;
    const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?redirect_uri=${googleRedirectUri}&prompt=consent&response_type=code&client_id=${googleClientId}&scope=openid%20email%20profile&access_type=offline`;

    const redirectUrl = await launchAuthFlow(authUrl);
    const authCode = extractAuthCode(redirectUrl);
    return await exchangeCodeForToken(authCode);
  } catch (error) {
    throw new Error(
      `Sign-in failed: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}
