import { RemoteGenericCardsApi } from '@/api/genericCard';
import * as axiosLib from 'axios';
import { storage } from 'wxt/storage';

interface SuccessResponse {
  success: true;
  hasVocabularyCards: boolean;
}

interface ErrorResponse {
  error: true;
  type: 'auth' | 'api' | 'network' | 'unknown';
  message?: string;
  status?: number;
  detail?: unknown;
}

type VocabularyCardsCheckResponse = SuccessResponse | ErrorResponse;

/**
 * Handles Axios and other errors, returning a structured ErrorResponse.
 * @param error The error object caught.
 * @returns A structured ErrorResponse.
 */
function handleApiError(error: any): ErrorResponse {
  if (axiosLib.isAxiosError(error)) {
    if (error.response) {
      if (error.response.status === 401 || error.response.status === 403) {
        return { error: true, type: 'auth', status: error.response.status };
      }
      return { error: true, type: 'api', status: error.response.status, detail: error.response.data };
    } else {
      return { error: true, type: 'network', message: error.message };
    }
  }
  return { error: true, type: 'unknown', message: String(error) };
}

/**
 * Gets the current user ID from the auth store
 * @returns The user ID or undefined if not available
 */
async function getCurrentUserId(): Promise<number | undefined> {
  const authStore = await storage.getItem('local:auth') as any;
  if (!authStore) return undefined;
  
  // First check authenticated user
  if (authStore.user && authStore.user.id) {
    return authStore.user.id;
  }
  
  // Fall back to anonymous user if authenticated user not found
  if (authStore.annonymousUser && authStore.annonymousUser.id) {
    return authStore.annonymousUser.id;
  }
  
  return undefined;
}

/**
 * Handles the 'writingAssistantCheckVocabularyCards' message.
 * Checks if the user has any vocabulary cards.
 * @returns A promise resolving to the check result (boolean or error object).
 */
export async function handleVocabularyCardsCheck(): Promise<VocabularyCardsCheckResponse> {
  const api = new RemoteGenericCardsApi();
  
  try {
    // Get current user ID from auth store
    const userId = await getCurrentUserId();
    
    // Call API with owner filter if we have a user ID
    const params: { owner?: number } = {};
    if (userId !== undefined) {
      params.owner = userId;
    }
    console.log(params);
    
    const cardsListResult = await api.list(params);
    
    // Check if totalPages > 0 to determine if user has any cards
    return { 
      success: true, 
      hasVocabularyCards: cardsListResult.totalPages > 0 
    };
  } catch (error: any) {
    return handleApiError(error);
  }
} 