import type { LLMModel, LLMProvider } from '@/utils/llm';

export async function loadLlmConfig(): Promise<{ models: LLMModel[]; providers: LLMProvider[] }> {
  const persistedStore = await storage.getItem('local:llm') as any;
  if (!persistedStore) throw new Error('No LLM config found in storage');
  const models: LLMModel[] = Array.isArray(persistedStore.models)
    ? persistedStore.models
    : Object.values(persistedStore.models || {});
  const providers: LLMProvider[] = Array.isArray(persistedStore.providers)
    ? persistedStore.providers
    : Object.values(persistedStore.providers || {});
  return { models, providers };
}

export function getModelByName(models: LLMModel[], name: string): LLMModel | undefined {
  return models.find((model) => model.name === name);
}

export function getModelById(models: LLMModel[], id: number): LLMModel | undefined {
  return models.find((model) => model.id === id);
}

export function getApiKey(providers: LLMProvider[], providerName: string): string | undefined {
  return providers.find((p) => p.name === providerName)?.apiKey;
} 