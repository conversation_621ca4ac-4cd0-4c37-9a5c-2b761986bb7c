import { RemoteWriteHistoryApi } from '@/api/writeHistory';
import * as axiosLib from 'axios';
import type { SaveHistoryPayload } from '@/components/write/api/index';
import type { RevisionData } from '@/components/write/types';

export async function handleSaveHistory(messageData: SaveHistoryPayload) {
  const api = new RemoteWriteHistoryApi();
  try {
    // Expect messageData to contain the full payload for history creation
    const { userText, selectedTone, revisions, ...rest } = messageData;
    // Ensure correct types for triggerType and useMyVocabulary
    const triggerType: 'Revise' | 'Refresh' | 'GrammarCheck' =
      rest.triggerType === 'Refresh' || rest.triggerType === 'GrammarCheck' ? rest.triggerType : 'Revise';
    const useMyVocabulary: boolean = typeof rest.useMyVocabulary === 'boolean' ? rest.useMyVocabulary : false;

    // Validate revisions array
    if (!Array.isArray(revisions) || revisions.length === 0) {
      return { error: true, type: 'validation', message: 'No revisions provided' };
    }

    // Build the payload for the API
    const historyPayload = {
      originalText: userText,
      selectedTone,
      triggerType,
      useMyVocabulary,
      revisions,
    };
    
    // Process cardIds - ensure it's an array of strings when useMyVocabulary is true
    // If useMyVocabulary is false, still pass cardIds if somehow available
    let cardIds: string[] | undefined;
    
    if (Array.isArray(rest.cardIds)) {
      // Filter to ensure all ids are strings
      cardIds = rest.cardIds.filter((id: any) => typeof id === 'string') as string[];
      
      // If we expect vocabulary but have no card IDs, use an empty array instead of undefined
      if (cardIds.length === 0 && useMyVocabulary) {
        cardIds = [];
      } else if (cardIds.length === 0) {
        // If not using vocabulary and no valid IDs, set to undefined
        cardIds = undefined;
      }
    } else if (useMyVocabulary) {
      // If using vocabulary but no cardIds provided, use empty array
      cardIds = [];
    }
    
    // Call create method, getting the ID of the created history entry
    const createdHistory = await api.create(historyPayload, cardIds);
    
    // Return success with history ID for potential future updates
    return { success: true, historyId: createdHistory.id };
  } catch (error) {
    if (axiosLib.isAxiosError(error)) {
      if (error.response) {
        if (error.response.status === 401 || error.response.status === 403) {
          return { error: true, type: 'auth', status: error.response.status };
        }
        if (error.response.status === 400) {
          return { error: true, type: 'validation', detail: error.response.data };
        }
        return { error: true, type: 'api', status: error.response.status, detail: error.response.data };
      } else {
        return { error: true, type: 'network', message: error.message };
      }
    }
    return { error: true, type: 'unknown', message: String(error) };
  }
}

interface UpdateHistoryMessageData {
  historyId: number;
  payload: {
    revisions: RevisionData[];
  };
}

export async function handleUpdateHistory(messageData: UpdateHistoryMessageData) {
  const api = new RemoteWriteHistoryApi();
  try {
    const { historyId, payload } = messageData;
    
    // Validate historyId
    if (!historyId || typeof historyId !== 'number' || historyId <= 0) {
      return { error: true, type: 'validation', message: 'Invalid history ID' };
    }
    
    // Validate revisions array
    if (!payload.revisions || !Array.isArray(payload.revisions) || payload.revisions.length === 0) {
      return { error: true, type: 'validation', message: 'No revisions provided in update payload' };
    }
    
    // Call the API to update the history entry
    await api.update(historyId, { revisions: payload.revisions });
    
    return { success: true };
  } catch (error) {
    if (axiosLib.isAxiosError(error)) {
      if (error.response) {
        if (error.response.status === 401 || error.response.status === 403) {
          return { error: true, type: 'auth', status: error.response.status };
        }
        if (error.response.status === 404) {
          return { error: true, type: 'not_found', message: 'History entry not found' };
        }
        if (error.response.status === 400) {
          return { error: true, type: 'validation', detail: error.response.data };
        }
        return { error: true, type: 'api', status: error.response.status, detail: error.response.data };
      } else {
        return { error: true, type: 'network', message: error.message };
      }
    }
    return { error: true, type: 'unknown', message: String(error) };
  }
}