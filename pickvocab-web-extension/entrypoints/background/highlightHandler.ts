import { loadLlmConfig, getModelByName, getApiKey } from './llmConfig';
import { selectActiveModel, selectPickvocabModel } from './modelSelection';
import { createChatSource, createPickvocabChatSource } from './chatSourceFactory';
import type { ChatSource } from 'pickvocab-dictionary';
import { extractFromCodeBlock } from '@/components/write/utils/revisionUtils';
import YAML from 'yaml';
import type { HighlightedRevisionItem, LlmErrorResult } from '@/components/write/types';

interface HighlightVocabularyPayload {
  prompt: string;
}

/**
 * Main handler function for vocabulary highlighting requests
 * @param messageData Object containing the prompt for highlighting
 * @returns Array of highlighted revision items or error object
 */
export async function handleHighlightVocabulary(
  messageData: HighlightVocabularyPayload
): Promise<HighlightedRevisionItem[] | LlmErrorResult> {
  try {
    const { prompt } = messageData;
    if (!prompt) {
      return errorResult('invalid_prompt', 'Prompt is empty or invalid');
    }

    // Log the received prompt for debugging
    console.log('Received highlighting prompt:', prompt);

    // 1. Load LLM config
    const { models, providers } = await loadLlmConfig();

    let modelSource: ChatSource | null = null;

    // 2. Try to select an active user model
    const selectedModel = selectActiveModel(models);
    if (selectedModel) {
      console.log('Using active user model for highlighting:', selectedModel.name);
      const apiKey = getApiKey(providers, selectedModel.provider);
      if (!apiKey) {
        return errorResult('api_key', `Missing API key for provider: ${selectedModel.provider}`);
      }
      modelSource = createChatSource(selectedModel, apiKey);
    } else {
      // 3. Fallback to Pickvocab model
      console.log('No active user model found, falling back to Pickvocab model');
      const pickvocabModelName = import.meta.env.WXT_PICKVOCAB_MODEL;
      const pickvocabModel = selectPickvocabModel(models, pickvocabModelName);
      if (!pickvocabModel) {
        return errorResult('llm_config', 'No Pickvocab model found');
      }
      modelSource = createPickvocabChatSource((name) => getModelByName(models, name), pickvocabModel);
    }

    if (!modelSource) {
      return errorResult('llm_source', 'Failed to instantiate chat source');
    }

    // 4. Call LLM service and process the result
    return await callLlmAndLog(modelSource, prompt);
  } catch (err: unknown) {
    return errorResult('unexpected', String(err));
  }
}

/**
 * Call the LLM service and process the response
 * @param modelSource Instantiated chat source
 * @param prompt Highlighting prompt
 * @returns Array of highlighted revisions or error
 */
async function callLlmAndLog(
  modelSource: ChatSource, 
  prompt: string
): Promise<HighlightedRevisionItem[] | LlmErrorResult> {
  try {
    console.log('Sending highlighting prompt to LLM');
    const rawResponse = await modelSource.sendMessage(prompt);
    console.log('Received raw LLM response for highlighting');
    
    // Process the response
    return extractAndParseHighlightedRevisions(rawResponse);
  } catch (err: unknown) {
    const message = err instanceof Error ? err.message : String(err);
    return errorResult('llm_call', `LLM call failed: ${message}`);
  }
}

/**
 * Extract and parse the highlighting results from LLM response
 * @param rawResponse Raw response from LLM
 * @returns Array of highlighted revisions or error
 */
function extractAndParseHighlightedRevisions(
  rawResponse: unknown
): HighlightedRevisionItem[] | LlmErrorResult {
  // Extract message content from response
  const rawMessage = getRawMessageFromLlmResponse(rawResponse);
  if (typeof rawMessage !== 'string') return rawMessage; // error result
  
  // Extract YAML from code block
  const yamlString = extractFromCodeBlock(rawMessage);
  console.log('Extracted YAML string:', yamlString.substring(0, 200) + '...');
  
  // Parse YAML
  const parsedYaml = parseYaml(yamlString);
  if (parsedYaml && typeof parsedYaml === 'object' && 'error' in parsedYaml) {
    return parsedYaml as LlmErrorResult;
  }
  
  // Validate and normalize
  return validateAndNormalizeHighlightedRevisions(parsedYaml);
}

/**
 * Utility: Extract message string from LLM response
 */
function getRawMessageFromLlmResponse(rawResponse: unknown): string | LlmErrorResult {
  if (typeof rawResponse === 'string') {
    return rawResponse;
  }
  if (
    rawResponse &&
    typeof rawResponse === 'object' &&
    'message' in rawResponse &&
    typeof (rawResponse as { message: unknown }).message === 'string'
  ) {
    return (rawResponse as { message: string }).message;
  }
  const errorObj: LlmErrorResult = {
    error: true,
    type: 'invalid_llm_response',
    message: 'LLM response is not a string or object with a message property',
  };
  return errorObj;
}

/**
 * Utility: Parse YAML string
 */
function parseYaml(yamlString: string): unknown | LlmErrorResult {
  try {
    const parsed = YAML.parse(yamlString);
    return parsed;
  } catch (yamlErr) {
    return { error: true, type: 'yaml_parse', message: String(yamlErr) };
  }
}

/**
 * Validate and normalize highlighted revisions data
 * @param parsedYaml Parsed YAML content
 * @returns Normalized highlighted revisions array or error
 */
function validateAndNormalizeHighlightedRevisions(parsedYaml: unknown): HighlightedRevisionItem[] | LlmErrorResult {
  // Check for expected structure
  if (
    !parsedYaml ||
    typeof parsedYaml !== 'object' ||
    !('highlighted_revisions' in parsedYaml) ||
    !Array.isArray((parsedYaml as { highlighted_revisions: unknown }).highlighted_revisions)
  ) {
    console.error('Invalid highlighting response format:', parsedYaml);
    return {
      error: true,
      type: 'invalid_format',
      message: 'LLM response does not contain a valid highlighted_revisions array',
    };
  }

  const highlightedRevisionsRaw: unknown[] = (parsedYaml as { highlighted_revisions: unknown[] }).highlighted_revisions;
  
  // Normalize and validate data
  const normalized: HighlightedRevisionItem[] = highlightedRevisionsRaw
    .filter(
      (item): item is Record<string, unknown> =>
        typeof item === 'object' &&
        item !== null &&
        typeof (item as Record<string, unknown>).index === 'number' &&
        typeof (item as Record<string, unknown>).highlightedText === 'string'
    )
    .map((item) => ({
      index: (item as Record<string, unknown>).index as number,
      highlightedText: (item as Record<string, unknown>).highlightedText as string
    }));

  // Check if we have any valid items
  if (normalized.length === 0) {
    console.error('No valid highlighted revisions found in parsed YAML:', highlightedRevisionsRaw);
    return {
      error: true,
      type: 'no_valid_highlights',
      message: 'No valid highlighted revisions found in LLM response',
    };
  }

  console.log(`Successfully parsed ${normalized.length} highlighted revisions`);
  return normalized;
}

/**
 * Create a standardized error result
 */
function errorResult(type: string, message: string): LlmErrorResult {
  console.error(`Highlighting error (${type}):`, message);
  return { error: true, type, message };
} 