import type { LLMModel } from '@/utils/llm';

export function selectActiveModel(models: LLMModel[]): LLMModel | undefined {
  // User models are those not provided by Pickvocab
  const userModels = models.filter((model) => model.provider !== 'Pickvocab');
  return userModels.find((model) => model.isActive);
}

export function selectPickvocabModel(models: LLMModel[], pickvocabModelName: string): LLMModel | undefined {
  return models.find((model) => model.name === pickvocabModelName);
} 