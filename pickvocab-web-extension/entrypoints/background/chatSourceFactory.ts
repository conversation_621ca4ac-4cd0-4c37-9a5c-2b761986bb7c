import type { LLMModel } from '@/utils/llm';
import {
  AnthropicChat,
  ArliAIChat,
  CerebrasChat,
  ChutesChat,
  GeminiChat,
  GroqChat,
  MistralChat,
  OpenAIChat,
  OpenRouterChat,
  SambaNovaChat,
} from 'pickvocab-dictionary';
import { CodestralChatClient } from '@/entrypoints/popup/stores/llm/codestralChatClient';
import { createPickvocabChat } from '@/entrypoints/popup/stores/llm/createPickvocabModel';

export function createChatSource(model: LLMModel, apiKey: string): any {
  switch (model.provider) {
    case 'OpenAI':
      return new OpenAIChat({
        modelName: model.name,
        modelId: model.id,
        apiKey,
      });
    case 'Anthropic':
      return new AnthropicChat({
        modelName: model.name,
        modelId: model.id,
        apiKey,
      });
    case 'Gemini':
      return new GeminiChat({
        modelName: model.name,
        modelId: model.id,
        apiKey,
      });
    case 'Groq':
      return new GroqChat({
        modelName: model.name,
        modelId: model.id,
        apiKey,
      });
    case 'Cerebras':
      return new CerebrasChat({ 
        modelName: model.name,
        modelId: model.id,
        apiKey,
        isThinkingModel: model.isThinking
      });
    case 'OpenRouter':
      return new OpenRouterChat({
        modelName: model.name,
        modelId: model.id,
        apiKey,
      });
    case 'ArliAI':
      return new ArliAIChat({
        modelName: model.name,
        modelId: model.id,
        apiKey,
      });
    case 'SambaNova':
      return new SambaNovaChat({
        modelName: model.name,
        modelId: model.id,
        apiKey,
      });
    case 'Mistral':
      return new MistralChat({
        modelName: model.name,
        modelId: model.id,
        apiKey,
      });
    case 'Codestral':
      return new CodestralChatClient({
        modelName: model.name,
        modelId: model.id,
        apiKey,
      });
    case 'Chutes':
      return new ChutesChat({
        modelName: model.name,
        modelId: model.id,
        apiKey,
        isThinkingModel: model.isThinking
      });
    default:
      throw new Error(`Unknown provider: ${model.provider}`);
  }
}

export function createPickvocabChatSource(getModelByName: (name: string) => LLMModel | undefined, pickvocabModel: LLMModel): any {
  return createPickvocabChat(getModelByName, pickvocabModel.name, pickvocabModel.id);
} 