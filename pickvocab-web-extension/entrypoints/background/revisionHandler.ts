import { loadLlmConfig, getModelByName, getApiKey } from './llmConfig';
import { selectActiveModel, selectPickvocabModel } from './modelSelection';
import { createChatSource, createPickvocabChatSource } from './chatSourceFactory';
import type { ChatResponse, ChatSource } from 'pickvocab-dictionary';
import { extractFromCodeBlock } from '@/components/write/utils/revisionUtils';
import YAML from 'yaml';
import type { RevisionData } from '@/components/write/types';

// Error result type for LLM operations
export type LlmErrorResult = { error: true; type: string; message: string };

interface GenerateRevisionPayload {
  prompt: string;
  indexToIdMap?: Record<string, string>;
}

export async function handleGenerateRevision(messageData: GenerateRevisionPayload): Promise<RevisionData[] | LlmErrorResult> {
  try {
    const { prompt, indexToIdMap } = messageData;

    // 1. Load config
    const { models, providers } = await loadLlmConfig();

    let modelSource: ChatSource | null = null;

    // 2. Try to select an active user model
    const selectedModel = selectActiveModel(models);
    if (selectedModel) {
      const apiKey = getApiKey(providers, selectedModel.provider);
      if (!apiKey) return errorResult('api_key', `Missing API key for provider: ${selectedModel.provider}`);
      modelSource = createChatSource(selectedModel, apiKey);
    } else {
      // 3. Fallback to Pickvocab model
      const pickvocabModelName = import.meta.env.WXT_PICKVOCAB_MODEL;
      const pickvocabModel = selectPickvocabModel(models, pickvocabModelName);
      if (!pickvocabModel) return errorResult('llm_config', 'No Pickvocab model found');
      modelSource = createPickvocabChatSource((name) => getModelByName(models, name), pickvocabModel);
    }

    if (!modelSource) {
      return errorResult('llm_source', 'Failed to instantiate chat source');
    }
    
    // Call LLM and process the result
    const result = await callLlmAndLog(modelSource, prompt);
    
    // If there was an error or no indexToIdMap, return the result as is
    if ('error' in result || !indexToIdMap) {
      return result;
    }
    
    // Process vocabulary indices in each revision
    return processLLMRevisions(result, indexToIdMap);
  } catch (err: unknown) {
    return errorResult('unexpected', String(err));
  }
}

/**
 * Maps LLM vocabulary indices to real card IDs
 * @param revisions The array of RevisionData from the LLM
 * @param indexToIdMap Map of vocabulary indices to real card IDs
 * @returns Processed revisions with real_card_ids field populated
 */
function processLLMRevisions(
  revisions: RevisionData[],
  indexToIdMap: Record<string, string>
): RevisionData[] {
  return revisions.map(revision => {
    const processedRevision = { ...revision };
    
    if (Array.isArray(revision.user_vocabularies_used) && revision.user_vocabularies_used.length > 0) {
      // Convert the indices from the LLM response to actual card IDs
      const realCardIds = revision.user_vocabularies_used
        .map(vocabIndex => {
          const cardId = indexToIdMap[vocabIndex];
          return cardId || null;
        })
        .filter((id): id is string => id !== null);
      
      // Add the real card IDs to the revision data
      processedRevision.real_card_ids = realCardIds;
    }
    
    return processedRevision;
  });
}

function errorResult(type: string, message: string): LlmErrorResult {
  return { error: true, type, message };
}

// Utility: Extract message string from LLM response
function getRawMessageFromLlmResponse(rawResponse: unknown): string | LlmErrorResult {
  if (typeof rawResponse === 'string') {
    return rawResponse;
  }
  if (
    rawResponse &&
    typeof rawResponse === 'object' &&
    'message' in rawResponse &&
    typeof (rawResponse as { message: unknown }).message === 'string'
  ) {
    return (rawResponse as { message: string }).message;
  }
  const errorObj: LlmErrorResult = {
    error: true,
    type: 'invalid_llm_response',
    message: 'LLM response is not a string or object with a message property',
  };
  return errorObj;
}

// Utility: Parse YAML string
function parseYaml(yamlString: string): unknown | LlmErrorResult {
  try {
    const parsed = YAML.parse(yamlString);
    return parsed;
  } catch (yamlErr) {
    return { error: true, type: 'yaml_parse', message: String(yamlErr) };
  }
}

// Utility: Validate and normalize revisions
function validateAndNormalizeRevisions(parsedYaml: unknown): RevisionData[] | LlmErrorResult {
  if (
    !parsedYaml ||
    typeof parsedYaml !== 'object' ||
    !('revisions' in parsedYaml) ||
    !Array.isArray((parsedYaml as { revisions: unknown }).revisions)
  ) {
    const errorObj: LlmErrorResult = {
      error: true,
      type: 'invalid_format',
      message: 'LLM response does not contain a valid revisions array',
    };
    return errorObj;
  }
  const revisionsRaw: unknown[] = (parsedYaml as { revisions: unknown[] }).revisions;
  const normalized: RevisionData[] = revisionsRaw
    .filter(
      (rev): rev is Record<string, unknown> =>
        typeof rev === 'object' &&
        rev !== null &&
        typeof (rev as Record<string, unknown>).revision === 'string' &&
        typeof (rev as Record<string, unknown>).feedback === 'string'
    )
    .map((rev) => ({
      revision: (rev as Record<string, unknown>).revision as string,
      feedback: (rev as Record<string, unknown>).feedback as string,
      learning_focus: Array.isArray((rev as Record<string, unknown>).learning_focus)
        ? ((rev as Record<string, unknown>).learning_focus as string[])
        : [],
      user_vocabularies_used: Array.isArray((rev as Record<string, unknown>).user_vocabularies_used)
        ? ((rev as Record<string, unknown>).user_vocabularies_used as string[])
        : [],
      real_card_ids: Array.isArray((rev as Record<string, unknown>).real_card_ids)
        ? ((rev as Record<string, unknown>).real_card_ids as string[])
        : undefined,
    }));
  if (normalized.length === 0) {
    const errorObj: LlmErrorResult = {
      error: true,
      type: 'no_valid_revisions',
      message: 'No valid revisions found in LLM response',
    };
    return errorObj;
  }
  return normalized;
}

// Main YAML extraction/validation pipeline
function extractAndParseYamlFromLlmResponse(rawResponse: unknown): RevisionData[] | LlmErrorResult {
  const rawMessage = getRawMessageFromLlmResponse(rawResponse);
  if (typeof rawMessage !== 'string') return rawMessage; // error result
  const yamlString = extractFromCodeBlock(rawMessage);
  const parsedYaml = parseYaml(yamlString);
  if (parsedYaml && typeof parsedYaml === 'object' && 'error' in parsedYaml) return parsedYaml as LlmErrorResult;
  return validateAndNormalizeRevisions(parsedYaml);
}

async function callLlmAndLog(modelSource: ChatSource, prompt: string): Promise<RevisionData[] | LlmErrorResult> {
  try {
    const rawResponse = await modelSource.sendMessage(prompt);

    // --- YAML Extraction and Parsing ---
    return extractAndParseYamlFromLlmResponse(rawResponse);
  } catch (err: unknown) {
    const message = err instanceof Error ? err.message : String(err);
    return errorResult('llm_call', `LLM call failed: ${message}`);
  }
} 