<script lang="ts" setup>
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

import { Separator } from '@/components/ui/separator';

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
} from '@/components/ui/sidebar';

// @ts-ignore
import IconKey from '@tabler/icons-vue/dist/esm/icons/IconKey.mjs';
// @ts-ignore
import IconSettings from '@tabler/icons-vue/dist/esm/icons/IconSettings.mjs';
// @ts-ignore
import IconKeyboard from '@tabler/icons-vue/dist/esm/icons/IconKeyboard.mjs';
import pickvocabImg from '~/assets/pickvocab.png';
import APIKeysView from './APIKeysView.vue';
import PreferencesView from './PreferencesView.vue';
import KeyboardShortcutsView from './KeyboardShortcutsView.vue';

const data: {
  navMain: {
    title: string;
    url: string;
    icon?: any;
  }[];
} = {
  navMain: [
    {
      title: 'API Keys',
      url: '#',
      icon: IconKey,
    },
    {
      title: 'Preferences',
      url: '#',
      icon: IconSettings,
    },
    {
      title: 'Keyboard Shortcuts',
      url: '#',
      icon: IconKeyboard,
    },
  ],
};

const selectedItem = ref('API Keys');
</script>

<template>
  <SidebarProvider>
    <Sidebar>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
              <div class="py-4 px-2 flex items-center gap-2">
                <div class="bg-white p-1 rounded border border-gray-200">
                  <img :src="pickvocabImg" alt="pickvocab logo" class="w-5 h-5" />
                </div>
                <div class="font-semibold text-sm">Settings</div>
              </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent class="gap-0">
        <SidebarMenu v-for="item in data.navMain" :key="item.title" class="px-4">
          <SidebarMenuItem>
            <SidebarMenuButton
              :class="{ 'bg-gray-200': item.title === selectedItem }"
              class="hover:bg-gray-100 text-xs font-medium"
              @click="selectedItem = item.title"
              as-child
            >
              <a :href="item.url">
                <component :is="item.icon" v-if="item.icon" class="!w-4 !h-4" />
                {{ item.title }}
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>

      <SidebarRail />
    </Sidebar>

    <SidebarInset>
      <header class="flex h-16 shrink-0 items-center gap-2 border-b px-4">
        <SidebarTrigger class="-ml-1" />
        <Separator orientation="vertical" class="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbPage>{{  selectedItem }}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      
      <div class="h-full">
        <APIKeysView v-if="selectedItem === 'API Keys'" />
        <PreferencesView v-if="selectedItem === 'Preferences'" />
        <KeyboardShortcutsView v-if="selectedItem === 'Keyboard Shortcuts'" />
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>

<style scoped></style>
