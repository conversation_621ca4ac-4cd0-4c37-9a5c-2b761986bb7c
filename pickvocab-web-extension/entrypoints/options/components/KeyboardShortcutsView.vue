<script lang="ts" setup>
import { ref, computed } from 'vue';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

// Define shortcuts manually
const shortcuts = ref([
  {
    name: 'Open extension popup',
    shortcut: 'Ctrl+Shift+K',
    shortcutMac: 'Command+Shift+K',
  },
  {
    name: 'Lookup selected text on page',
    shortcut: 'Ctrl+Shift+H',
    shortcutMac: 'Command+Shift+H',
  }
  // Add more shortcuts as needed
]);

// Determine if user is on Mac using the same approach as in WordLookupView component
const isMac = computed(() => {
  // Check for macOS using userAgent string
  return /Mac|iPhone|iPad|iPod/i.test(navigator.userAgent);
});

// Format the shortcut based on platform
function getFormattedShortcut(shortcut: string, shortcutMac?: string): string {
  if (isMac.value && shortcutMac) return shortcutMac;
  return shortcut;
}
</script>

<template>
  <div class="min-h-screen bg-white">
    <div class="max-w-3xl mx-auto p-6">
      <section class="mb-8">
        <h2 class="text-xl font-semibold mb-4">Keyboard Shortcuts</h2>
        
        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
          <div class="space-y-6">
            <div v-if="shortcuts.length === 0" class="text-center py-4 text-muted-foreground">
              No keyboard shortcuts configured
            </div>
            
            <div v-else class="space-y-6">
              <div v-for="(shortcut, index) in shortcuts" :key="index" class="border-b pb-6 last:border-0 last:pb-0">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h3 class="text-base font-medium text-gray-900">{{ shortcut.name }}</h3>
                  </div>
                  <div class="flex gap-1">
                    <kbd v-for="(key, i) in getFormattedShortcut(shortcut.shortcut, shortcut.shortcutMac).split('+')" :key="i" 
                      class="px-2 py-1 text-xs text-gray-800 bg-gray-100 border border-gray-200 rounded shadow-sm">
                      {{ key }}
                    </kbd>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped>
kbd {
  display: inline-block;
  min-width: 1.75em;
  text-align: center;
}
</style> 