<script lang="ts" setup>
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Tabs,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';
import Models from './Models.vue';
import Providers from './Providers.vue';
</script>

<template>
  <div class="w-full h-full flex justify-center">
    <Tabs default-value="model" class="px-4 sm:px-0 sm:w-[540px] my-24">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="model">
          Model
        </TabsTrigger>
        <TabsTrigger value="provider">
          Provider
        </TabsTrigger>
      </TabsList>
      <TabsContent value="model">
        <Card>
          <CardHeader>
            <CardTitle>Model</CardTitle>
            <!-- <CardDescription>
              Make changes to your account here. Click save when you're done.
            </CardDescription> -->
          </CardHeader>
          <CardContent class="space-y-2">
            <Models />
          </CardContent>
          <!-- <CardFooter>
            <Button>Save changes</Button>
          </CardFooter> -->
        </Card>
      </TabsContent>
      <TabsContent value="provider">
        <Card>
          <CardHeader>
            <CardTitle>Provider</CardTitle>
            <!-- <CardDescription>
              Change your password here. After saving, you'll be logged out.
            </CardDescription> -->
          </CardHeader>
          <CardContent class="space-y-2">
            <Providers />
          </CardContent>
          <!-- <CardFooter>
            <Button>Save password</Button>
          </CardFooter> -->
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>

<style scoped></style>
