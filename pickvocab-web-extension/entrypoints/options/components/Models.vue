<script lang="ts" setup>
import { Switch } from '@/components/ui/switch';
import { useLLMStore } from '@/entrypoints/popup/stores/llm';

const emit = defineEmits(["close"]);
const llmStore = useLLMStore();
llmStore.hydrate();
</script>

<template>
  <div class="mt-2 flex flex-col justify-between flex-grow">
    <div class="grid gap-4 mb-4 grid-cols-2 max-h-[320px] overflow-y-auto pl-2 pr-4">
      <div class="col-span-2 mb-4" v-for="model in llmStore.userModels.filter((model) => !model.isHidden)">
        <div class="flex items-center">
          <div class="flex items-center">
            <img :src="llmStore.providerMap[model.provider].logo" :alt="model.provider" class="w-5 h-5" />
            <div>
              <span class="ml-2 text-base font-medium text-gray-600 dark:text-white">{{ model.label }}</span>
              <span v-if="model.isFree"
                class="ml-2 text-xs text-gray-500 bg-gray-50 py-1 px-2 border rounded">Free</span>
            </div>
            <svg v-if="model.isActive &&
              llmStore.getErrorFromValidation(model.name)
            " class="ml-2 flex-shrink-0 w-5 h-5 text-red-800" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
              width="24" height="24" fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 13V8m0 8h.01M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
          </div>
          <div class="ml-auto">
            <Switch v-model:checked="model.isActive" class="data-[state=checked]:bg-blue-600 focus-visible:ring-blue-600" />
            <!-- <FwbToggle v-model="model.isActive"></FwbToggle> -->
          </div>
        </div>
        <p v-if="model.isActive &&
          llmStore.getErrorFromValidation(model.name)
        " class="w-full p-4 mt-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400">
          {{ llmStore.getErrorFromValidation(model.name) }}
        </p>
      </div>
    </div>

    <div v-if="llmStore.getErrorFromValidation('global')" class="col-span-2 flex mt-4">
      <div class="w-full p-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
        {{ llmStore.getErrorFromValidation("global") }}
      </div>
    </div>

    <div v-if="!llmStore.activeUserModel" class="col-span-2 flex mt-2">
      <div class="p-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
        For top performance among free models, we recommend <span class="font-bold">Gemini 2.0 Flash</span>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
