<script lang="ts" setup>
import { Switch } from '@/components/ui/switch';
import { useAppStore } from '@/entrypoints/popup/stores/app';
import { ref } from 'vue';

const appStore = useAppStore();
appStore.hydrate();

const showReloadMessage = ref(false);

const handleSettingChange = () => {
  showReloadMessage.value = true;
};
</script>

<template>
  <div class="min-h-screen bg-white">
    <div class="max-w-3xl mx-auto p-6">
      <section class="mb-8">
        <h2 class="text-xl font-semibold mb-4">General</h2>
        
        <!-- Reload Message -->
        <div v-if="showReloadMessage" class="mb-4 p-4 bg-blue-50 text-blue-700 rounded-lg">
          Please reload your tabs or restart your browser for the changes to take effect
        </div>

        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
          <div class="space-y-6">
            <!-- Floating Lookup Button Setting -->
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="text-base font-medium text-gray-900">Show floating lookup button</h3>
                <p class="text-sm text-gray-500 mt-1">Display a lookup button when text is selected on a webpage</p>
              </div>
              <Switch 
                v-model:checked="appStore.enableFloatingLookupButton" 
                class="data-[state=checked]:bg-blue-600 focus-visible:ring-blue-600"
                @update:checked="handleSettingChange"
              />
            </div>

            <!-- Add more settings here with the same pattern -->
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped>
</style> 