import { onMessage } from "webext-bridge/content-script";
import "./content/style.css";
// @ts-ignore
import { getSurroundingPassage, createFloatingPopup, createFloatingLookupButton } from './content/utils.ts'; // https://github.com/wxt-dev/wxt/issues/357
import { storage } from 'wxt/storage';
import { createMessagingService, setGlobalMessagingService } from '@/utils/messaging';
import { useContentStore } from './content/store';
import { useWritingStore } from '@/components/write/stores/writingStore';

// Initialize the messaging service for content script
setGlobalMessagingService(createMessagingService('content'));

export default defineContentScript({
  matches: ["http://*/*", "https://*/*"], // TODO: file://
  cssInjectionMode: "ui",
  async main(ctx) {
    // Check if floating lookup button is enabled
    const appSettings = await storage.getItem('local:app') as any;
    const enableFloatingLookupButton = appSettings?.enableFloatingLookupButton ?? true;

    if (enableFloatingLookupButton) {
      // Initialize the floating lookup button
      createFloatingLookupButton(ctx, async (selectedText, passage, offset, fontSize) => {
        try {
          // Delay the creation of the popup to avoid conflicts with event propagation
          setTimeout(async () => {
            await createFloatingPopup(ctx, selectedText, passage, offset, fontSize, 'menu');
          }, 50);
        } catch (error) {
          console.error("Error creating floating popup:", error);
        }
      });
    }

    // Note: We don't have a way to clean up the button when the content script is unloaded
    // but the browser will clean up the DOM elements when the page is navigated away from

    onMessage("getSelectionDetails", async (message) => {
      const selection = window.getSelection();
      if (!selection) {
        return undefined;
      }
      const selectedText = selection.toString();
      const { passage, offset, fontSize } = getSurroundingPassage(selection);
      return { selectedText, passage, offset, fontSize };
    });

    onMessage("showPopupDefinition", async (message) => {
      const { word, context, offset, fontSize } = message.data as any;
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        try {
          await createFloatingPopup(ctx, word, context, offset, fontSize, 'lookup');
        } catch (error) {
          console.error("Error creating floating popup from message:", error);
        }
      }
    });
    
    // Add keyboard shortcut listener (Ctrl/Cmd+Shift+H)
    document.addEventListener('keydown', async (e) => {
      // Check for Ctrl/Cmd+Shift+H
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'h') {
        e.preventDefault(); // Prevent default browser behavior

        const selection = window.getSelection();
        if (selection && selection.toString().trim() !== '') {
          const selectedText = selection.toString();
          const { passage, offset, fontSize } = getSurroundingPassage(selection);

          // --- Hide the floating button if it exists --- 
          const floatingButton = document.getElementById('wxt-floating-lookup-button');
          if (floatingButton) {
            floatingButton.style.display = 'none';
          }
          // ---------------------------------------------

          try {
            // Pass undefined for onCloseCallback as shortcut doesn't need to manage button state
            await createFloatingPopup(ctx, selectedText, passage, offset, fontSize, 'lookup', undefined);
          } catch (error) {
            console.error("Error creating floating popup from keyboard shortcut:", error);
          }
        }
      }
    });

    // Using webext-bridge for context menu actions
    onMessage('TRIGGER_FIX_WRITING_POPUP', async (message) => {
      // No data expected in the message, just the trigger
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0 && selection.toString().trim()) {
        const selectedText = selection.toString();
        const { passage, offset, fontSize, targetElement } = getSurroundingPassage(selection);
        await createFloatingPopup(
          ctx,
          selectedText,
          passage,
          offset,
          fontSize,
          'fix writing',
          undefined,
          targetElement instanceof HTMLElement ? targetElement : undefined
        );
      }
    });

    onMessage('LOOKUP_WORD', async (message) => {
      const { word } = message.data as { word: string };

      let passage = word;
      let offset = 0;
      let fontSize: number | undefined;
      let targetElement: HTMLElement | undefined;

      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0 && selection.toString().trim() === word) {
        try {
          const passageInfo = getSurroundingPassage(selection);
          passage = passageInfo.passage || word;
          offset = passageInfo.offset;
          fontSize = passageInfo.fontSize;
          targetElement = passageInfo.targetElement instanceof HTMLElement ? passageInfo.targetElement : undefined;
        } catch (e) {
          console.warn("[PickVocab] Error getting surrounding passage for context menu lookup:", e);
        }
      }

      await createFloatingPopup(
        ctx,
        word,
        passage,
        offset,
        fontSize,
        'lookup',
        undefined,
        targetElement
      );
    });
  },
});
