import { createApp } from 'vue';
import { createPinia } from 'pinia';
import './style.css';
import router from './router';
import App from './App.vue';
import { createMessagingService, setGlobalMessagingService } from '@/utils/messaging';

// Initialize the messaging service for popup
setGlobalMessagingService(createMessagingService('popup'));

createApp(App)
  .use(router)
  .use(createPinia())
  .mount('#app');
