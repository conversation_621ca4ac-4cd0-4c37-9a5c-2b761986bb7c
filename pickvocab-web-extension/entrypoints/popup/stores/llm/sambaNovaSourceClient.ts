import type {
  BaseWordEntry,
  BaseWordInContextEntry,
  DefinitionForLanguagePromptResult,
  DictionarySource,
  ExamplePromptResult,
  SynonymPromptResult
} from "pickvocab-dictionary";
import axios from "axios";

const CLIENT_API_URL = import.meta.env.WXT_CLIENT_API_URL;

export class SambaNovaSourceClient implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 2000;

  constructor(config: { modelId: number, modelName: string, maxToken?: number, apiKey?: string }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const response = await axios.post(`${CLIENT_API_URL}/api/sambaNova/`, {
      endpoint: 'listAllMeanings',
      apiKey: this.apiKey,
      modelId: this.modelId,
      modelName: this.modelName,
      maxToken: this.maxTokens,
      args: [word]
    });
    return response.data as BaseWordEntry;
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const response = await axios.post(`${CLIENT_API_URL}/api/sambaNova/`, {
      endpoint: 'listAllMeaningsForLanguage',
      apiKey: this.apiKey,
      modelId: this.modelId,
      modelName: this.modelName,
      maxToken: this.maxTokens,
      args: [input, language]
    });
    return response.data as DefinitionForLanguagePromptResult;
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const response = await axios.post(`${CLIENT_API_URL}/api/sambaNova/`, {
      endpoint: 'getMoreExamples',
      apiKey: this.apiKey,
      modelId: this.modelId,
      modelName: this.modelName,
      maxToken: this.maxTokens,
      args: [input]
    });
    return response.data as ExamplePromptResult;
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const response = await axios.post(`${CLIENT_API_URL}/api/sambaNova/`, {
      endpoint: 'getMoreSynonymsForDefinition',
      apiKey: this.apiKey,
      modelId: this.modelId,
      modelName: this.modelName,
      maxToken: this.maxTokens,
      args: [input]
    });
    return response.data as SynonymPromptResult;
  }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const response = await axios.post(`${CLIENT_API_URL}/api/sambaNova/`, {
      endpoint: 'getMeaningInContext',
      apiKey: this.apiKey,
      modelId: this.modelId,
      modelName: this.modelName,
      maxToken: this.maxTokens,
      args: [word, context, offset]
    });
    return response.data as BaseWordInContextEntry;
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const response = await axios.post(`${CLIENT_API_URL}/api/sambaNova/`, {
      endpoint: 'getMeaningInContextShort',
      apiKey: this.apiKey,
      modelId: this.modelId,
      modelName: this.modelName,
      maxToken: this.maxTokens,
      args: [word, context, offset]
    });
    return response.data as BaseWordInContextEntry;
  }

  async getMeaningInContextShortForLanguage(
    word: string, 
    context: string, 
    offset: number, 
    language: string
  ): Promise<string> {
    const response = await axios.post(`${CLIENT_API_URL}/api/sambaNova/`, {
      endpoint: 'getMeaningInContextShortForLanguage',
      apiKey: this.apiKey,
      modelId: this.modelId,
      modelName: this.modelName,
      maxToken: this.maxTokens,
      args: [word, context, offset, language]
    });
    return response.data as string;
  }
}
