import { defineStore } from "pinia";
import { storage } from 'wxt/storage';

export const useAppStore = defineStore('app', () => {
  const isInitialized = ref(false);
  const enableFloatingLookupButton = ref(true);

  async function hydrate() {
    const persistedStore = await storage.getItem('local:app') as any;
    if (persistedStore) {
      if (persistedStore.enableFloatingLookupButton !== undefined) {
        enableFloatingLookupButton.value = persistedStore.enableFloatingLookupButton;
      }
    }
    await persist();
  }

  async function persist() {
    await storage.setItem('local:app', {
      enableFloatingLookupButton: enableFloatingLookupButton.value,
    });
  }

  watch(enableFloatingLookupButton, () => {
    persist();
  });

  return {
    isInitialized,
    enableFloatingLookupButton,
    hydrate,
  };
});
