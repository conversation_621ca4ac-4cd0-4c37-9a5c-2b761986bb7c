import { createRouter, createWebHashHistory } from 'vue-router';

import LookupView from '@/components/lookup/LookupView.vue';
import { useAuthStore } from './stores/auth';
import { useLLMStore } from './stores/llm';

const routes = [
  {
    path: '/',
    name: 'Lookup',
    component: LookupView,
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();
  authStore.hydrate();
  const llmStore = useLLMStore();
  llmStore.hydrate();

  next();
});

export default router;
