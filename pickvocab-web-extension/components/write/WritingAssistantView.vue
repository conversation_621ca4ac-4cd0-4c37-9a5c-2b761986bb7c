<script setup lang="ts">
import WritingInputSection from './components/WritingInputSection.vue';
import WritingOutputSection from './components/WritingOutputSection.vue';
import { useContentStore } from '@/entrypoints/content/store';
import { useWritingStore } from './stores/writingStore';
import { onMounted } from 'vue';
import { useWritingRevisionHandler } from './composables/useWritingRevisionHandler';
import { useAuthStore } from '@/entrypoints/popup/stores/auth';
import { useLLMStore } from '@/entrypoints/popup/stores/llm';
import Spinner from '@/components/Spinner.vue';

const contentStore = useContentStore();
const writingStore = useWritingStore();
const authStore = useAuthStore();
const llmStore = useLLMStore();
llmStore.setExtensionUI('content-script');
const revisionHandler = useWritingRevisionHandler();

function handleOpenManageTones() {
  writingStore.setWritingAssistantSubView('manage_tones');
}

function goBack() {
  contentStore.setSelectedAction('menu');
}

function handleNavigateRevision(direction: 'prev' | 'next') {
  if (direction === 'prev') revisionHandler.previousRevision();
  else if (direction === 'next') revisionHandler.nextRevision();
}

function handleRefreshRevision() {
  revisionHandler.initiateRevision();
}

async function handleCopyRevisedText() {
  try {
    await navigator.clipboard.writeText(revisionHandler.revisedText.value);
  } catch (e) {
    // TODO: Replace with a toast notification
  }
}

onMounted(async () => {
  Promise.allSettled([writingStore.hydrate(), llmStore.hydrate(), authStore.hydrate()]);
  writingStore.userText = contentStore.word;
  writingStore.setWritingAssistantSubView('main');
  writingStore.loadPredefinedTones();
  writingStore.fetchCustomTones();
});
</script>

<template>
  <div
    class="writing-assistant-view min-w-[600px] max-w-[600px] max-h-[min(600px,90vh)] overflow-x-hidden overflow-y-auto"
  >
    <div class="flex justify-between items-center py-4 px-6 border-b border-gray-200 relative z-10 shadow-[0_1px_2px_rgba(0,0,0,0.05)]">
      <h2 class="text-xl font-semibold text-gray-800">Writing Assistant</h2>
      <button
        @click="goBack"
        class="text-sm text-blue-600 hover:text-blue-800"
      >
        ← Back to options
      </button>
    </div>

    <div class="space-y-4">
      <WritingInputSection @openManageTones="handleOpenManageTones" :revisionHandler="revisionHandler" />

      <div v-if="revisionHandler.isLoading.value" class="flex justify-center items-center p-10">
        <Spinner :size="'8'" />
      </div>

      <div v-else-if="revisionHandler.revisionError.value" class="bg-white border border-gray-200 rounded-lg shadow-sm p-4 mb-4">
          <div class="flex items-center bg-red-50 border border-red-200 text-red-800 rounded-md px-3 py-2 text-sm">
              <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z"/></svg>
              <span class="font-medium">{{ revisionHandler.revisionError.value }}</span>
          </div>
           <button
              @click="handleRefreshRevision"
              class="mt-2 text-sm text-blue-600 hover:text-blue-800"
            >
              Try again
            </button>
      </div>

      <WritingOutputSection
        v-else-if="revisionHandler.allRevisionsResult.value.length > 0"
        :is-loading="revisionHandler.isLoading.value"
        :is-loading-vocabulary-cards="revisionHandler.isLoadingVocabularyCards.value"
        :revised-text="revisionHandler.revisedText.value"
        :llm-feedback-text="revisionHandler.llmFeedback.value"
        :learning-focus="revisionHandler.learningFocus.value"
        :current-revision-index="revisionHandler.currentRevisionIndex.value"
        :total-revisions="revisionHandler.totalRevisions.value"
        :vocabulary-was-used="revisionHandler.vocabularyWasUsedForLastRevision.value"
        :current-revision-data="revisionHandler.currentRevisionData.value"
        :card-cache-by-id="revisionHandler.cardCacheById.value"
        @navigateRevision="handleNavigateRevision"
        @refreshRevision="handleRefreshRevision"
        @copyRevisedText="handleCopyRevisedText"
      />
    </div>
  </div>
</template>

<style scoped>
/* Styles moved to Tailwind classes */
</style>