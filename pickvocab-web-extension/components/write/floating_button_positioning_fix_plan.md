# Plan: Fix Floating Button Positioning (Revised after Debugging)

## 1. Problem Description

The floating lookup button (`#wxt-floating-lookup-button`) initially did not appear in the correct position when text was selected across multiple distinct content blocks within the Notion editor (and potentially other rich-text editors). Later, fixes for this introduced a regression where selections within a single block were no longer centered correctly.

**Diagnosis & Debugging Steps:**

1.  Initial analysis showed that for multi-block selections, the `commonAncestorContainer` was a high-level element (e.g., `.notion-page-content`), but the `range.getBoundingClientRect()` seemed valid. The button appeared but was misplaced.
2.  Logging added to `positionButton` revealed that `getSurroundingPassage` was incorrectly identifying a `targetElement` (an `INPUT` element, likely unrelated) even for multi-block selections. This was because the fallback `querySelector('input')` search was being triggered on the high-level ancestor.
3.  A general heuristic (`containsMultipleBlockChildren`) was added to `getSurroundingPassage` to detect if the selection container likely spanned multiple blocks. If so, the input/textarea search was correctly bypassed.
4.  **Regression:** This fix caused the button for *partial* selections within a single block to be centered relative to the entire block element (derived from `startContainer`), not the specific `range`.
5.  **Final Root Cause:** The positioning logic needed to consistently use the precise coordinates of the selected `range` (`range.getBoundingClientRect()`) for calculating the button's center position, rather than relying on the bounding box of the container element.

## 2. Implemented Solution

Modified `getSurroundingPassage`, `positionButton`, and added a helper function `calculateAndCompletePositioning` in `pickvocab-web-extension/entrypoints/content/utils.ts`.

**Key Logic Changes:**

1.  **`getSurroundingPassage`:**
    *   Maintains the `containsMultipleBlockChildren` helper.
    *   If the determined container (`elementToQuery`) is likely a multi-block container (based on the helper), the function skips searching for `input` or `textarea` elements via `querySelector`, preventing the incorrect identification of unrelated inputs.
    *   It then proceeds with the standard logic to find a suitable passage container (`P`, `DIV`, `LI`, etc.).
2.  **`positionButton`:**
    *   Determines a potential container element (`positioningTargetElement`) based on the explicit `targetElement` (if input/textarea) or the selection's `startContainer` parent.
    *   Calls `requestAnimationFrame` which invokes the `calculateAndCompletePositioning` helper.
3.  **`calculateAndCompletePositioning` (Helper Function):**
    *   This function, called within `requestAnimationFrame`, is responsible for determining the final `DOMRect` used for positioning calculations.
    *   **Prioritizes `range.getBoundingClientRect()`:** It first attempts to get the bounding box directly from the selection `Range` (`currentRange.getBoundingClientRect()`).
    *   **Fallbacks:** If the `range` rect is invalid (null or zero size), it falls back to using the `pTargetElement` (if an input/textarea was specifically identified by `getSurroundingPassage`) or `pPosTargetElement` (the container derived from `startContainer`) rect as a last resort.
    *   Calls `completePositioning` with the chosen rectangle.
4.  **`completePositioning`:**
    *   Uses the `rect` passed from `calculateAndCompletePositioning` (which is now primarily the `range` rect) to calculate the final `buttonX` and `buttonY`, ensuring the button is centered over the actual selection.
5.  **Cleanup:** All temporary debugging `console.log` statements were removed.

## 3. Expected Outcome (Achieved)

The floating lookup button now consistently appears positioned correctly and centered above the user's text selection, regardless of whether the selection is partial within a single block or spans multiple content blocks in editors like Notion. 