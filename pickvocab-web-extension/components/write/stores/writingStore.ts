import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { writingApi } from '../api/index'
import type { PredefinedTone, CustomTone, SelectedToneIdentifier } from '../types'
import { storage } from 'wxt/storage'

interface PersistedWritingStore {
  selectedTone?: SelectedToneIdentifier | null;
  useVocabulary?: boolean;
  grammarCheckEnabled?: boolean;
}

export const useWritingStore = defineStore('writing', () => {
  // Placeholder for writing store
  // Will contain state and actions for the writing assistant feature

  const userText = ref('')
  // Task 15: Add useVocabulary state
  const useVocabulary = ref(false)
  // Task 27: Add grammarCheckEnabled state
  const grammarCheckEnabled = ref(false)
  // Add state for tracking vocabulary card availability
  const hasVocabularyCards = ref<boolean | null>(null)
  const isCheckingVocabularyCards = ref(false)

  const predefinedTones = ref<PredefinedTone[]>([
    {
      name: 'Professional',
      description: 'Formal language suitable for business and professional contexts.',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-800'
    },
    {
      name: 'Casual',
      description: 'Informal and relaxed language for everyday conversations.',
      bgColor: 'bg-teal-100',
      textColor: 'text-teal-800'
    },
    {
      name: 'Creative',
      description: 'Imaginative and creative language, great for poetry and storytelling.',
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-800'
    },
    {
      name: 'Academic',
      description: 'Scholarly language with precise terminology and formal structure.',
      bgColor: 'bg-indigo-100',
      textColor: 'text-indigo-800'
    },
    {
      name: 'Funny',
      description: 'Humorous and lighthearted style, great for jokes and witty remarks.',
      bgColor: 'bg-orange-100',
      textColor: 'text-orange-800'
    },
    {
      name: 'Technical Writing',
      description: 'Clear, concise language focused on technical accuracy and documentation.',
      bgColor: 'bg-green-100',
      textColor: 'text-green-800'
    },
    {
      name: 'Reddit-vibe',
      description: 'Conversational internet slang with references and casual humor typical of Reddit.',
      bgColor: 'bg-orange-400',
      textColor: 'text-orange-800'
    },
  ]);
  // Use robust identifier object for selection
  const defaultTone: SelectedToneIdentifier = { type: 'predefined', identifier: 'Professional' };
  const selectedTone = ref<SelectedToneIdentifier | null>(defaultTone);

  // Task 6: Add sub-view state and setter for writing assistant
  const writingAssistantSubView = ref<'main' | 'manage_tones'>('main');
  function setWritingAssistantSubView(view: 'main' | 'manage_tones') {
    writingAssistantSubView.value = view;
  }

  // Task 8: Add full state for writing assistant
  const customTones = ref<CustomTone[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  const allAvailableTones = computed(() => [
    ...predefinedTones.value.map(tone => ({ ...tone, type: 'predefined', identifier: tone.name })),
    ...customTones.value.map(tone => ({ ...tone, type: 'custom', identifier: tone.id }))
  ]);

  async function fetchCustomTones() {
    setIsLoading(true);
    setError(null);
    try {
      // Real API returns { success: true, tones: [...] }, not just an array
      const result = await writingApi.fetchCustomTones();
      if (result && !('error' in result)) { // Check if result is not an error
        setCustomTones(result.tones || []);
      } else {
        setError('fetchCustomTones: ' + (result?.message || 'Failed to load custom tones'));
      }
    } catch (err: any) {
      setError('fetchCustomTones: ' + (typeof err === 'string' ? err : (err?.message || JSON.stringify(err) || 'Failed to load custom tones')));
    } finally {
      setIsLoading(false);
    }
  }
  function loadPredefinedTones() {
    // For symmetry/future: could reload from config, for now does nothing
  }
  function selectTone(tone: SelectedToneIdentifier) {
    selectedTone.value = tone;
  }

  // Placeholder actions for future logic
  function setCustomTones(tones: CustomTone[]) {
    customTones.value = tones;
  }
  function setIsLoading(val: boolean) {
    isLoading.value = val;
  }
  function setError(err: string | null) {
    error.value = err;
  }

  // --- Task 9.5: Persistence logic ---
  const isHydrated = ref(false);

  async function hydrate() {
    if (isHydrated.value) return;
    const persistedStore = await storage.getItem<PersistedWritingStore>('local:writing');
    let loadedToneIsValid = false;

    if (persistedStore && persistedStore.selectedTone) {
      const loaded = persistedStore.selectedTone;
      if (loaded.type === 'predefined') {
        loadedToneIsValid = predefinedTones.value.some(t => t.name === loaded.identifier);
      } else if (loaded.type === 'custom') {
        // Need to wait for custom tones to be fetched to validate,
        // but for now, we'll assume the ID might be valid later.
        // A more robust check could happen after fetchCustomTones completes.
        // For simplicity here, we check if identifier is a number.
        loadedToneIsValid = typeof loaded.identifier === 'number';
      }

      if (loadedToneIsValid) {
        selectedTone.value = loaded;
      } else {
        selectedTone.value = defaultTone;
      }
    } else {
       selectedTone.value = defaultTone;
    }

    // Task 15: Load useVocabulary from persisted store
    if (persistedStore && typeof persistedStore.useVocabulary === 'boolean') {
      useVocabulary.value = persistedStore.useVocabulary;
    }

    // Task 27: Load grammarCheckEnabled from persisted store
    if (persistedStore && typeof persistedStore.grammarCheckEnabled === 'boolean') {
      grammarCheckEnabled.value = persistedStore.grammarCheckEnabled;
    }

    // Persist the potentially updated/defaulted state
    await persist();
    isHydrated.value = true;
  }

  async function persist() {
    await storage.setItem('local:writing', {
      selectedTone: selectedTone.value,
      useVocabulary: useVocabulary.value,
      grammarCheckEnabled: grammarCheckEnabled.value,
    });
  }

  watch(selectedTone, () => {
    if (!isHydrated.value) return;
    persist();
  });

  // Task 15: Add watcher for useVocabulary
  watch(useVocabulary, () => {
    if (!isHydrated.value) return;
    persist();
  });

  // Task 27: Add watcher for grammarCheckEnabled
  watch(grammarCheckEnabled, () => {
    if (!isHydrated.value) return;
    persist();
  });

  // --- Task 10: CRUD actions for custom tones (mocked) ---
  async function addCustomTone(newTone: { name: string; description: string }) {
    setIsLoading(true);
    setError(null);
    try {
      // Real API returns { success: true, tone: {...} }, not just the tone object
      // Add default bgColor and textColor as they are required by CreateCustomTonePayload
      const payload = { ...newTone, bgColor: 'bg-gray-200', textColor: 'text-gray-800' };
      const result = await writingApi.createCustomTone(payload);
      if (result && !('error' in result) && result.success && result.tone) { // Check if result is not an error
        customTones.value = [...customTones.value, result.tone];
      } else if (result && 'error' in result) {
        setError('addCustomTone: ' + (result.message || 'Failed to add custom tone'));
      } else {
        setError('addCustomTone: Unexpected API response');
      }
    } catch (err: any) {
      setError('addCustomTone: ' + (typeof err === 'string' ? err : (err?.message || JSON.stringify(err) || 'Failed to add custom tone')));
    } finally {
      setIsLoading(false);
    }
  }

  async function updateCustomTone(updatedTone: { id: number; name: string; description: string }) {
    setIsLoading(true);
    setError(null);
    try {
      const result = await writingApi.updateCustomTone(updatedTone);
      if (result) {
        customTones.value = customTones.value.map(tone =>
          tone.id === updatedTone.id ? { ...tone, ...updatedTone } : tone
        );
      }
    } catch (err: any) {
      setError('updateCustomTone: ' + (typeof err === 'string' ? err : (err?.message || JSON.stringify(err) || 'Failed to update custom tone')));
    } finally {
      setIsLoading(false);
    }
  }

  async function deleteCustomTone(id: number) {
    setIsLoading(true);
    setError(null);
    try {
      await writingApi.deleteCustomTone(id); // Pass id as number
      customTones.value = customTones.value.filter(tone => tone.id !== id);
    } catch (err: any) {
      setError('deleteCustomTone: ' + (typeof err === 'string' ? err : (err?.message || JSON.stringify(err) || 'Failed to delete custom tone')));
    } finally {
      setIsLoading(false);
    }
  }

  function setUserText(text: string) {
    userText.value = text;
  }

  // Function to check if user has vocabulary cards
  async function checkVocabularyCards() {
    if (isCheckingVocabularyCards.value) return hasVocabularyCards.value;
    
    try {
      isCheckingVocabularyCards.value = true;
      
      // Use the API method instead of directly calling RemoteGenericCardsApi
      const result = await writingApi.checkVocabularyCards();
      
      if (result && !('error' in result) && result.success) {
        // Update state based on API response
        hasVocabularyCards.value = result.hasVocabularyCards;
      } else {
        // Default to true on error to avoid showing the warning incorrectly
        console.error('Failed to check vocabulary cards:', result);
        hasVocabularyCards.value = true;
      }
      
      return hasVocabularyCards.value;
    } catch (error) {
      console.error('Failed to check vocabulary cards:', error);
      // Default to true on error to avoid showing the warning incorrectly
      hasVocabularyCards.value = true;
      return hasVocabularyCards.value;
    } finally {
      isCheckingVocabularyCards.value = false;
    }
  }

  return {
    userText,
    useVocabulary,
    grammarCheckEnabled,
    predefinedTones,
    customTones,
    allAvailableTones,
    selectedTone,
    selectTone,
    loadPredefinedTones,
    fetchCustomTones,
    writingAssistantSubView,
    setWritingAssistantSubView,
    isLoading,
    error,
    setCustomTones,
    setIsLoading,
    setError,
    hydrate,
    isHydrated,
    addCustomTone,
    updateCustomTone,
    deleteCustomTone,
    setUserText,
    // Add new exports for vocabulary card check
    hasVocabularyCards,
    isCheckingVocabularyCards,
    checkVocabularyCards,
  }
}) 