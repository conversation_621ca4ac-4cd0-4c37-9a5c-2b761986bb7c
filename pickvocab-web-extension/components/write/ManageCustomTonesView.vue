<script setup lang="ts">
import { ref } from 'vue';
import { useWritingStore } from './stores/writingStore';
import Spinner from '@/components/Spinner.vue';
const writingStore = useWritingStore();

const showAddToneForm = ref(false);
const addToneName = ref('');
const addToneDescription = ref('');
const addError = ref<string | null>(null);

async function handleAddTone() {
  addError.value = null;
  if (!addToneName.value.trim()) {
    addError.value = 'Name is required.';
    return;
  }
  try {
    await writingStore.addCustomTone({ name: addToneName.value, description: addToneDescription.value });
    addToneName.value = '';
    addToneDescription.value = '';
    showAddToneForm.value = false;
  } catch (e: any) {
    addError.value = e?.message || 'Failed to add tone.';
  }
}

async function handleDeleteTone(id: number) {
  await writingStore.deleteCustomTone(id);
}
</script>

<template>
  <div class="max-w-md mx-auto bg-white rounded-md shadow p-4 relative overflow-y-auto max-h-[500px]">
    <!-- Back Button -->
    <button
      class="mb-4 text-sm text-blue-600 hover:text-blue-800"
      @click="writingStore.setWritingAssistantSubView('main')"
    >
      ← Back
    </button>
    <h2 class="text-xl font-semibold mb-1">Manage Custom Tones</h2>
    <p class="text-gray-600 mb-4">Add new custom tones or remove existing ones.</p>

    <!-- Error Banner (non-blocking) -->
    <div v-if="writingStore.error" class="text-sm text-red-600 bg-red-50 p-2 rounded-md border border-red-200 mb-2">
      Error: {{ writingStore.error }}
    </div>

    <!-- Loading Overlay (static placeholder) -->
    <div v-if="writingStore.isLoading" class="absolute inset-0 bg-white/70 flex items-center justify-center z-10 rounded-md">
      <Spinner :size="8" />
      <span class="text-blue-600 ml-2">Loading...</span>
    </div>

    <h3 class="text-base font-semibold text-gray-800 mb-2">Existing custom tones</h3>
    <div v-if="writingStore.customTones.length === 0" class="text-sm text-gray-500 italic mb-3">No custom tones added yet.</div>
    <ul v-else class="space-y-3 max-h-[300px] overflow-y-auto rounded-md mb-4">
      <li v-for="tone in writingStore.customTones" :key="tone.id" class="flex items-center justify-between text-sm p-2 bg-white border border-gray-200 rounded-md shadow-sm">
        <div class="flex-grow mr-2">
          <span class="font-medium text-gray-800 block">{{ tone.name }}</span>
          <span class="text-xs text-gray-500 block mt-0.5">{{ tone.description || 'No description' }}</span>
        </div>
        <button
          type="button"
          class="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-100 rounded-full flex-shrink-0 ml-2"
          aria-label="Remove tone"
          @click="handleDeleteTone(tone.id)"
          :disabled="writingStore.isLoading"
        >
          <span class="inline-block w-4 h-4">🗑️</span>
        </button>
      </li>
    </ul>

    <!-- Add New Tone Section -->
    <div class="pt-2">
      <!-- Button to show the form -->
      <button 
        v-if="!showAddToneForm"
        @click="showAddToneForm = true"
        class="w-full flex items-center justify-center space-x-1 py-1.5 px-2 text-sm rounded-md hover:bg-gray-100 transition-colors border border-dashed border-gray-300 text-gray-600 hover:text-gray-800 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed mb-2"
      >
        <span class="inline-block w-4 h-4 mr-1">➕</span>
        <span>Add new tone</span>
      </button>
      <!-- Form to add new tone -->
      <div v-if="showAddToneForm" class="space-y-3 border border-gray-200 p-3 rounded-md bg-gray-50/50">
        <h3 class="text-base font-semibold text-gray-800">Add new tone details</h3>
        <div v-if="addError" class="text-sm text-red-600 bg-red-50 p-2 rounded-md border border-red-200 mt-2">
          {{ addError }}
        </div>
        <div class="space-y-2">
          <label for="toneName" class="text-sm font-medium text-gray-700">Name</label>
          <input 
            id="toneName"
            type="text" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="e.g., Friendly, Concise"
            v-model="addToneName"
            :disabled="writingStore.isLoading"
          />
        </div>
        <div class="space-y-2">
          <label for="toneDescription" class="text-sm font-medium text-gray-700">Description (optional)</label>
          <textarea 
            id="toneDescription"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[80px]"
            placeholder="Describe this tone"
            v-model="addToneDescription"
            :disabled="writingStore.isLoading"
          ></textarea>
        </div>
        <button 
          class="flex items-center justify-center ml-auto px-4 py-2 text-sm bg-blue-700 text-white rounded-md hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
          @click="handleAddTone"
          :disabled="writingStore.isLoading"
        >
          <span>Add</span>
        </button>
        <button
          class="mt-2 text-sm text-gray-500 hover:text-gray-700 underline"
          @click="showAddToneForm = false"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
</style> 