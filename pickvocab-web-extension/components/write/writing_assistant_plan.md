# Development Plan: Writing Assistant Feature for Web Extension (Iterative Workflow)

## Introduction

This document outlines an iterative plan to implement the Writing Assistant feature within the PickVocab web extension. The goal is to adapt the core functionality currently available in the web client, making it accessible via the extension's popup.

**Current Web Client Writing Assistant Features (to be adapted/ported):**

*   AI-powered text revision based on user input.
*   Selection of writing tones from a predefined list or user-created custom tones.
*   Management (create, update, delete) of custom writing tones.
*   Optional integration with the user's "My Vocabulary" via similarity search.
*   Display of multiple revision suggestions with associated LLM feedback and learning focus points.
*   Display and highlighting of vocabulary words used within the revisions (when vocabulary integration is enabled).
*   Navigation between different revision suggestions for a single request.
*   Persistent history of revision sessions (backend storage).
*   Saving user preferences (selected tone, vocabulary toggle) locally (`localStorage`).

This plan breaks down the work into small, demonstrable tasks, mixing UI implementation with placeholder logic, suitable for sequential development and review.

## Background Context & Analysis Summary

*   **Goal:** Replicate core Writing Assistant functionality (revision, tone management, history saving) in the extension popup, adapting the feature described in the web client's architecture document (`../../pickvocab-client/components/app/write/architecture.md`).
*   **Trigger:** "Fix writing" option in `OptionMenuView`, integrating into the flow established by the option view plan (`../../pickvocab-web-extension/entrypoints/content/option_view_plan.md`).
*   **Key Constraints/Decisions:**
    *   Code will be ported and adapted from the client (`architecture.md` provides component/logic details).
    *   UI will aim for similarity within popup constraints.
    *   API calls proxied through the background script.
    *   Dedicated `writingStore` for feature state.
    *   Use dedicated `ManageCustomTonesView` instead of a dialog.
    *   History *saving* is required; history *viewing/loading* UI is out of scope.
    *   Background script handles LLM YAML parsing.
    *   **[UPDATE 2024-06-09]**: Predefined tones now use `name` as their unique identifier (not `id`), matching the client. All code, types, and components have been updated to reflect this. Custom tones continue to use their numeric `id` as identifier.

## Iterative Implementation Plan

**Task 1: Foundation & Basic Trigger [COMPLETED]**

*   **Goal:** Setup file structure and make the "Fix writing" button navigate to a basic placeholder view.
*   **Steps:**
    1.  **Directory Setup:** Ensure `components/write/`, `components/write/components/`, `components/write/composables/`, `components/write/stores/`, `components/write/api/`, `components/write/utils/` exist. Create them if necessary.
    2.  **Create Placeholder Files:** Create empty Vue/TS files for `components/write/WritingAssistantView.vue`, `components/write/ManageCustomTonesView.vue`, `components/write/components/WritingInputSection.vue`, `components/write/components/WritingOutputSection.vue`, `components/write/components/ToneSelector.vue`, `components/write/stores/writingStore.ts`, `components/write/composables/useWritingRevisionHandler.ts`, `components/write/composables/useWritingRevisionApi.ts`, `components/write/api/index.ts`, `components/write/utils/revisionUtils.ts`, `components/write/utils/promptUtils.ts`.
    3.  **Update `contentStore`:** In `entrypoints/content/store.ts`:
        *   Ensure `selectedAction` state exists and `setSelectedAction` handles the `'fix writing'` value (it already does based on current code).
        *   Export the necessary state and function.
    4.  **Modify `OptionMenuView`:** In `entrypoints/content/OptionMenuView.vue`, remove the `disabled` attribute and `cursor-not-allowed opacity-70` classes from the "Fix writing" button. Add `@click="handleFixWriting"`. Implement the `handleFixWriting` method to call `contentStore.setSelectedAction('fix writing')`.
    5.  **Basic `FloatingPopupContainer` Logic:** In `entrypoints/content/FloatingPopupContainer.vue`, import `WritingAssistantView` from `components/write/WritingAssistantView.vue`. Add conditional logic: add an `else-if="selectedAction === 'fix writing'"` condition to render `<WritingAssistantView />`.
*   **Review:** Verify clicking "Fix writing" correctly switches the popup to an empty view.

**Task 2: Core Writing View Shell [COMPLETED]**

*   **Goal:** Implement the static layout of `WritingAssistantView`, including the input and output sections.
*   **Steps:**
    1.  **Port `WritingInputSection` Static UI:** Copy template/styling from client `InputSection.vue` into `components/write/components/WritingInputSection.vue`. Adapt for extension. Include static textarea, placeholder buttons, and placeholder `<ToneSelector/>`.
    2.  **Port `WritingOutputSection` Static UI:** Copy template/styling from client `OutputSection.vue` into `components/write/components/WritingOutputSection.vue`. Adapt. Include static placeholders for revised text, feedback, buttons.
    3.  **Assemble `WritingAssistantView`:** In `components/write/WritingAssistantView.vue`, include `<WritingInputSection />` and `<WritingOutputSection />`. Arrange basic layout.
*   **Review:** Verify the writing assistant view appears with static input and output areas resembling the client layout.

**Task 3: Input Area & Initial Text [COMPLETED]**

*   **Goal:** Make the input textarea functional and populate it with the text selected on the page.
*   **Steps:**
    1.  **Basic `writingStore`:** In `components/write/stores/writingStore.ts`, set up Pinia store, add `userText: ref('')` state.
    2.  **Connect Input:** In `components/write/components/WritingInputSection.vue`, import `useWritingStore` from '@/components/write/stores/writingStore'. Use `v-model` to bind the textarea to `writingStore.userText`.
    3.  **Populate Initial Text:** In `components/write/WritingAssistantView.vue`, import `useContentStore` from '@/entrypoints/content/store' and `useWritingStore` from '@/components/write/stores/writingStore'. In `onMounted`, read `contentStore.word` and set `writingStore.userText`.
*   **Review:** Verify selected text appears in the input area when the view loads, and typing in the textarea updates the state (can check with Vue DevTools).

**Task 4: Tone Selector Display (Static Data) [COMPLETED]**

*   **Goal:** Implement the `ToneSelector` UI and display a static list of predefined tones.
*   **Steps:**
    1.  **Port `ToneSelector` Static UI:** Copy template/styling from client `ToneSelector.vue` into `components/write/components/ToneSelector.vue`. Adapt.
    2.  **Add Predefined Tones:** In `writingStore.ts`, add a static array `predefinedTones` (e.g., `[{ name: 'General' }, ...]`) and a `selectedToneIdentifier: ref(null)` state. **[UPDATE 2024-06-09]**: Predefined tones use `name` as their identifier, not `id`.
    3.  **Display Tones:** In `ToneSelector.vue`, import `useWritingStore`. Use `v-for` to display `writingStore.predefinedTones` in a dropdown/list. Indicate the `selectedToneIdentifier` visually (e.g., highlight).
    4.  **Include Manage Custom Tones Trigger:** Ensure the static "Manage Custom Tones" button/link is present.
    5.  **Integrate into Input Section:** Ensure `<ToneSelector />` is correctly placed within `WritingInputSection.vue`.
*   **Review:** Verify the tone selector appears within the input section, displaying the static list of tones.

**Task 5: Output Area Structure (Static) [COMPLETED]**

*   **Goal:** Finalize the static structure of the output area based on the ported UI.
*   **Steps:**
    1.  **Review `WritingOutputSection.vue`:** Ensure all static elements (placeholders for text, feedback, focus, buttons for navigation, copy, refresh) are present and styled correctly.
*   **Review:** Verify the output section layout matches the intended static design.

**Task 6: Tone Management Navigation [COMPLETED]**

*   **Goal:** Enable clicking the "Manage Custom Tones" trigger to switch to the (currently empty) `ManageCustomTonesView` and back.
*   **Steps:**
    1.  **Add Sub-View State:** In `components/write/stores/writingStore.ts`, add state `writingAssistantSubView: ref('main')` and action `setWritingAssistantSubView(view: 'main' | 'manage_tones')`.
    2.  **Implement `FloatingPopupContainer` Logic Update:** Modify the conditional rendering in `entrypoints/content/FloatingPopupContainer.vue`: when `contentStore.selectedAction === 'fix writing'`, render based on `writingStore.writingAssistantSubView` (you'll need to import and use `writingStore` here), showing either `WritingAssistantView` or `ManageCustomTonesView`.
    3.  **Implement Navigation Trigger:**
        *   In `components/write/components/ToneSelector.vue`, add `@click` to the trigger -> `$emit('openManageTones')`.
        *   In `components/write/components/WritingInputSection.vue`, listen for `@openManageTones` -> `$emit('openManageTones')`.
        *   In `components/write/WritingAssistantView.vue`, listen for `@openManageTones` -> `writingStore.setWritingAssistantSubView('manage_tones')`.
    4.  **Implement Back Navigation Placeholder:** In `components/write/ManageCustomTonesView.vue` (still mostly empty), add a static "Back" button with `@click="writingStore.setWritingAssistantSubView('main')"` (import store first).
    5.  **Reset View on Mount:** In `components/write/WritingAssistantView.vue`'s `onMounted`, add `writingStore.setWritingAssistantSubView('main')`.
*   **Review:** Verify clicking "Manage Custom Tones" switches to the (empty) manage view, and clicking "Back" returns.

**Task 7: Tone Management UI (Static) [COMPLETED]**

*   **Goal:** Implement the static UI for the `ManageCustomTonesView`.
*   **Steps:**
    1.  **Port `ManageCustomTonesView` UI:** Copy template/styling from client `ManageCustomTonesDialog.vue` into `components/write/ManageCustomTonesView.vue`. Adapt for a view layout. Include static lists, forms, buttons.
*   **Review:** Verify the manage tones view displays the static UI elements correctly.

**Task 8: API/State Placeholders [COMPLETED]**

*   **Goal:** Set up the non-functional backend communication structure and full store state.
*   **Steps:**
    1.  **Background Handlers (Placeholders):** Implement basic message handlers in `entrypoints/background.ts` for all `WRITING_ASSISTANT_*` actions. They should log receipt and return mock data/success.
    2.  **API Helpers:** Implement functions in `components/write/api/index.ts` that send messages to the background placeholders.
    3.  **Full `writingStore` State:** Add all remaining state properties (`customTones`, `isLoading`, `error`, etc.) to `components/write/stores/writingStore.ts`.
    4.  **Port Utils:** Copy/adapt content into `components/write/utils/revisionUtils.ts`. Create `components/write/utils/promptUtils.ts` and implement functions to generate the necessary LLM prompt templates based on user input and selected tone, adapting logic from client files like `reviseTextSimplePrompt.ts`.
*   **Review:** Code review of the placeholder structures.

**Task 8.5: Type Definitions [COMPLETED]**

*   **Goal:** Define shared TypeScript interfaces for data structures used in the Writing Assistant feature.
*   **Steps:**
    1.  **Create Types File:** Create `components/write/types.ts`.
    2.  **Define Core Interfaces:** Define interfaces like `RevisionData` (based on LLM output, see client's `useRevisionApi.ts`), `SelectedToneIdentifier` (see client's `toneStore.ts`), `CustomTone` (for custom tones from API, see client's `api/write/customTones.ts`), etc., referencing the client architecture if needed. Note: Predefined tones now use `name` as their identifier, not `id` (**[UPDATE 2024-06-09]**).
    3.  **Use Types:** Import and utilize these types in relevant store, composable, component, and API files as they are developed.
*   **Review:** Ensure core data structures are typed correctly based on client definitions.

**Task 9: Dynamic Tone Loading & Selection [COMPLETED]**

*   **Goal:** Fetch custom tones from the (mocked) backend, load predefined tones, and implement tone selection logic.
*   **Steps:**
    1.  **(Update): Ensure `predefinedTones` state is initialized with hardcoded values in `writingStore.ts`.** (We will keep the predefined tones hardcoded in the store for this iteration, rather than loading from a config file). **[UPDATE 2024-06-09]**: Predefined tones use `name` as their identifier, not `id`.
    2.  **(Was Step 1): Implement `fetchCustomTones` Action:** In `writingStore.ts`, implement the action to call the API helper, handle mock response, update `customTones` state, set `isLoading` state.
    3.  **(Was Step 2): Call Load/Fetch on Mount:** In `WritingAssistantView.vue`'s `onMounted`, call `writingStore.loadPredefinedTones()` and `writingStore.fetchCustomTones()`.
    4.  **(Was Step 3): Implement `selectTone` Action:** In `writingStore.ts`, implement the action to update `selectedToneIdentifier`.
    5.  **(Was Step 4): Connect `ToneSelector`:** Modify `ToneSelector.vue` to:
        *   Get `allAvailableTones` (computed getter combining predefined/custom) and `selectedToneIdentifier` from the store.
        *   Display the dynamic list.
        *   Call `writingStore.selectTone` on selection change.
*   **Review:** Verify the tone selector shows combined tones (loaded predefined + mock custom), selection works, and loading state displays briefly.

**Task 9.5: Implement Writing Store Persistence [COMPLETED]**

*   **Goal:** Persist key user preferences from the writing store to the extension's local storage to maintain settings between sessions.
*   **Steps:**
    1.  **Identify State to Persist:** Determine which parts of the `writingStore` state should be persisted (definitely `selectedToneIdentifier`, possibly others like UI toggles if added later).
    2.  **Implement Loading:** In `writingStore.ts`, modify the store setup to load persisted values from `browser.storage.local` (e.g., using `storage.getItem('local:writing')`) upon initialization, providing sensible defaults if no saved state exists.
    3.  **Implement Saving:** Use Pinia's `$subscribe` mechanism or Vue's `watch` within the store setup to automatically save relevant state changes back to `browser.storage.local` (e.g., `storage.setItem('local:writing', { selectedToneIdentifier: state.selectedToneIdentifier, ... })`). Ensure saving is debounced or batched appropriately if needed to avoid excessive writes.
*   **Review:** Verify that the selected tone (and any other chosen state) persists correctly after closing and reopening the popup or browser.

**Task 10: Tone Management CRUD (Mocked) [COMPLETED]**

*   **Goal:** Implement the UI logic for adding, editing, and deleting custom tones using the mocked backend.
*   **Steps:**
    1.  **Implement Store CRUD Actions:** Implement `addCustomTone`, `updateCustomTone`, `deleteCustomTone` actions in `writingStore.ts` to call API helpers and optimistically update `customTones` state based on mock success responses.
    2.  **Connect `ManageCustomTonesView`:** Bind forms and buttons to call the respective store actions. Display the `customTones` list dynamically from the store.
*   **Review:** Verify adding/editing/deleting tones updates the list UI (using mock backend responses).

**Task 10.5: Implement Real Background Tone Logic [COMPLETED]**

*   **Goal:** Connect the tone management features to the actual backend API.
*   **Note:** The tone selection and CRUD logic now uses a robust `SelectedToneIdentifier` object (`{ type: 'predefined' | 'custom', identifier: string | number }`) for all tone selection, persistence, and UI logic, matching the client. All tone-related handlers and store logic use this model for consistency and extensibility. **[UPDATE 2024-06-09]**: Predefined tones use `name` as their identifier, not `id`.
*   **Steps:**
    1.  Update the `writingAssistantFetchCustomTones` handler in `background.ts` to make the real API call to fetch custom tones.
    2.  Update the `writingAssistantCreateCustomTone` handler in `background.ts` to make the real API call to create a custom tone.
    3.  Update the `writingAssistantUpdateCustomTone` handler in `background.ts` to make the real API call to update a custom tone.
    4.  Update the `writingAssistantDeleteCustomTone` handler in `background.ts` to make the real API call to delete a custom tone.
    5.  Ensure proper authentication and error handling are included in these background handlers.
        *   **API Client:** Implement a `RemoteCustomTonesApi` class in `@/api/customTones.ts` with methods for CRUD operations (`list`, `create`, `update`, `delete`).
            *   (Reference the implementation in `pickvocab-client/api/write/customTones.ts` for structure and logic.)
        *   **Authentication:** Ensure each method in `RemoteCustomTonesApi` uses the shared `axios` instance obtained via `getAxiosInstance()` (imported from `@/utils/axiosInstance`) for making requests. This leverages the default `Authorization` header managed by the background script, ensuring the correct token is automatically used.
        *   **Error Handling:** In the background message handlers (in `background.ts`) that use `RemoteCustomTonesApi`:
            *   Wrap the API calls (`api.list()`, `api.create()`, etc.) within `try...catch` blocks.
            *   Catch Axios errors (`axios.isAxiosError`), check `error.response.status` for specific codes (401/403 for auth, 400 for validation, 404 for not found, 5xx for server issues), and handle network/other errors.
            *   Return structured success data or structured error objects (e.g., `{ error: true, type: 'auth', status: 401 }`, `{ error: true, type: 'validation', detail: ... }`) back to the frontend message caller.
*   **Review:** Verify that adding, editing, deleting, and fetching custom tones via the extension UI now correctly interacts with the backend API and persists changes. Check that API errors (e.g., auth failures, server errors) are handled gracefully and potentially communicated to the user.

**Task 11: Revision Logic (Mocked) [COMPLETED]**

*   **Goal:** Make the "Revise" button trigger a process, display mock revision data, and enable navigation between mock revisions.
*   **Reference:** When implementing this task, **refer to the client's `useRevisionApi.ts` and `useWriteRevisionHandler.ts`** (in `pickvocab-client/components/app/write/`) for the canonical `RevisionData` structure and revision handling logic. Ensure your mock data matches the expected format and fields, so that the real API can be integrated later without changes to the consuming code.
*   **Steps:**
    1.  **Basic Composables:** Create basic structure for `useWritingRevisionApi` and `useWritingRevisionHandler`.
    2.  **Mock `generateRevision`:** In `useWritingRevisionApi`, implement a mock function that returns a predefined array of mock `RevisionData` after a short delay.
    3.  **Implement `useWritingRevisionHandler` (Mocked):** Implement `initiateRevision` to call the mock `generateRevision`, update state (`allRevisionsResult`, etc.). Implement `nextRevision`/`previousRevision`.
    4.  **Connect `WritingAssistantView`:** Instantiate handler. Connect "Revise" button to `handler.initiateRevision`. Connect `WritingOutputSection` props to handler state/computed properties. Connect navigation buttons.
    5.  **Implement OutputSectionHeader Button Logic:**
        *   Implement the logic for the "Refresh" and "Copy" buttons in `OutputSectionHeader`:
            *   The header component should emit `refresh-revision` and `copy-revised-text` events when the respective buttons are clicked.
            *   `WritingOutputSection` should listen for these events and emit them up to the parent (`WritingAssistantView.vue`), or handle them directly if the logic is local.
            *   In `WritingAssistantView`, handle these events:
                *   **Refresh:** Call `revisionHandler.initiateRevision()` again (optionally with a flag to indicate it's a refresh).
                *   **Copy:** Use the Clipboard API to copy the current revised text.
        *   **Reference:** When implementing the Refresh and Copy button logic, refer to the client's `OutputSectionHeader.vue` and related handler logic in `pickvocab-client/components/app/write/` for canonical event emission, clipboard handling, and any user feedback patterns.
*   **Review:** Verify clicking "Revise" shows mock results in the output, prev/next buttons cycle through them, and Refresh/Copy buttons work as expected.

**Task 12: Real Revision Generation & Display (Revised Breakdown with Verification)**

*   **Sub-task 12.1: Background Handler - Receive Request & Instantiate Service [COMPLETED]**
    *   **Goal:** Ensure the background handler receives the message with the prompt and instantiates the correct LLM service based on the user's active model selection or fallback.
    *   **Steps:**
        1.  Update `writingAssistantGenerateRevision` handler in `entrypoints/background.ts` to receive the `prompt` (but **not** the LLM config) from the popup.
        2.  In the background handler, retrieve the LLM config (models, providers, etc.) from storage (`local:llm`).
        3.  Implement `getModelByName` and any other necessary model/provider lookup helpers in the background (as simple array searches, referencing the store's implementation).
        4.  Select the active user model (first model with `isActive: true`). If found, create the corresponding chat source for that model (using provider/type switch logic as in the store).
        5.  If no active model is found, fallback to the Pickvocab model: use `getModelByName` to find the Pickvocab model, and use `createPickvocabModel.ts` (imported directly in the background) to create the Pickvocab chat source.
        6.  Add logging *inside* the handler to confirm receipt of the message, log the prompt, and log the model selection and chat source instantiation (success/failure or class name instantiated).
    *   **Verification:** Trigger the "Revise" action from the popup UI. Check the **background script's console logs** to confirm the handler was invoked, the prompt was received, the correct model was selected (or fallback used), and the log message indicating the LLM service instantiation attempt appears. (The process might still fail at the next step, but this confirms the initial handling).

*   **Sub-task 12.2: Background Handler - Call LLM & Log Raw Response [COMPLETED]**
    *   **Goal:** Execute the LLM call and log the outcome (raw response or error), matching the client logic.
    *   **Findings:**
        - The previous handler implementation only instantiated the chat source and did not call the LLM or log the response/error.
        - The client implementation calls `chatSource.sendMessage(prompt)`, logs before and after, and handles errors.
    *   **Steps:**
        1. After instantiating the chat source, call `await modelSource.sendMessage(prompt)`.
        2. Log the prompt and model info before the call.
        3. Wrap the LLM call in `try...catch`.
        4. On success: log a confirmation and the *raw* LLM response.
        5. On failure: log the error and return a structured error object.
        6. Return the raw LLM response (or error) to the caller for further processing.
        7. All YAML parsing in this and subsequent steps must use the `yaml` package.
    *   **Verification:** Trigger "Revise" from the popup. Check the **background script's console logs** to see the prompt being sent, the model info, and the subsequent raw LLM response text (or an error message if the call failed).

*   **Sub-task 12.3: Background Handler - Extract, Parse YAML & Log Result [COMPLETED]**
    *   **Goal:** Process the raw LLM response to extract and parse the YAML content. (Look into the client to see how we did it)
    *   **Steps:**
        1.  Implement/integrate the `extractFromCodeBlock` utility.
        2.  Add logging *after* extraction to show the extracted YAML string (or indicate if none was found).
        3.  Add YAML parsing using the `yaml` package (`import YAML from 'yaml';`).
        4.  Wrap the parsing logic in `try...catch`.
        5.  Add logging *after* parsing:
            *   On success: Log a confirmation and the *parsed JavaScript object*.
            *   On failure: Log the YAML parsing error.
    *   **Verification:** Trigger "Revise" (ideally with input known to generate YAML). Check the **background script's console logs** to see the extracted YAML string and the resulting parsed object (or a parsing error).

*   **Sub-task 12.4: Background Handler - Validate, Normalize & Log Final Response [COMPLETED]**
    *   **Goal:** Validate and normalize the parsed data, and log the final structured data or error being sent back to the popup.
    *   **Steps:**
        1.  Implement validation logic (check `revisions` array, etc.).
        2.  Implement normalization logic (ensure `learning_focus` is an array, etc.).
        3.  Add logging *before* returning from the handler:
            *   On success: Log the final, validated, and normalized `RevisionData[]` array being returned.
            *   On failure (validation, normalization, or errors caught earlier): Log the structured error object being returned.
    *   **Verification:** Trigger "Revise". Check the **background script's console logs** for the final log message showing the exact structured data (`RevisionData[]`) or structured error object that the handler is sending back to the popup.

*   **Sub-task 12.5: Frontend - Update API Hook & Log Communication [COMPLETED]**
    *   **Goal:** Update the frontend API composable (`useWritingRevisionApi`) to send the full request and log the response received from the background.
    *   **Steps:**
        1.  Modify `generateRevision` in `useWritingRevisionApi` to send the prompt and LLM config.
        2.  Add logging *within* `generateRevision` (in the popup's context):
            *   Before sending the message: Log the config/prompt being sent.
            *   After receiving the response: Log the full data or error object received from the background script.
    *   **Verification:** Trigger "Revise". Check the **popup's console logs** (or content script console if the popup logs there) to see the message being sent and the structured response/error received back from the background handler.

*   **Sub-task 12.6: Frontend - Adapt Handler & Display Results/Errors [COMPLETED]**
    *   **Goal:** Connect the handler (`useWritingRevisionHandler`) to the updated API hook and display the real revisions or errors in the UI.
    *   **Steps:**
        1.  Ensure `useWritingRevisionHandler` correctly processes the `RevisionData[]` or the structured error received via `useWritingRevisionApi`.
        2.  Update the `WritingOutputSection.vue` component (and its children) to correctly display the `currentRevision` data based on the structure in `RevisionData`.
        3.  Implement UI elements to display error messages based on the error state populated by the handler when the API call fails.
    *   **Verification:** Observe the **popup UI**:
        *   On success: Does the output section now display the first revision suggestion generated by the *real* LLM, including its text, feedback, and learning focus? Do the previous/next buttons work if multiple revisions were generated?
        *   On failure: Does the UI display a user-friendly error message indicating that the revision failed (potentially hinting at the type of error if available from the structured error object)?

**Task 13: History Saving [COMPLETE]**

*   **Goal:** Save the completed revision to the backend history.
*   **Steps:**
    1.  **Implement Real Background Save:** Update the `writingAssistantSaveHistory` handler in `background.ts`:
        *   Make the real backend API call to save the history using the shared `axios` instance from `getAxiosInstance()` for authentication.
        *   Implement robust `try...catch` error handling (similar to Task 10.5, Step 5), returning a structured success or error response.
    2.  **Implement `saveHistory` in `useWritingRevisionApi`:** Call the API helper, ensuring it handles the structured response from the background handler.
    3.  **Call Save:** In `useWritingRevisionHandler`'s `initiateRevision`/`refreshRevision`, call `useWritingRevisionApi.saveHistory` after successfully receiving revisions. Handle potential errors reported by the API call.
*   **Reference:** When implementing this task, **refer to the client's history saving logic** (see `pickvocab-client/components/app/write/useRevisionApi.ts` and related files) for canonical payload structure, error handling, and API usage patterns. This ensures consistency and leverages best practices already established in the main web client.
*   **Review:** Verify (e.g., via network tools or checking the web app) that history entries are saved after a revision. Ensure API errors during saving are handled.

**Task 14: Final Polish [COMPLETED]**
- Remove all mock and logs during the implementation

This plan provides a clear, step-by-step path with review points after key functional milestones.

## Deferred Features

The following features from the web client's Writing Assistant are explicitly **not** included in this initial implementation plan for the web extension:

*   **Grammar Check:**
    *   Grammar Check toggle in the input section.
    *   Associated backend logic or LLM prompts for grammar checking.
*   **Revision History UI & Loading:** (Already noted as out of scope) The `HistoryDialog` component and logic to browse or load past revisions are not included. Only saving history is implemented.

## Vocabulary Integration Implementation Plan (Tasks 15-25)

This plan integrates the vocabulary features, assuming successful completion of Task 14 (Final Polish) from the original plan.

**Task 15: UI - Vocabulary Toggle & State [COMPLETED]**

*   **Goal:** Add the "Use My Vocabulary" toggle to the UI and manage its state.
*   **Steps:**
    1.  **Port `VocabularyToggle.vue`:** Adapt the component from `pickvocab-client/components/app/write/VocabularyToggle.vue` into `components/write/components/VocabularyToggle.vue`.
    2.  **Add State:** In `components/write/stores/writingStore.ts`, add `useVocabulary: ref(false)` state.
    3.  **Integrate Toggle:** Add the `<VocabularyToggle />` component within `components/write/components/WritingInputSection.vue`, binding its value using `v-model` to `writingStore.useVocabulary`.
    4.  **Persistence:** Update the persistence logic in `writingStore.ts` (Task 9.5) to also load/save the `useVocabulary` state to/from `browser.storage.local` (e.g., key `local:writing.useVocabulary`).
*   **Review:** Verify the toggle appears, its state changes are reflected in the store, and the state persists across popup sessions.

**Task 16: Types & Utilities [COMPLETED]**

*   **Goal:** Update TypeScript definitions and port necessary utility functions.
*   **Steps:**
    1.  **Update `RevisionData`:** In `components/write/types.ts`, update the `RevisionData` interface to include `user_vocabularies_used?: string[]` (LLM indices) and `real_card_ids?: string[]` (actual card IDs).
    2.  **Add `Card` Type:** Ensure a suitable `Card` interface (representing vocabulary cards fetched from the API) exists or is added to `components/write/types.ts` (or a shared types location). Refer to `pickvocab-client/utils/card.ts` or API definitions.
    3.  **Port Utilities:** Adapt necessary utility functions from the client, such as `formatCardForPrompt` (from `useWriteRevisionHandler.ts`) and potentially card-related helpers, placing them in `components/write/utils/` or similar.
*   **Review:** Code review of types and ported utility functions.

**Task 17: Backend API & Background Handlers (Similarity Search) [COMPLETED]**

*   **Goal:** Implement background script logic to handle vocabulary similarity search requests.
*   **Steps:**
    1.  **API Client:** Ensure an API client class exists for card operations, including similarity search initiation and result fetching (e.g., adapt `RemoteGenericCardsApi` from client `api/genericCard.ts` for use in the background script, if not already available). Make sure it uses the shared authenticated `axios` instance.
    2.  **New Background Handler (`writingAssistantSimilaritySearch`):**
        *   Create a new message handler in `entrypoints/background.ts`.
        *   It should receive the `userText` to search.
        *   Call the backend API client to *initiate* the similarity search task.
        *   **Internal Polling:** Implement polling logic *within this handler* (using `setTimeout` with exponential backoff) to check the task status via the API client.
        *   Once the task succeeds, fetch the resulting card data.
        *   Return the fetched `Card[]` or a structured error object (e.g., `{ error: true, type: 'search_failed' | 'timeout' }`) to the popup.
        *   Include robust error handling (`try...catch`) for API calls and polling timeouts.
*   **Review:** Verify the background handler correctly initiates search, polls, fetches results, and returns data/errors via background console logs and message responses.

**Task 17.5: Frontend Test - Trigger & Log Similarity Search [TESTED]**

*   **Goal:** Add temporary frontend logic to trigger the background similarity search when the vocabulary toggle is enabled and log the results for verification. This verifies Task 17's background handler independently.
*   **Steps:**
    1.  **API Composable (`useWritingRevisionApi`):** Add a temporary function `testSimilaritySearch(text: string)` that sends a message to the `writingAssistantSimilaritySearch` background handler. It should simply log the raw success response (`Card[]`) or structured error object received from the background to the **frontend console**.
    2.  **Trigger Search on Toggle:** In `components/write/components/VocabularyToggle.vue` (or the component managing the toggle state):
        *   Import `useWritingRevisionApi` and `useWritingStore`.
        *   Use `watch` to monitor the `writingStore.useVocabulary` state.
        *   When the toggle transitions from `false` to `true`:
            *   Retrieve the current `userText` from `writingStore`.
            *   Call `api.testSimilaritySearch(userText.value)`.
*   **Review:** Turn the "Use My Vocabulary" toggle ON. Check the **frontend console** (popup or content script console) to verify that the `Card[]` array returned by the background handler (Task 17) is logged successfully, or that a structured error is logged if the background search failed. This confirms the background handler works as expected before full integration. Remove the test logic after verifying.

**Task 18: Frontend API & Handler Logic (Similarity Search) [COMPLETED]**

*   **Goal:** Integrate the similarity search call into the frontend revision workflow, structuring the handler similarly to the client.
*   **Steps:**
    1.  **API Composable (`useWritingRevisionApi`):** Add a new function `findSimilarCards(text: string)` that sends a message to the `writingAssistantSimilaritySearch` background handler and returns the `Promise<Card[]>` or handles the structured error.
    2.  **Handler Composable (`useWritingRevisionHandler`):**
        *   Add state refs: `isRevising: ref(false)`, `isLoadingVocabularyCards: ref(false)`, `revisionError: ref(null)` (or similar), `cardCacheById: ref(new Map())`, and `indexToIdMap: ref(new Map())`.
        *   **Create Helper Functions:** Implement internal helper functions `initiateRevisionWithSearch()` and `initiateRevisionDirectly()`, mirroring the client's structure.
            *   `initiateRevisionWithSearch()`:
                *   Sets `isRevising`, `isLoadingVocabularyCards`.
                *   Calls `api.findSimilarCards()`.
                *   Handles success: populates `cardCacheById` and `indexToIdMap`, resets `isLoadingVocabularyCards`. Proceeds to prompt generation (Task 19).
                *   Handles failure: sets `revisionError`, resets loading states.
            *   `initiateRevisionDirectly()`:
                *   **Move the current logic of `initiateRevision` into this function immediately.**
                *   This is a pure refactor: no behavior changes, just a function move. Do not wait for Task 19.
                *   Sets `isRevising` (or `isLoading`), builds the prompt, calls the revision API, handles errors/state, and saves history with `useMyVocabulary: false`.
        *   **Modify `initiateRevision`:**
            *   At the beginning, add `if (writingStore.grammarCheckEnabled) { await initiateGrammarCheck(); return; }`. (Use `writingStore.grammarCheckEnabled` directly or the passed-in ref depending on how state is managed).
            *   The rest of the function (checking `useVocabulary`, calling `initiateRevisionWithSearch` or `initiateRevisionDirectly`) remains as the `else` path.
*   **Review:** Verify that when "Use My Vocabulary" is toggled on, the revision process includes a loading state for cards, calls the background search via `initiateRevisionWithSearch`, populates caches/maps, and correctly handles success/error before proceeding. Check console logs for API calls and handler state changes. Verify the non-vocabulary path calls `initiateRevisionDirectly`.

**Note:** This step is a pure refactor for the non-vocabulary path. The logic move should be done immediately, not deferred to Task 19.

**Alignment Confirmation:** The refactoring in Task 18 creates the necessary structure for the vocabulary-specific logic added in Tasks 19, 20, and 21. The plan remains consistent and sequential.

**Task 19: Prompt Generation & Backend Revision Handler Update [COMPLETED]**

*   **Goal:** Modify prompt generation and the background revision handler to use vocabulary data.
*   **Steps:**
    1.  **Port `useMyVocabularyPrompt`:** Adapt the prompt generation logic from `pickvocab-client/components/app/write/useMyVocabularyPrompt.ts` into `components/write/utils/promptUtils.ts`.
    2.  **Modify `useWritingRevisionHandler` (Prompt Generation Logic):**
        *   **In `initiateRevisionWithSearch()`:** *After* the `api.findSimilarCards()` call succeeds (from Task 18) and cards/map are populated:
            *   Use the ported `useMyVocabularyPrompt` utility (passing formatted cards, `indexToIdMap`, and tone details) to build the vocabulary-aware prompt.
            *   Call `api.generateRevision`, passing the generated vocabulary-aware prompt and the `indexToIdMap`. (Note: `initiateRevisionDirectly` already handles simple prompt generation as part of the logic moved in Task 18).
    3.  **Modify `useWritingRevisionApi`:** Update `generateRevision` signature to accept optional `indexToIdMap`. Pass this along in the message payload to the background.
    4.  **Modify Background Handler (`writingAssistantGenerateRevision`):**
        *   Update the handler in `entrypoints/background.ts` to receive the optional `indexToIdMap` from the message payload.
        *   Ensure the YAML parsing logic correctly extracts the `user_vocabularies_used` array from the LLM response if present.
        *   **Crucially:** Before returning the parsed `RevisionData[]`, add the logic to process `user_vocabularies_used` using the received `indexToIdMap` to populate the `real_card_ids` field for each revision (similar to `processLLMRevisions` in the client handler). *This logic now runs in the background script.*
*   **Review:** Verify the correct prompt is generated based on the toggle (simple prompt via `Directly` path, vocab prompt via `WithSearch` path). Verify the background handler receives the map (only on the vocab path), processes LLM output correctly, and adds `real_card_ids` to the `RevisionData` sent back to the popup. Check console logs in both popup and background.

**Task 20: Frontend Handler - Process Results & History Save [COMPLETED]**

*   **Goal:** Update the frontend handler to process vocabulary results and ensure history saving includes vocabulary context.
*   **Steps:**
    1.  **Modify `useWritingRevisionHandler`:**
        *   When receiving `RevisionData[]` from `api.generateRevision` (in both `initiateRevisionDirectly` and `initiateRevisionWithSearch`), check for the presence of `real_card_ids`. Store this information appropriately (e.g., update `vocabularyWasUsedForLastRevision: ref(boolean)`). (Note: `cardCacheById` is populated in Task 18).
    2.  **Modify History Saving:**
        *   Update the payload sent to `api.saveHistory` (and subsequently the background handler `writingAssistantSaveHistory`) to include:
            *   `useMyVocabulary: writingStore.useVocabulary.value`
            *   The `real_card_ids` associated with each revision (already part of the `RevisionData` structure received from the background in Task 19).
    3.  **Modify Background Handler (`writingAssistantSaveHistory`):** Ensure the handler correctly receives and saves the `useMyVocabulary` flag and the `real_card_ids` within the revision data to the backend history entry.
*   **Review:** Verify `real_card_ids` are correctly associated with revisions in the handler's state. Verify history entries saved to the backend include the `useMyVocabulary` flag and `real_card_ids`.

**Task 21: UI - Display Used Vocabulary**

*   **Goal:** Show the vocabulary cards used in the current revision.
*   **Steps:**
    1.  **Port `UsedVocabularyList.vue`:** Adapt the component from the client into `components/write/components/UsedVocabularyList.vue`.
    2.  **Port `VocabularyCard.vue` (or similar display component):** Adapt the component used to display a single card within the list (likely a simplified version).
    3.  **Integrate into `WritingOutputSection.vue`:**
        *   Conditionally render `<UsedVocabularyList />` based on whether the `currentRevision` has `real_card_ids`.
        *   Pass the necessary card data (fetched using `real_card_ids` and the `cardCacheById` from `useWritingRevisionHandler`) as props to `UsedVocabularyList`.

**Task 22: Highlighting Types & Utilities [COMPLETED]**

*   **Goal:** Define necessary types and utility functions for the highlighting process, mirroring the client implementation.
*   **Steps:**
    1.  **Add Types:** In `components/write/types.ts`, define interfaces `RevisionHighlightInput` (containing `originalIndex`, `revisionText`, `vocabularyList: string[]`) and `HighlightedRevisionItem` (containing `index`, `highlightedText`). Refer to the client's `highlightVocabularyPrompt.ts` and `useRevisionApi.ts` for structure.
    2.  **Port Utilities:**
        *   Adapt the `highlightVocabularyPrompt` function from the client (`components/app/write/highlightVocabularyPrompt.ts`) into `components/write/utils/promptUtils.ts`. This function takes an array of `RevisionHighlightInput` and generates the LLM prompt for highlighting.
        *   Adapt the logic from the client's `prepareHighlightingInput` helper function (within `useWriteRevisionHandler.ts`) into a new utility function in `components/write/utils/revisionUtils.ts`. This function takes `RevisionData[]` and the `cardCacheById` map and returns `RevisionHighlightInput[]`.
*   **Review:** Code review of the new types and ported utility functions. Ensure they match the client's structure and logic.

**Task 23: Background Handler (Highlighting) [COMPLETED]**

*   **Goal:** Implement a background handler to perform the LLM call for highlighting vocabulary words.
*   **Steps:**
    1.  **New Background Handler (`writingAssistantHighlightVocabulary`):**
        *   Create a new message handler in `entrypoints/background.ts`.
        *   It should receive the `prompt` generated by the frontend (Task 22).
        *   Instantiate the appropriate LLM service (using active user selection or fallback, similar to Task 12.1).
        *   Call the LLM service with the received prompt (`await modelSource.sendMessage(prompt)`).
        *   Extract and parse the LLM response, expecting a structure that maps original revision indices to highlighted text (e.g., YAML list of objects matching `HighlightedRevisionItem`). Use the `yaml` package for parsing.
        *   Validate and normalize the parsed data.
        *   Return the validated `HighlightedRevisionItem[]` array or a structured error object back to the popup.
        *   Include logging for received prompt, LLM call, raw response, parsed data, and final return value/error.
*   **Review:** Trigger the handler manually (e.g., via browser devtools messaging) with a sample prompt. Check background console logs to verify LLM interaction, parsing, and the structure of the returned data or error.

**Task 24: Frontend API (Highlighting) [COMPLETED]**

*   **Goal:** Add a function to the frontend API composable to trigger the highlighting background handler.
*   **Steps:**
    1.  **Add API Function:** In `components/write/composables/useWritingRevisionApi.ts`, add a new function `generateHighlights(prompt: string): Promise<HighlightedRevisionItem[]>`.
    2.  This function sends a message to the `writingAssistantHighlightVocabulary` background handler with the `prompt`.
    3.  It should handle the structured response from the background, returning the `HighlightedRevisionItem[]` on success or throwing/handling the structured error on failure.
*   **Review:** Code review of the new API function. Ensure it correctly sends the message and handles the promise resolution/rejection based on the background response.

**Task 25: Frontend Handler (Highlighting Orchestration) [COMPLETED]**

*   **Goal:** Implement the logic in the revision handler to orchestrate the highlighting process after initial revisions are received.
*   **Steps:**
    1.  **Add Helper Functions (in `useWritingRevisionHandler.ts`):**
        *   `_generateAndParseHighlights(prompt: string): Promise<HighlightedRevisionItem[]>`: Calls `api.generateHighlights(prompt)` and handles its promise/error.
        *   `_applyHighlightsToState(highlightedItems: HighlightedRevisionItem[], revisionsToHighlight: RevisionHighlightInput[])`: Updates the `allRevisionsResult.value` array. It should find the correct revision by `originalIndex` and replace its `revision` property with the `highlightedText`. Include safety checks (like the client's `applyHighlightsToState`) to prevent accidental large changes.
        *   `_fetchAndApplyHighlights()`: The main orchestrator function.
            *   Calls the `prepareHighlightingInput` utility (from Task 22) using current `allRevisionsResult.value` and `cardCacheById.value`.
            *   If input is generated, calls the `highlightVocabularyPrompt` utility (Task 22) to create the prompt.
            *   Calls `_generateAndParseHighlights` with the prompt.
            *   If highlights are parsed successfully, calls `_applyHighlightsToState`.
            *   Includes `try...catch` block and logging for the overall process.
    2.  **Trigger Highlighting:** In `useWritingRevisionHandler.ts`, modify the `initiateRevisionWithSearch` function:
        *   After the initial revisions are successfully received from `api.generateRevision` and *after* the initial history save is triggered (Task 20), check if `vocabularyWasUsedForLastRevision.value` is true.
        *   If true, call `_fetchAndApplyHighlights()`. **Crucially, do *not* `await` this call.** It should run in the background without blocking the UI.
*   **Review:** Verify that when "Use My Vocabulary" is enabled and revisions are generated:
    *   The `_fetchAndApplyHighlights` function is called.
    *   The highlighting prompt is generated and sent via the API (check frontend/background logs).
    *   The `allRevisionsResult` state is updated with highlighted text upon successful response (check Vue DevTools or logs).
    *   The process handles errors gracefully.

**Task 26: History Update with Highlights [COMPLETED]**

*   **Goal:** Update the saved history entry with the highlighted revision text after the highlighting process completes.
*   **Steps:**
    1.  **Sub-task 26.1 (Track History ID):** In `useWritingRevisionHandler.ts`, ensure the `id` returned from the initial `saveHistory` call (Task 20) is stored in a ref (e.g., `currentHistoryEntryId: ref<number | null>(null)`). Reset this ref when a new revision cycle starts.
    2.  **Sub-task 26.2 (Backend/Background Handler):**
        *   Create a new background message handler: `writingAssistantUpdateHistory`.
        *   It should receive `historyId: number` and `payload: { revisions: RevisionData[] }`.
        *   Call the backend API (e.g., `PATCH /api/revision-history/{historyId}/`) using the shared authenticated `axios` instance, sending the `payload`.
        *   Return a simple success/error status. Include robust `try...catch`.
    3.  **Sub-task 26.3 (Frontend API):**
        *   Add `updateHistory(historyId: number, payload: { revisions: RevisionData[] })` to `useWritingRevisionApi.ts`.
        *   This function sends a message to the `writingAssistantUpdateHistory` handler.
    4.  **Sub-task 26.4 (Frontend Handler):**
        *   Add a helper `_updateHistoryWithHighlights()` in `useWritingRevisionHandler.ts`.
        *   This checks if `currentHistoryEntryId.value` is valid.
        *   If valid, it calls `api.updateHistory(currentHistoryEntryId.value, { revisions: allRevisionsResult.value })`. Includes `try...catch` and logging (errors here shouldn't block UI).
        *   Modify `_fetchAndApplyHighlights` to call `_updateHistoryWithHighlights()` *after* `_applyHighlightsToState` succeeds.
*   **Review:** Verify that after highlighting completes successfully:
    *   The `updateHistory` API call is triggered (check frontend/background logs).
    *   The corresponding history entry in the backend database is updated with the `revisions` array containing the highlighted text.

## Grammar Check Implementation Plan (Tasks 27-30)

This section details the plan to add the Grammar Check feature, mirroring the client implementation.

**Task 27: UI - Grammar Check Toggle & State [COMPLETED]**

*   **Goal:** Add a UI toggle for enabling Grammar Check and manage its state.
*   **Steps:**
    1.  **Add State:** In `components/write/stores/writingStore.ts`, add `grammarCheckEnabled: ref(false)` state.
    2.  **Add Toggle UI:** In `components/write/components/WritingInputSection.vue`:
        *   Add a `<Switch>` component (or similar existing UI element) next to a `<Label>` "Grammar Check Only".
        *   Bind the toggle's state using `v-model` to `writingStore.grammarCheckEnabled`.
    3.  **Persistence:** Update the persistence logic in `writingStore.ts` (Task 9.5) to also load/save the `grammarCheckEnabled` state (e.g., under `local:writing.grammarCheckEnabled`).
    4.  **Visual Disabling (Optional but Recommended):** In `WritingInputSection.vue`, add logic (e.g., using computed properties or `:disabled` bindings) to visually disable the "Use My Vocabulary" toggle and the `<ToneSelector />` when `writingStore.grammarCheckEnabled` is true.
*   **Review:** Verify the toggle appears, its state persists, and enabling it visually disables the vocabulary toggle and tone selector.

**Task 28: Port Grammar Check Prompt [COMPLETED]**

*   **Goal:** Create the grammar check prompt utility in the extension.
*   **Steps:**
    1.  **Copy & Adapt:** Copy the content of `pickvocab-client/components/app/write/grammarCheckPrompt.ts` into `components/write/utils/promptUtils.ts`. Export the `grammarCheckPrompt` function. Ensure imports (if any) are correct for the extension environment.
*   **Review:** Code review of the ported `grammarCheckPrompt` function in `promptUtils.ts`.

**Task 29: Update Revision Handler Logic [COMPLETED]**

*   **Goal:** Modify the `useWritingRevisionHandler` composable to handle the grammar check state and use the correct prompt and revision flow.
*   **Steps:**
    1.  **Add State Input:** Modify `components/write/composables/useWritingRevisionHandler.ts`:
        *   Update the exported function signature to accept `grammarCheckEnabled: Ref<boolean>` as an argument (if not already done for vocabulary/tone). Ensure it's passed down when the composable is instantiated in `WritingAssistantView.vue`.
        *   Import `grammarCheckPrompt` from `../utils/promptUtils`.
    2.  **Implement `initiateGrammarCheck` Helper:** Create a new async helper function `initiateGrammarCheck()` within the handler:
        *   Sets `isRevising.value = true;`.
        *   Resets `revisionError.value = null;`.
        *   Resets `currentHistoryEntryId.value = null;`.
        *   Sets `vocabularyWasUsedForLastRevision.value = false;` // Ensure vocab highlighting isn't triggered.
        *   Calls `grammarCheckPrompt(writingStore.userText)` to get the prompt.
        *   **Calls the LLM:** `const result = await generateRevision(prompt);`
        *   **Handles Response:**
            *   Checks if `isLlmErrorResult(result)`. If so, sets `revisionError.value` and resets loading states.
            *   If successful, sets `allRevisionsResult.value = result;`.
            *   Resets `currentRevisionIndex.value = 0;`.
        *   **Saves History:** *After* successfully receiving results, constructs the `SaveHistoryPayload` with `triggerType: 'GrammarCheck'` and calls `await saveRevisionHistory(payload);`. Include error handling around the save call (log only).
        *   Includes `try...catch` block around the LLM call and result processing.
        *   Resets `isRevising.value = false;` in the `finally` block.
    3.  **Modify `initiateRevision`:** Update the main `initiateRevision` function:
        *   At the beginning, add `if (writingStore.grammarCheckEnabled) { await initiateGrammarCheck(); return; }`. (Use `writingStore.grammarCheckEnabled` directly or the passed-in ref depending on how state is managed).
        *   The rest of the function (checking `useVocabulary`, calling `initiateRevisionWithSearch` or `initiateRevisionDirectly`) remains as the `else` path.
*   **Review:** Verify that when the "Grammar Check Only" toggle is enabled, clicking "Revise" or "Refresh" triggers the `initiateGrammarCheck` function, uses the correct prompt, calls the LLM, processes results, saves history with the correct trigger type, and bypasses vocabulary/tone logic. Check console logs and the flow in Vue DevTools.

**(End of Grammar Check Tasks)**
