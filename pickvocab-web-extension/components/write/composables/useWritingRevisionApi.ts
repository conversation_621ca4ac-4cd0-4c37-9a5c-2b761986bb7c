import type { RevisionData, LlmErrorResult, HighlightedRevisionItem } from '../types'
import { writingApi, type SaveHistoryPayload, type SaveHistoryResult, type SimilaritySearchResult, type SimilaritySearchErrorResult, type UpdateHistoryPayload, type UpdateHistoryResult } from '../api/index'
import type { Card } from '@/utils/card'

export function useWritingRevisionApi() {
  // Real implementation: calls background script via writingApi
  async function generateRevision(prompt: string, indexToIdMap?: Map<string, string>): Promise<RevisionData[] | LlmErrorResult> {
    const result = await writingApi.generateRevision(prompt, indexToIdMap);
    return result;
  }

  async function saveHistory(historyData: SaveHistoryPayload): Promise<SaveHistoryResult | LlmErrorResult> {
    return await writingApi.saveHistory(historyData);
  }

  async function updateHistory(historyId: number, payload: UpdateHistoryPayload): Promise<UpdateHistoryResult | LlmErrorResult> {
    return await writingApi.updateHistory(historyId, payload);
  }

  async function findSimilarCards(text: string): Promise<SimilaritySearchResult | SimilaritySearchErrorResult> {
    return await writingApi.findSimilarCards(text);
  }

  async function generateHighlights(prompt: string): Promise<HighlightedRevisionItem[] | LlmErrorResult> {
    return await writingApi.generateHighlights(prompt);
  }

  return {
    generateRevision,
    saveHistory,
    updateHistory,
    findSimilarCards,
    generateHighlights
  }
}