# Plan: Integrate TiptapEditor into RevisedTextViewer (Web Extension)

**Goal:** Update `pickvocab-web-extension/components/write/components/RevisedTextViewer.vue` to use the `TiptapEditor` component for rendering `revisedText`, enabling markdown support, similar to the client implementation.

**Pre-computation/Analysis Summary:**

*   `TiptapEditor.vue` component does not exist in the `pickvocab-web-extension` project.
*   Required Tiptap dependencies (`@tiptap/vue-3`, `@tiptap/starter-kit`, `tiptap-markdown`) are not listed in `pickvocab-web-extension/package.json`.
*   Note: The extension uses Vite/WXT and does not have <PERSON>ux<PERSON>'s auto-imports, requiring explicit imports of Tiptap components.

**Implementation Steps:**

1.  **Add Dependencies:**
    *   Add the following Tiptap packages to `pickvocab-web-extension/package.json`, matching the versions used in the client:
        - `@tiptap/vue-3@^2.11.7` (pin exact version)
        - `@tiptap/starter-kit@^2.10.x` (pin exact version)
        - `@tiptap/extension-highlight@^2.10.3`
        - `@tiptap/extension-placeholder@^2.10.3`
        - `tiptap-markdown@^0.8.10`
        - `@tiptap/extension-bubble-menu` (required for bubble menu functionality)
    *   Run the following command to add these packages:
        ```sh
        pnpm add @tiptap/vue-3@^2.11.7 @tiptap/starter-kit@^2.10.x @tiptap/extension-highlight@^2.10.3 @tiptap/extension-placeholder@^2.10.3 tiptap-markdown@^0.8.10 @tiptap/extension-bubble-menu -w --filter pickvocab-web-extension
        ```

2.  **Copy/Share Component:**
    *   Copy the `TiptapEditor.vue` file from `pickvocab-client/components/editor/TiptapEditor.vue` to `pickvocab-web-extension/components/editor/TiptapEditor.vue`.
    *   Add explicit imports at the top of the copied `TiptapEditor.vue`:
        ```ts
        import { useEditor, EditorContent, BubbleMenu } from '@tiptap/vue-3'
        import StarterKit from '@tiptap/starter-kit'
        import { Highlight } from '@tiptap/extension-highlight'
        import { Placeholder } from '@tiptap/extension-placeholder'
        import { Markdown } from 'tiptap-markdown'
        // ... other icon imports
        ```
    *   Identify and copy any associated utility files or custom Tiptap extensions imported by the client's `TiptapEditor.vue` to the corresponding location in the extension's structure (e.g., `pickvocab-web-extension/components/editor/utils/`). Create the `pickvocab-web-extension/components/editor/` directory if it doesn't exist.

3.  **Update Imports in `RevisedTextViewer.vue`:**
    *   In `pickvocab-web-extension/components/write/components/RevisedTextViewer.vue`:
        ```ts
        <script setup lang="ts">
        import TiptapEditor from '@/components/editor/TiptapEditor.vue'
        defineProps({ revisedText: String })
        </script>
        ```
    *   Change the import path for `TiptapEditor` to point to the newly copied location.

4.  **Implement `TiptapEditor` Usage:**
    *   In `pickvocab-web-extension/components/write/components/RevisedTextViewer.vue`:
        *   Replace the `<p v-else ...>` tag within the template.
        *   Add the `<TiptapEditor>` component inside the main `div`.
        *   Configure its props based on the client's usage in `pickvocab-client/components/app/write/RevisedTextViewer.vue`:
            *   `:text="revisedText"`
            *   `:editable="false"`
            *   `:show-options="false"`
            *   `:show-bubble-menu="false"`
            *   `:enable-markdown="true"`
            *   `:css-classes="'prose max-w-none focus:outline-none min-h-[inherit] text-sm'"` (adjust `min-h` and base text size as needed for the extension's context).
        *   Ensure the `v-if="revisedText"` logic correctly controls the display of the editor, and the `v-else` handles the placeholder text.
5.  **Styling:**
    *   Copy the base styling from the client's `RevisedTextViewer.vue`:
        - Container: `text-sm bg-white p-4 min-h-[200px] rounded-md border border-gray-200 shadow-inner`
        - Editor: `:css-classes="'max-w-none focus:outline-none min-h-[inherit]'"`
    *   Add necessary Tiptap editor styles in a dedicated CSS file (e.g., `pickvocab-web-extension/components/editor/TiptapEditor.css`).
    *   Import the styles in `TiptapEditor.vue`.
    *   Add any custom styles needed for proper markdown rendering.

6.  **Verification Steps:**

**Notes:**
- Keep package versions pinned to exact versions to avoid unexpected behavior changes.
- The extension's build environment differs from the client's Nuxt setup, requiring explicit imports.
- Consider adding error boundaries around the editor component to gracefully handle any initialization issues.
- Document any extension-specific modifications made to the editor component for future reference. 