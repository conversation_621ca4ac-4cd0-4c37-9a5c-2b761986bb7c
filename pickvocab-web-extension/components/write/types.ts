// Core type definitions for Writing Assistant feature
// References: pickvocab-client/components/app/write/useRevisionApi.ts, pickvocab-client/stores/toneStore.ts, pickvocab-client/api/write/customTones.ts

// Note: Card types are imported from @/utils/card.ts where needed

// --- Revision Data (from useRevisionApi.ts) ---
export interface RevisionData {
  revision: string;
  user_vocabularies_used?: string[]; // Index references for LLM-generated content
  real_card_ids?: string[]; // Actual card IDs for restored content
  feedback: string;
  learning_focus?: string[];
}

// --- Highlighting Types ---
export interface RevisionHighlightInput {
  originalIndex: number; // Keep track of the original index in allRevisionsResult
  revisionText: string;  // The text to be highlighted
  vocabularyList: string[]; // List of vocabulary words to highlight
}

export interface HighlightedRevisionItem {
  index: number;         // Corresponds to the originalIndex in RevisionHighlightInput
  highlightedText: string; // The revision text with highlighting markup applied
}

// --- Selected Tone Identifier (from toneStore.ts) ---
// Used for robust tone selection: type distinguishes predefined vs custom, identifier is name or id.
export interface SelectedToneIdentifier {
  type: 'predefined' | 'custom';
  identifier: string | number; // name for predefined, id for custom
}

// --- Predefined Tone (from toneStore.ts) ---
export interface PredefinedTone {
  name: string;
  description: string;
  bgColor: string;
  textColor: string;
}

// --- Custom Tone (from api/write/customTones.ts) ---
export interface CustomTone {
  id: number;
  ownerId: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  bgColor: string;
  textColor: string;
}

// --- Formatted Vocabulary for Prompts ---
export interface FormattedVocabulary {
  word: string;
  id: string;
}

// Error result type for LLM operations (mirrors backend)
export type LlmErrorResult = { error: true; type: string; message: string };