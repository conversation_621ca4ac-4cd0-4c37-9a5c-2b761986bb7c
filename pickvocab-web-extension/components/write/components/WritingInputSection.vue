<script setup lang="ts">
// @ts-ignore
import IconEdit from '@tabler/icons-vue/dist/esm/icons/IconEdit.mjs';
// @ts-ignore
import IconWand from '@tabler/icons-vue/dist/esm/icons/IconWand.mjs';
import ToneSelector from './ToneSelector.vue';
import VocabularyToggle from './VocabularyToggle.vue';
import GrammarCheckToggle from './GrammarCheckToggle.vue';
import { useWritingStore } from '../stores/writingStore';
// import { useWritingRevisionHandler } from '../composables/useWritingRevisionHandler';
import { computed, watch, onMounted } from 'vue';

const writingStore = useWritingStore();
const emit = defineEmits(['openManageTones']);
// const revisionHandler = useWritingRevisionHandler();

const props = defineProps({
  revisionHandler: { type: Object, required: true }
});

// Computed property to check if features should be disabled due to grammar check
const isDisabledByGrammarCheck = computed(() => writingStore.grammarCheckEnabled);

// Check for vocabulary cards when component is mounted
onMounted(async () => {
  if (writingStore.hasVocabularyCards === null) {
    await writingStore.checkVocabularyCards();
  }
});

function handleRevise() {
  props.revisionHandler.initiateRevision();
}

function handleTextareaKeydown(event: KeyboardEvent) {
  // Stop all keydown events from bubbling up when the textarea is focused
  event.stopPropagation();
}

function handleVocabularyToggle(newValue: boolean) {
  if (newValue && writingStore.grammarCheckEnabled) {
    writingStore.grammarCheckEnabled = false;
  }
  writingStore.useVocabulary = newValue;
}

function handleGrammarCheckToggle(newValue: boolean) {
  if (newValue && writingStore.useVocabulary) {
    writingStore.useVocabulary = false;
  }
  writingStore.grammarCheckEnabled = newValue;
}
</script>

<template>
  <div class="writing-input-section bg-white py-4 px-6">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold text-gray-800 flex items-center">
        <IconEdit class="h-5 w-5 text-blue-600 mr-2" />
        Your Text
      </h3>
    </div>

    <textarea
      class="mt-2 w-full min-h-[150px] text-base p-4 border border-gray-200 rounded-md shadow-inner focus:outline-none focus:ring-blue-500 bg-white text-gray-800"
      placeholder="Enter your text here..."
      v-model="writingStore.userText"
      @keydown="handleTextareaKeydown"  
    ></textarea>

    <div class="mt-4 space-y-4">
      <VocabularyToggle 
        :model-value="writingStore.useVocabulary"
        @update:model-value="handleVocabularyToggle"
        :disabled="props.revisionHandler.isLoading.value"
        :has-vocabulary-cards="writingStore.hasVocabularyCards ?? undefined"
      />

      <GrammarCheckToggle
        :model-value="writingStore.grammarCheckEnabled"
        @update:model-value="handleGrammarCheckToggle"
        :disabled="props.revisionHandler.isLoading.value"
      />
    </div>

    <div class="mt-5 flex justify-end space-x-3">
      <!-- Tone selector placeholder -->
      <ToneSelector 
        @openManageTones="emit('openManageTones')" 
        :disabled="isDisabledByGrammarCheck"
      />

      <!-- Revise button -->
      <button
        class="flex items-center px-5 py-2 text-sm bg-blue-700 text-white rounded-md hover:bg-blue-800 font-medium transition-colors shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
        :disabled="props.revisionHandler.isLoading.value"
        @click="handleRevise"
      >
        <IconWand class="h-4 w-4 mr-2" />
        <span>Revise</span>
      </button>
    </div>
  </div>
</template>

<style scoped>
.writing-input-section {
  margin-bottom: 1rem;
}
</style> 