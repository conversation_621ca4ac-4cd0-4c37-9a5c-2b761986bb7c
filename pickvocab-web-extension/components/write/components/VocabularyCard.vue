<script setup lang="ts">
import { defineProps } from 'vue';
import type { Card, DefinitionCard, ContextCard } from '@/utils/card';
import { CardType } from '@/utils/card';
import DefinitionCardEntry from './DefinitionCardEntry.vue';
import ContextCardEntry from './ContextCardEntry.vue';

const props = defineProps<{
  card: Card;
}>();

// Function to open the card details in a new tab
function openCardDetails(cardId: string | number) {
  try {
    const url = `${import.meta.env.WXT_CLIENT_API_URL}/app/cards/${cardId}`;
    window.open(url, '_blank');
  } catch (error) {
    console.error('Failed to open card details:', error);
  }
}
</script>

<template>
  <div 
    class="rounded-lg bg-white"
    @click="openCardDetails(card.id)"
  >
    <!-- Conditionally render based on card type -->
    <DefinitionCardEntry
      v-if="card.cardType === CardType.DefinitionCard"
      :card="card as DefinitionCard"
    />
    <ContextCardEntry
      v-else-if="card.cardType === CardType.ContextCard"
      :card="card as ContextCard"
    />
    
    <!-- Fallback if card type is not recognized (should not happen in practice) -->
    <div v-else class="flex flex-col space-y-2">
      <h4 class="text-md font-medium text-gray-900">
        Unknown Vocabulary Card
      </h4>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 