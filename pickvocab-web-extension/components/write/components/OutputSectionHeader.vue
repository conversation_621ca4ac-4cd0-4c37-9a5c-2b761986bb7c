<script setup lang="ts">
import { type Component } from 'vue';
import Spinner from '@/components/Spinner.vue';
// @ts-ignore
import IconRefresh from '@tabler/icons-vue/dist/esm/icons/IconRefresh.mjs';
// @ts-ignore
import IconCopy from '@tabler/icons-vue/dist/esm/icons/IconCopy.mjs';
// @ts-ignore
import IconPencil from '@tabler/icons-vue/dist/esm/icons/IconPencil.mjs'; // Default icon

defineProps({
  title: { type: String, default: 'Revised Text' },
  iconComponent: { type: Object as () => Component, required: false },
  isRefreshing: { type: Boolean, default: false }, // Prop to control spinner
});

defineEmits(['refreshRevision', 'copyRevisedText']);
</script>

<template>
  <div class="flex justify-between items-center">
    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
      <!-- Use Icon provided by parent or default -->
      <component :is="iconComponent" class="h-5 w-5 text-emerald-600 mr-2" v-if="iconComponent" />
      <IconPencil v-else class="h-5 w-5 text-emerald-600 mr-2" />
      {{ title }}
    </h3>
    <div class="flex space-x-1">
      <button
        @click="$emit('refreshRevision')"
        class="p-2 text-gray-600 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
        :disabled="isRefreshing"
        title="Refresh revision"
      >
        <Spinner v-if="isRefreshing" size="5" />
        <IconRefresh v-else class="w-5 h-5" />
        <span class="sr-only">Refresh</span>
      </button>
      <button
        @click="$emit('copyRevisedText')"
        class="p-2 text-gray-600 hover:bg-gray-100 rounded-md"
        title="Copy revised text"
      >
        <IconCopy class="w-5 h-5" />
        <span class="sr-only">Copy</span>
      </button>
    </div>
  </div>
</template>
