<script setup lang="ts">
// @ts-expect-error: no types for tabler icon mjs import
import IconChevronDown from '@tabler/icons-vue/dist/esm/icons/IconChevronDown.mjs';
// @ts-expect-error: no types for tabler icon mjs import
import IconSettings from '@tabler/icons-vue/dist/esm/icons/IconSettings.mjs';
import { useWritingStore } from '../stores/writingStore';
import { ref } from 'vue';
import type { SelectedToneIdentifier } from '../types';

const emit = defineEmits(['openManageTones']);

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
});

const writingStore = useWritingStore();
const showDropdown = ref(false);

function selectTone(tone: any) {
  writingStore.selectTone({ type: tone.type, identifier: tone.identifier });
  showDropdown.value = false;
}
function openManageTones() {
  emit('openManageTones');
}
function getSelectedTone() {
  return writingStore.allAvailableTones.find(t => t.type === writingStore.selectedTone?.type && t.identifier === writingStore.selectedTone?.identifier) || null;
}
function toggleDropdown() {
  if (!props.disabled) {
    showDropdown.value = !showDropdown.value;
  }
}
</script>

<template>
  <div class="tone-selector">
    <div class="relative">
      <button 
        class="flex items-center justify-between px-3 py-2 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 w-48 focus:outline-none focus:ring-2 focus:ring-blue-500"
        :class="{ 'opacity-60 cursor-not-allowed hover:bg-white': disabled }"
        @click="toggleDropdown"
        type="button"
        aria-haspopup="listbox"
        :aria-expanded="showDropdown"
        :disabled="disabled"
      >
        <div class="flex items-center truncate">
          <span
            class="h-3 w-3 rounded-full mr-1.5 inline-block"
            :class="getSelectedTone()?.bgColor"
          ></span>
          <span class="truncate font-medium">
            {{ getSelectedTone()?.name || 'Select Tone' }}
          </span>
        </div>
        <IconChevronDown class="ml-1 w-4 h-4 text-gray-700" />
      </button>
      <div v-if="showDropdown && !disabled" class="absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-10">
        <ul class="py-1 max-h-72 overflow-y-auto" tabindex="-1" role="listbox">
          <!-- Predefined Tones Group -->
          <li v-if="writingStore.predefinedTones.length > 0" class="px-2 py-1 text-xs text-gray-700 font-semibold select-none">Default tones</li>
          <li v-for="tone in writingStore.predefinedTones" :key="'predefined-' + tone.name">
            <button
              class="w-full text-left px-3 py-2 hover:bg-blue-50 flex items-center rounded-md transition-colors"
              :class="{ 'bg-blue-100 font-semibold': writingStore.selectedTone && 'predefined' === writingStore.selectedTone?.type && tone.name === writingStore.selectedTone?.identifier }"
              @click="selectTone({ type: 'predefined', identifier: tone.name })"
              role="option"
              :aria-selected="!!(writingStore.selectedTone && 'predefined' === writingStore.selectedTone?.type && tone.name === writingStore.selectedTone?.identifier)"
            >
              <span
                class="h-3 w-3 rounded-full mr-4 inline-block flex-shrink-0"
                :class="tone.bgColor"
              ></span>
              <div class="flex flex-col">
                <span class="font-medium text-sm">{{ tone.name }}</span>
                <span class="text-xs text-gray-700 whitespace-normal mt-0.5 font-normal">{{ tone.description }}</span>
              </div>
            </button>
          </li>
          <!-- Separator if both groups exist -->
          <li v-if="writingStore.predefinedTones.length > 0 && writingStore.customTones.length > 0">
            <div class="my-1 border-t border-gray-200"></div>
          </li>
          <!-- Custom Tones Group -->
          <li v-if="writingStore.customTones.length > 0" class="px-2 py-1 text-xs text-gray-700 font-semibold select-none">Custom tones</li>
          <li v-for="tone in writingStore.customTones" :key="'custom-' + tone.id">
            <button
              class="w-full text-left px-3 py-2 hover:bg-blue-50 flex items-center rounded-md transition-colors"
              :class="{ 'bg-blue-100 font-semibold': writingStore.selectedTone && 'custom' === writingStore.selectedTone?.type && tone.id === writingStore.selectedTone?.identifier }"
              @click="selectTone({ type: 'custom', identifier: tone.id })"
              role="option"
              :aria-selected="!!(writingStore.selectedTone && 'custom' === writingStore.selectedTone?.type && tone.id === writingStore.selectedTone?.identifier)"
            >
              <span
                class="h-3 w-3 rounded-full mr-4 inline-block flex-shrink-0"
                :class="tone.bgColor"
              ></span>
              <div class="flex flex-col">
                <span class="font-medium text-sm">{{ tone.name }}</span>
                <span class="text-xs text-gray-700 whitespace-normal mt-0.5 font-normal">{{ tone.description }}</span>
              </div>
            </button>
          </li>
        </ul>
        <div class="border-t border-gray-200 mt-1">
          <button
            class="w-full flex items-center justify-center space-x-1 py-3 px-3 text-sm rounded-md hover:bg-gray-100 transition-colors font-medium text-gray-700"
            @click="openManageTones"
          >
            <IconSettings class="h-4 w-4 mr-1" />
            <span>Manage custom tones</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.tone-selector {
  position: relative;
}
</style> 