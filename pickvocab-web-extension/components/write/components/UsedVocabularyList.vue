<script setup lang="ts">
import { type PropType } from 'vue';
import type { Card } from '@/utils/card';
import VocabularyCard from './VocabularyCard.vue';

// Check if we need the Spinner component
import Spinner from '@/components/Spinner.vue';

defineProps({
  usedVocabularyCards: {
    type: Array as PropType<Card[]>,
    required: true
  },
  isLoadingVocabularyCards: { type: Boolean, default: false },
});
</script> 

<template>
  <div>
    <div v-if="isLoadingVocabularyCards" class="flex justify-center items-center py-4">
      <Spinner size="6" />
    </div>
    <div v-else-if="usedVocabularyCards && usedVocabularyCards.length > 0" class="space-y-3">
      <!-- Use the VocabularyCard component to display each card -->
      <VocabularyCard 
        v-for="card in usedVocabularyCards" 
        :key="card.id || ''" 
        :card="card"
      />
    </div>
    <div v-else class="text-sm text-gray-400 italic py-2">
      No specific vocabulary words from your list were used in this revision.
    </div>
  </div>
</template>
