<script setup lang="ts">
import { ref, type PropType, watch, computed } from 'vue';
import type { Card } from '@/utils/card';
import type { RevisionData } from '../types';
import OutputSectionHeader from './OutputSectionHeader.vue';
import RevisedTextViewer from './RevisedTextViewer.vue';
import UsedVocabularyList from './UsedVocabularyList.vue';
import LLMFeedbackDisplay from './LLMFeedbackDisplay.vue';
// import Spinner from '@/components/Spinner.vue'; // REMOVED Spinner Import
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'; // Using shadcn-vue collapsible

// @ts-ignore
import IconChevronLeft from '@tabler/icons-vue/dist/esm/icons/IconChevronLeft.mjs';
// @ts-ignore
import IconChevronRight from '@tabler/icons-vue/dist/esm/icons/IconChevronRight.mjs';
// @ts-ignore
import IconMessageCircle from '@tabler/icons-vue/dist/esm/icons/IconMessageCircle.mjs';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';

const props = defineProps({
  isLoading: { type: Boolean, default: false },
  isLoadingVocabularyCards: { type: Boolean, default: false },
  revisedText: { type: String, default: '' },
  llmFeedbackText: { type: String, default: '' },
  learningFocus: { type: Array as PropType<string[]>, default: () => [] },
  usedVocabularyCards: {
    type: Array as PropType<Card[]>,
    default: () => []
  },
  currentRevisionIndex: { type: Number, default: 0 },
  totalRevisions: { type: Number, default: 0 },
  vocabularyWasUsed: { type: Boolean, default: false },
  currentRevisionData: { type: Object as PropType<RevisionData | undefined>, default: undefined },
  cardCacheById: { 
    type: Object as PropType<Map<string, Card>>, 
    default: () => new Map() 
  }
});

// State for collapsible sections - Controlled by v-model
const showLLMFeedback = ref(false); // Default closed
const showUsedVocabulary = ref(false); // Default closed

// Computed property to determine if we should show the vocabulary section
const shouldShowVocabularySection = computed(() => {
  return props.vocabularyWasUsed || 
    (props.currentRevisionData && 
     Array.isArray(props.currentRevisionData.real_card_ids) && 
     props.currentRevisionData.real_card_ids.length > 0);
});

// Computed property to get the cards to display
const cardsToDisplay = computed(() => {
  if (!props.currentRevisionData || 
      !Array.isArray(props.currentRevisionData.real_card_ids) ||
      props.currentRevisionData.real_card_ids.length === 0) {
    return [];
  }
  
  // Filter out any card IDs that don't exist in the cache
  return props.currentRevisionData.real_card_ids
    .map(id => props.cardCacheById.get(id))
    .filter((card): card is Card => !!card);
});

const emits = defineEmits([
  'copyRevisedText',
  'refreshRevision',
  'navigateRevision',
]);


function navigate(direction: 'prev' | 'next') {
  emits('navigateRevision', direction);
}
</script>

<template>
  <!-- Show spinner when loading -->
  <!-- <div v-if="props.isLoading" class="flex justify-center items-center p-10"> -->
    <!-- <Spinner :size="'8'" /> -->
  <!-- </div> -->
  <!-- Show error if present and not loading -->
  <!-- <div v-else class="bg-white border border-gray-200 rounded-lg shadow-sm p-4 space-y-4 mb-4"> -->
  <div class="bg-white py-4 px-6 space-y-4 mb-4">
    <!-- <div v-if="props.error" class="flex items-center bg-red-50 border border-red-200 text-red-800 rounded-md px-3 py-2 mb-2 text-sm"> -->
      <!-- <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z"/></svg> -->
      <!-- <span class="font-medium">{{ props.error }}</span> -->
    <!-- </div> -->
    <OutputSectionHeader
      @refresh-revision="emits('refreshRevision')"
      @copy-revised-text="emits('copyRevisedText')"
      :is-refreshing="false"  /> <!-- TODO: Bind isRefreshing prop later -->

    <RevisedTextViewer :revised-text="props.revisedText" />

    <!-- Revision Navigation Controls - Show only if more than one revision -->
    <div v-if="props.totalRevisions > 1" class="flex items-center justify-center space-x-4 mt-2 mb-2">
      <button
        @click="navigate('prev')"
        :disabled="props.currentRevisionIndex === 0"
        class="p-2 rounded-md text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Previous revision"
      >
        <IconChevronLeft class="w-5 h-5" />
      </button>
      <span class="text-sm font-medium text-gray-700">
        Revision {{ props.currentRevisionIndex + 1 }} of {{ props.totalRevisions }}
      </span>
      <button
        @click="navigate('next')"
        :disabled="props.currentRevisionIndex >= props.totalRevisions - 1"
        class="p-2 rounded-md text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Next revision"
      >
        <IconChevronRight class="w-5 h-5" />
      </button>
    </div>

    <!-- Feedback Section using shadcn Collapsible -->
    <Collapsible v-model:open="showLLMFeedback">
      <CollapsibleTrigger as-child>
        <button class="flex items-center justify-between w-full cursor-pointer hover:bg-gray-100 rounded-md py-2 px-3 text-left border border-transparent hover:border-gray-200 transition-colors">
          <div class="flex items-center">
            <IconMessageCircle class="w-5 h-5 mr-3 text-emerald-600 flex-shrink-0" />
            <h4 class="font-medium text-gray-800">Feedback</h4>
          </div>
            <IconChevronRight class="h-5 w-5 text-gray-400 transition-transform duration-200" :class="{ 'rotate-90': showLLMFeedback }"/>
        </button>
      </CollapsibleTrigger>
      <CollapsibleContent class="mt-2">
          <LLMFeedbackDisplay
            :llm-feedback-text="props.llmFeedbackText"
            :learning-focus="props.learningFocus"
          />
      </CollapsibleContent>
    </Collapsible>

    <!-- Conditionally render Used Vocabulary section using shadcn Collapsible -->
    <Collapsible v-if="shouldShowVocabularySection" v-model:open="showUsedVocabulary">
        <CollapsibleTrigger as-child>
          <button class="flex items-center justify-between w-full cursor-pointer hover:bg-gray-100 rounded-md py-2 px-3 text-left border border-transparent hover:border-gray-200 transition-colors">
            <div class="flex items-center">
              <IconBook2 class="w-5 h-5 mr-3 text-emerald-600 flex-shrink-0" />
              <h4 class="font-medium text-gray-800">Used Vocabulary</h4>
                <span
                v-if="cardsToDisplay.length > 0"
                class="inline-flex items-center justify-center ml-2 w-5 h-5 text-xs font-semibold text-emerald-800 bg-emerald-100 rounded-full"
              >
                {{ cardsToDisplay.length }}
              </span>
            </div>
            <IconChevronRight class="h-5 w-5 text-gray-400 transition-transform duration-200" :class="{ 'rotate-90': showUsedVocabulary }"/>
          </button>
        </CollapsibleTrigger>
        <CollapsibleContent class="mt-2">
          <UsedVocabularyList
            :used-vocabulary-cards="cardsToDisplay"
            :is-loading-vocabulary-cards="props.isLoadingVocabularyCards"
          />
        </CollapsibleContent>
    </Collapsible>
  </div>
</template> 