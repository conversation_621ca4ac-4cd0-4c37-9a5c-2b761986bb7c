import { type FormattedVocabulary, type RevisionHighlightInput } from '../types';

/**
 * Generates a prompt for revising text without vocabulary integration
 * @param selectedToneStyleDescription The selected tone style description
 * @param userText The user's text to revise
 * @returns A prompt string for the LLM
 */
export function generateRevisionPrompt(selectedToneStyleDescription: string, userText: string): string {
  return `You're a writing assistant that improves text based on a selected tone and style.

## Your Task:
Provide 3 revised versions of the user's text that:
1. Enhance overall writing quality (flow, structure, clarity)
2. Maintain the user's authentic voice
3. Apply the user's selected tone/style: ${selectedToneStyleDescription}

## Guidelines:
- Each revision should take a different approach to improving the text.
- Ensure your response is in valid YAML format that can be parsed programmatically.
- IMPORTANT: If the 'User's Text' appears to be a question, DO NOT answer the question. Your sole purpose is to revise the phrasing and structure of the user's text itself according to the other guidelines.

## Revision-Specific Instructions:
- Revision 1: Ensure the length is approximately the same as the original text. Focus on clarity and structure improvements.
- Revision 2: Maintain approximately the same length as the original text. Take a different stylistic approach than the first revision (e.g., more concise, more descriptive).
- Revision 3: Explore a significantly different way to express the core message, perhaps by rephrasing key sentences or adjusting the overall emphasis.

## Output Format:
\`\`\`yaml
revisions:
  - revision: |-
      Full text of first revision here. IMPORTANT: Ensure this text is indented with exactly 6 spaces. Uses the multi-line YAML format with |- to preserve formatting. Length should be approximately the same as the original text.
    feedback: |-
      Comprehensive analysis comparing original and revised versions. Address structure, organization, flow, coherence, and sentence structure improvements. Include technical elements like grammar, punctuation, and mechanics corrections. Comment on language choices and word choice improvements. Describe how tone and style changes affect reader engagement while maintaining the writer's authentic voice.
    learning_focus:
      - "Key point 1 for user to focus on in future writing"
      - "Key point 2 for user to focus on in future writing"
      # Include 2-3 most important learning points

  - revision: |-
      Full text of second revision here. IMPORTANT: Ensure this text is indented with exactly 6 spaces. Use a different stylistic approach than the first revision. Length should be approximately the same as the original text.
    feedback: |-
      Detailed feedback for second revision following the same comprehensive approach as above. Focus on the different stylistic choices made in this version.
    learning_focus:
      - "Key learning point 1 for this revision"
      - "Key learning point 2 for this revision"
      # Include 2-3 most important learning points

  - revision: |-
      Full text of third revision here. IMPORTANT: Ensure this text is indented with exactly 6 spaces. This version should explore a significantly different way to express the core message.
    feedback: |-
      Detailed feedback focused on the alternative approach taken in this revision and its impact on clarity, style, and message delivery.
    learning_focus:
      - "Learning point about alternative phrasing or structuring"
      - "Learning point about the impact of different stylistic choices"
      # Include 2-3 most important learning points
\`\`\`

User's Text:
${userText}
`;
}

/**
 * Generates a prompt for revising text with vocabulary integration
 * @param selectedToneStyleDescription The selected tone style description
 * @param userText The user's text to revise
 * @param userVocabularies The user's vocabulary words to integrate
 * @returns A prompt string for the LLM
 */
export function useMyVocabularyPrompt(
  selectedToneStyleDescription: string,
  userText: string,
  userVocabularies: FormattedVocabulary[]
): string {
  return `You're a writing assistant that improves text while integrating user's vocabulary words naturally.

## Your Task:
Provide 3 revised versions of the user's text that:
1. Enhance overall writing quality (flow, structure, clarity)
2. Naturally incorporate appropriate words from their vocabulary list
3. Maintain the user's authentic voice
4. Apply the user's selected tone/style: ${selectedToneStyleDescription}

## Guidelines:
- Only use vocabulary words that genuinely fit the context
- Each revision should take a different approach
- Prioritize natural writing over forced vocabulary usage
- Ensure your response is in valid YAML format that can be parsed programmatically
- IMPORTANT: If the 'User's Text' appears to be a question, DO NOT answer the question. Your sole purpose is to revise the phrasing and structure of the user's text itself according to the other guidelines.

## Revision-Specific Instructions:
- Revision 1: Ensure the length is approximately the same as the original text. Focus on clarity and structure improvements.
- Revision 2: Maintain approximately the same length as the original text. Take a different stylistic approach than the first revision.
- Revision 3: Prioritize incorporating as many user vocabulary words as possible, while still ensuring they fit naturally and appropriately. This revision may be slightly longer if needed to accommodate more vocabulary integration.

## Output Format:
\`\`\`yaml
revisions:
  - revision: |-
      Full text of first revision here. IMPORTANT: Ensure this text is indented with exactly 6 spaces. Uses the multi-line YAML format with |- to preserve formatting. Length should be approximately the same as the original text.
    user_vocabularies_used: # User's vocabularies used in the revision
      - word_1_id
      - word_2_id
      # More if any
    feedback: |-
      Comprehensive analysis comparing original and revised versions. Address structure, organization, flow, coherence, and sentence structure improvements. Include technical elements like grammar, punctuation, and mechanics corrections. Comment on language choices, word choice improvements, and how vocabulary words naturally enhance meaning. Describe how tone and style changes affect reader engagement while maintaining the writer's authentic voice.
    learning_focus:
      - "Key point 1 for user to focus on in future writing"
      - "Key point 2 for user to focus on in future writing"
      # Include 2-3 most important learning points

  - revision: |-
      Full text of first revision here. IMPORTANT: Ensure this text is indented with exactly 6 spaces. Uses the multi-line YAML format with |- to preserve formatting. Length should be approximately the same as the original text.
    user_vocabularies_used: # User's vocabularies used in the revision
      - word_1_id
      - word_2_id
      # More if any
    feedback: |-
      Detailed feedback for second revision following the same comprehensive approach as above. Focus on the different stylistic choices made in this version.
    learning_focus:
      - "Key learning point 1 for this revision"
      - "Key learning point 2 for this revision"
      # Include 2-3 most important learning points

  - revision: |-
      Full text of first revision here. IMPORTANT: Ensure this text is indented with exactly 6 spaces. Uses the multi-line YAML format with |- to preserve formatting. Length should be approximately the same as the original text.
    user_vocabularies_used: # User's vocabularies used in the revision
      - word_1_id
      - word_2_id
      - word_3_id
      - word_4_id
      # More if any
    feedback: |-
      Detailed feedback focused on how vocabulary integration enhances the writing while maintaining coherence and clarity. Discuss the balance between vocabulary usage and natural expression.
    learning_focus:
      - "Learning point about effective vocabulary integration"
      - "Learning point about maintaining authentic voice while expanding vocabulary"
      # Include 2-3 most important learning points
\`\`\`

## Example Output:
\`\`\`yaml
revisions:
  - revision: |-
      I think we should implement a stopgap measure to temporarily fix the issue, and then, in the meantime, we can work on finding a more permanent solution. This approach will allow us to address the problem immediately while also exploring long-term fixes down the line.
    user_vocabularies_used:
      - "0"
      - "8"
    feedback: |-
      This revision improves clarity by introducing the concept of a "stopgap" measure... The structure is enhanced... The use of "down the line" effectively conveys... The revision prioritizes natural expression...
    learning_focus:
      - "Using vocabulary words like 'stopgap' can enhance clarity..."
      - "Structuring sentences clearly can improve coherence..."
# --- (Further revisions would follow the same structure) ---
\`\`\`

User's Text:
${userText}

Words:
${JSON.stringify(userVocabularies)}
`;
}

/**
 * Creates a prompt for highlighting vocabulary words in revisions
 * @param revisionsToHighlight An array of objects containing revision text and vocabulary list
 * @returns A prompt string for the LLM
 */
export function highlightVocabularyPrompt(revisionsToHighlight: RevisionHighlightInput[]): string {
  if (!revisionsToHighlight || revisionsToHighlight.length === 0) {
    console.warn('highlightVocabularyPrompt called with empty or invalid revisionsToHighlight array.');
    return ''; // Or handle as appropriate
  }

  // Construct the input data block for the prompt
  const inputDataBlock = revisionsToHighlight.map(revision => `
  - index: ${revision.originalIndex}
    text: |
      ${revision.revisionText.replace(/\n/g, '\n      ')}
    vocabulary:
${revision.vocabularyList.map(word => `      - ${word}`).join('\n')}
  `).join('');

  return `
You will receive a list of text revisions, each associated with an original index and a list of vocabulary words/phrases used within that specific revision.

Your task is to process **each revision independently**. For each revision, apply Markdown bold tags (\`**word**\`) to all occurrences of the vocabulary words/phrases provided *for that specific revision*. Follow the crucial matching guidelines below.

**Input Data:**
The input is provided as a YAML list, where each item represents a revision to be processed:

\`\`\`yaml
${inputDataBlock}
\`\`\`

**Crucial Matching Guidelines (Apply to each revision independently):**
- Your primary goal is to identify phrases in the text that *semantically match* the vocabulary items for that revision, even if they aren't character-for-character identical.
- **Handle Variations:** Bold phrases in the text that are variations of the vocabulary items (e.g., tense, pluralization, minor word changes).
- **Case Insensitivity:** Match vocabulary items regardless of case, but preserve the original casing of the text within the bold tags.
- **Prioritize Meaning:** Prioritize semantic meaning when deciding whether to bold a phrase.
- **Exact Matches:** Still bold exact matches.
- **No Rewriting:** Only add bold tags (\`** **\`). Do *not* modify the text in any other way.
- **Multiple Occurrences:** Bold *all* matching occurrences within the specific revision.

**Output Format:**
Return your response as a **single YAML code block**. The root of the YAML structure **must be an object** with a single key named \`highlighted_revisions\`. The value of this key **must be a list** where each item corresponds to an input revision and includes the original \`index\` and the resulting \`highlightedText\` with the vocabulary bolded according to the rules.

**Crucially, ensure the output is ONLY the YAML code block, starting with \`\`\`yaml and ending with \`\`\`. Do not include any introductory text or explanations outside the code block.**

**Overall Input/Output Example (Demonstrating Structure and Guideline Application):**

*Example Input:*
\`\`\`yaml
- index: 0
  text: |
    The quick brown fox jumps over the lazy dog. He saw two big dogs later.
  vocabulary:
    - quick brown fox
    - lazy dog
    - dog
- index: 1
  text: |
    You get the hang of it, congrats! Learning takes time.
  vocabulary:
    - getting the hang of
    - learn
- index: 2
  text: |
    Let's deliver a temporary patch to fix the issue at hand, buying us time to develop a more leading, permanent solution over the long haul. This isn't a janky duct tape fix; it's a strategic move to ensure we're not just imposing a quick solution on someone but actually solving the problem.
  vocabulary:
    - delivered
    - leading
    - over the long haul
    - imposing something on someone
    - janky duct tape
\`\`\`

*Example Output:*
\`\`\`yaml
highlighted_revisions:
  - index: 0
    highlightedText: |
      The **quick brown fox** jumps over the **lazy dog**. He saw two big **dogs** later.
  - index: 1
    highlightedText: |
      You **get the hang of it**, congrats! **Learning** takes time.
  - index: 2
    highlightedText: |
      Let's **deliver** a temporary patch to fix the issue at hand, buying us time to develop a more **leading**, permanent solution **over the long haul**. This isn't a **janky duct tape** fix; it's a strategic move to ensure we're not just **imposing** a quick solution **on someone** but actually solving the problem.
\`\`\`

Now, process the provided input data based *strictly* on these rules and return the result in the specified YAML format.
  `;
}

/**
 * Generates a prompt for grammar-check-only mode
 *
 * This prompt instructs the LLM to only correct grammar, spelling, and punctuation
 * without altering the tone, style, or structure of the text.
 *
 * @param userText The text to be grammar-checked
 * @returns A prompt string for the LLM
 */
export function grammarCheckPrompt(userText: string): string {
  return `
You are a helpful writing assistant that specializes in grammar correction.

# Instructions
- Correct ONLY grammar mistakes, spelling errors, and punctuation issues in the text.
- DO NOT alter the tone, style, structure, or content of the text.
- DO NOT rewrite sentences or paragraphs unless necessary to fix grammar.
- DO NOT add or remove information.
- Return exactly ONE revision with the corrected text.
- Include feedback that lists the specific grammar issues you fixed.
- If the writing sounds awkward or unnatural, include a note in the feedback suggesting the user to revise those sections.
- Include 2-3 key learning points about the grammar rules applied.

# Format
Return your response in YAML format as follows:

\`\`\`yaml
revisions:
  - revision: |-
      <corrected text with grammar, spelling, and punctuation fixed>
      <IMPORTANT: Ensure the revision text is indented with exactly 6 spaces. Uses the multi-line YAML format with |- to preserve formatting>
    feedback: |-
      <list of grammar issues found and corrections made>
      <if the writing sounds awkward or unnatural, please suggest the user to revise>
    learning_focus:
      - <key grammar rule or concept applied #1>
      - <key grammar rule or concept applied #2>
      - <optional additional learning point #3>
\`\`\`

# Text to correct
${userText}
`;
}