import { CardType, type Card } from '@/utils/card';
import { type FormattedVocabulary, type RevisionData, type RevisionHighlightInput } from '../types';

/**
 * Formats a card for use in LLM prompts with index references
 * @param card The card to format
 * @param idx The index to use as ID for LLM reference
 * @returns A formatted vocabulary object with word details and index as ID
 */
export function formatCardForPrompt(card: Card, idx: number): FormattedVocabulary | null {
  if (!card) return null;
  
  // Format the card information based on its type
  if (card.cardType === CardType.DefinitionCard) {
    const definitionText = card.definition?.definition ?? '';
    return {
      word: `word: ${card.word}\ndefinition: ${definitionText}`,
      id: idx.toString()
    };
  } else if (card.cardType === CardType.ContextCard && card.wordInContext) {
    const definition = card.wordInContext.definition?.definition || 
                        card.wordInContext.definitionShort?.explanation || '';
    return {
      word: `word: ${card.wordInContext.word}\ndefinition: ${definition}`,
      id: idx.toString()
    };
  }
  
  // Log warning for unhandled card types
  console.warn(`Unhandled card type for card at index ${idx}. Card data:`, card);
  return null;
}

/**
 * Prepares input data for the highlighting process by extracting vocabulary words from revisions
 * @param revisions The revisions data array
 * @param cardCacheById Map of card IDs to Card objects
 * @returns Array of RevisionHighlightInput objects
 */
export function prepareHighlightingInput(
  revisions: RevisionData[],
  cardCacheById: Map<string, Card>
): RevisionHighlightInput[] {
  const revisionsToHighlight: RevisionHighlightInput[] = [];

  revisions.forEach((revisionData, index) => {
    // Only use real_card_ids for lookups
    const cardIds = revisionData.real_card_ids;

    if (Array.isArray(cardIds) && cardIds.length > 0) {
      const vocabularyWords = cardIds.map(cardId => {
        const card = cardCacheById.get(String(cardId));

        if (!card) {
          console.warn(`Card not found in cardCacheById for ID: ${cardId} in revision index ${index}`);
          return undefined;
        }
        // Extract the primary word/phrase based on card type
        return card.cardType === CardType.DefinitionCard ? card.word : card.wordInContext?.word;
      }).filter((word): word is string => word !== undefined && word.trim() !== '');

      if (vocabularyWords.length > 0) {
        revisionsToHighlight.push({
          originalIndex: index, // The index in the revisions array
          revisionText: revisionData.revision,
          vocabularyList: vocabularyWords
        });
      }
    }
  });

  return revisionsToHighlight;
}

/**
 * Parse revision data from API response
 * @param raw The raw data to parse
 * @returns The parsed revision data
 */
export function parseRevisionData(raw: any): any {
  // TODO: Implement real parsing/validation logic
  return raw;
}

/**
 * Validate revision format
 * @param data The data to validate
 * @returns Whether the data is valid
 */
export function validateRevisionFormat(data: any): boolean {
  // TODO: Implement real validation logic
  return !!data && Array.isArray(data.revisions);
}

/**
 * Helper to extract content from YAML code blocks
 * @param text The text to extract the code block from
 * @returns The extracted code block content or the original text if no code block is found
 */
export function extractFromCodeBlock(text: string): string {
  if (!text) return '';

  // Regex pattern for YAML code block extraction (VERY robust pattern)
  const codeBlockPattern = /```(?:yaml)?\s*([\s\S]*?)\s*```/i;
  const match = text.match(codeBlockPattern);

  if (match && match[1]) {
    return match[1].trim();
  }

  // If no code block found, return the input with simple formatting cleanup
  // This acts as a fallback in case the LLM didn't use a code block
  const cleanedText = text.trim()
    .replace(/^```yaml\s*/, '') // Remove starting delimiter
    .replace(/\s*```$/, '');    // Remove ending delimiter
    
  return cleanedText;
}