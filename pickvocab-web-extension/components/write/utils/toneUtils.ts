import type { SelectedToneIdentifier, PredefinedTone, CustomTone } from '../types';

export function getToneStyleDescription(
  selected: SelectedToneIdentifier | null,
  predefinedTones: PredefinedTone[],
  customTones: CustomTone[]
): string {
  if (!selected) return '';
  if (selected.type === 'predefined') {
    const tone = predefinedTones.find(t => t.name === selected.identifier);
    return tone ? `${tone.name} - ${tone.description}` : '';
  } else if (selected.type === 'custom') {
    const tone = customTones.find(t => t.id === selected.identifier);
    return tone ? `${tone.name} - ${tone.description}` : '';
  }
  return '';
} 