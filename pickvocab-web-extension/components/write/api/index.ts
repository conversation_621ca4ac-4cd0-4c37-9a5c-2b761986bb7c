// Placeholder for writing assistant API
// Will contain functions to communicate with the background script

import { sendMessage } from 'webext-bridge/content-script';
import type { RevisionData, LlmErrorResult, CustomTone, HighlightedRevisionItem } from '../types';
import type { Card } from '@/utils/card';

// Define a local JsonValue type for JSON-serializable values
export type JsonValue = string | number | boolean | null | JsonValue[] | { [key: string]: JsonValue };

// Define payload/result types for tone and history operations
export interface FetchCustomTonesResult {
  success: true;
  tones: CustomTone[];
}
export type CreateCustomTonePayload = Omit<CustomTone, 'id' | 'ownerId' | 'createdAt' | 'updatedAt'>;
export interface CreateCustomToneResult {
  success: true;
  tone: CustomTone;
}
export interface UpdateCustomTonePayload extends Partial<CreateCustomTonePayload> {
  id: number;
  [key: string]: unknown;
}
export interface UpdateCustomToneResult {
  success: true;
  tone: CustomTone;
}
export interface DeleteCustomToneResult {
  success: true;
}
export interface SaveHistoryPayload {
  // Required fields for saving history
  userText: string;
  selectedTone: string;
  // Array of revisions to save
  revisions: RevisionData[];
  // Optional fields with defaults in the background handler
  triggerType?: 'Revise' | 'Refresh' | 'GrammarCheck';
  useMyVocabulary?: boolean;
  // Optional array of card IDs if vocabulary is used
  cardIds?: string[];
}
export interface SaveHistoryResult {
  success: true;
  historyId: number;
}

// Define type for history update payload
export interface UpdateHistoryPayload {
  revisions: RevisionData[];
}
export interface UpdateHistoryResult {
  success: true;
}

// Define type for similarity search response
export interface SimilaritySearchResult {
  cards: Card[];
}

export type SimilaritySearchErrorResult = LlmErrorResult & {
  type: 'search_failed' | 'timeout';
};

// Define type for checking if user has vocabulary cards
export interface CheckVocabularyCardsResult {
  success: true;
  hasVocabularyCards: boolean;
}

export const writingApi = {
  async fetchCustomTones(): Promise<FetchCustomTonesResult | LlmErrorResult> {
    return await sendMessage('writingAssistantFetchCustomTones', {}) as FetchCustomTonesResult | LlmErrorResult;
  },
  async createCustomTone(tone: CreateCustomTonePayload): Promise<CreateCustomToneResult | LlmErrorResult> {
    return await sendMessage('writingAssistantCreateCustomTone', tone) as CreateCustomToneResult | LlmErrorResult;
  },
  async updateCustomTone(tone: UpdateCustomTonePayload): Promise<UpdateCustomToneResult | LlmErrorResult> {
    return await sendMessage('writingAssistantUpdateCustomTone', tone as JsonValue) as UpdateCustomToneResult | LlmErrorResult;
  },
  async deleteCustomTone(toneId: number): Promise<DeleteCustomToneResult | LlmErrorResult> {
    return await sendMessage('writingAssistantDeleteCustomTone', { id: toneId }) as DeleteCustomToneResult | LlmErrorResult;
  },
  async generateRevision(prompt: string, indexToIdMap?: Map<string, string>): Promise<RevisionData[] | LlmErrorResult> {
    return await sendMessage('writingAssistantGenerateRevision', { 
      prompt,
      indexToIdMap: indexToIdMap ? Object.fromEntries(indexToIdMap) : undefined
    }) as RevisionData[] | LlmErrorResult;
  },
  async saveHistory(historyData: SaveHistoryPayload): Promise<SaveHistoryResult | LlmErrorResult> {
    return await sendMessage('writingAssistantSaveHistory', historyData as unknown as JsonValue) as SaveHistoryResult | LlmErrorResult;
  },
  async updateHistory(historyId: number, payload: UpdateHistoryPayload): Promise<UpdateHistoryResult | LlmErrorResult> {
    return await sendMessage('writingAssistantUpdateHistory', { 
      historyId, 
      payload: payload as unknown as JsonValue 
    }) as UpdateHistoryResult | LlmErrorResult;
  },
  async findSimilarCards(text: string): Promise<SimilaritySearchResult | SimilaritySearchErrorResult> {
    return await sendMessage('writingAssistantSimilaritySearch', { userText: text }) as SimilaritySearchResult | SimilaritySearchErrorResult;
  },
  async generateHighlights(prompt: string): Promise<HighlightedRevisionItem[] | LlmErrorResult> {
    return await sendMessage('writingAssistantHighlightVocabulary', { prompt }) as HighlightedRevisionItem[] | LlmErrorResult;
  },
  async checkVocabularyCards(): Promise<CheckVocabularyCardsResult | LlmErrorResult> {
    return await sendMessage('writingAssistantCheckVocabularyCards', {}) as CheckVocabularyCardsResult | LlmErrorResult;
  }
};