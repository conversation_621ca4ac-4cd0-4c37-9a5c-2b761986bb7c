<script setup lang="ts">
import { computed } from 'vue';
// @ts-ignore
import IconLoader2 from '@tabler/icons-vue/dist/esm/icons/IconLoader2.mjs';

interface Props {
  text: string;           // Button text in normal state
  loadingText?: string;   // Button text when loading (defaults to "Processing...")
  isLoading: boolean;     // Loading state
  isPrimary?: boolean;    // Primary button style (defaults to true)
  class?: string;         // Custom class for styling
}

const props = withDefaults(defineProps<Props>(), {
  loadingText: '',
  isPrimary: true,
  class: ''
});

// Emit click event
const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>();

// Computed loading text
const buttonText = computed(() => {
  if (props.isLoading) {
    return props.loadingText || `${props.text}ing...`;
  }
  return props.text;
});

// Handle button click
function handleClick(event: MouseEvent) {
  if (!props.isLoading) {
    emit('click', event);
  }
}

// Computed classes
const buttonClasses = computed(() => {
  // Base classes for all buttons
  const baseClasses = 'inline-flex items-center justify-center';
  
  // Note: Opacity and cursor for the disabled state are now handled by CSS
  
  if (props.class) {
    // Custom styling 
    return `${baseClasses} ${props.class}`;
  }
  
  // Default styling
  const styleClasses = props.isPrimary 
    ? 'text-white bg-blue-700 hover:bg-blue-800 border border-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800'
    : 'text-gray-500 bg-white hover:bg-gray-100 border border-gray-300 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700';
  
  return `${baseClasses} font-medium rounded-lg text-sm px-5 py-2.5 text-center focus:outline-none ${styleClasses}`;
});
</script>

<template>
  <button 
    type="button"
    @click="handleClick"
    :disabled="isLoading"
    :class="buttonClasses"
  >
    <div class="flex items-center justify-center">
      <IconLoader2 v-if="isLoading" class="animate-spin w-4 h-4 mr-2" />
      <span><slot>{{ buttonText }}</slot></span>
    </div>
  </button>
</template>

<style>
/* 
  Generic approach using pointer-events to prevent hover styles 
  from applying when the button is disabled (loading).
*/
button:disabled {
  opacity: 0.75 !important;
  cursor: not-allowed !important;
  pointer-events: none; /* Prevent :hover state and styles */
}
</style> 