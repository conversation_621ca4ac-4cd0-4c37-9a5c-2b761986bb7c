<script setup lang="ts">
import { ref, watch, unref } from 'vue';
import { useEditor, EditorContent, BubbleMenu } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import { Highlight } from '@tiptap/extension-highlight';
import { Placeholder } from '@tiptap/extension-placeholder';
import { Markdown } from 'tiptap-markdown';
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
// @ts-ignore
import IconCopy from '@tabler/icons-vue/dist/esm/icons/IconCopy.mjs';
// @ts-ignore
import IconLockOpen2 from '@tabler/icons-vue/dist/esm/icons/IconLockOpen2.mjs';
// @ts-ignore
import IconLock from '@tabler/icons-vue/dist/esm/icons/IconLock.mjs';
// @ts-ignore
import IconEraser from '@tabler/icons-vue/dist/esm/icons/IconEraser.mjs';

const emit = defineEmits(['highlight', 'select']);

const props = withDefaults(defineProps<{
  text?: string,
  offset?: number,
  selectedText?: string,
  placeholder?: string,
  cssClasses?: string,
  focus?: boolean,
  showOptions?: boolean,
  showBubbleMenu?: boolean,
  editable?: boolean,
  enableMarkdown?: boolean,
}>(), {
  text: '',
  showOptions: true,
  showBubbleMenu: true,
  editable: true,
  enableMarkdown: false,
});

const lock = ref(false);

watch(lock, () => {
  if (!props.editable) return;
  if (lock.value) {
    editor.value?.setEditable(false);
  } else {
    editor.value?.setEditable(true);
  }
});

const SingleHighlight = Highlight.extend({
  addKeyboardShortcuts() {
    return {
      ...this.parent?.(),
      'Mod-Shift-h': () => {
        if (props.showBubbleMenu) {
          handleLookup();
        }
        return true;
      },
    }
  },
});

// Function to clear all highlights
const clearAllCurrentHighlights = () => {
  if (!editor.value) return;
  
  editor.value.chain().focus().unsetHighlight().run();
};

// Highlight handler
const handleLookup = (event?: any, shouldEmit = true) => {
  if (!editor.value) return;

  const { from, to } = editor.value.state.selection;

  // Clear all highlights (assuming your mark's name is 'highlight')
  clearAllCurrentHighlights();

  // Apply new highlight
  editor.value.commands.setHighlight();

  // Deselect text and close bubble menu
  // editor.value.commands.blur();
  editor.value.commands.setTextSelection(to);
  // get the selected text
  const selectedText = editor.value.state.doc.textBetween(from, to);

  if (shouldEmit) {
    emit('highlight', {
      selectedText,
      offset: from - 1,
      text: editor.value.getText(),
    });
  }
}

const editor = useEditor({
  // content: "",
  editable: props.editable,
  extensions: [
    StarterKit,
    SingleHighlight,
    ...(props.enableMarkdown ? [Markdown] : []),
    Placeholder.configure({
      placeholder: props.placeholder ?? 'Paste or type your passage here...',
    })
  ],
  editorProps: {
    attributes: {
      class: props.cssClasses
        ?? 'tiptap prose prose-sm sm:prose-base lg:prose-lg xl:prose-2xl m-5 focus:outline-none w-full min-h-[150px] max-h-[400px] overflow-auto border border-gray-200 rounded-md p-4',
    },
  },
  onCreate: ({ editor }) => {
    watch(() => props.focus, () => {
      if (props.focus) {
        editor.commands.focus();
      }
    });

    watch([() => props.text, () => props.selectedText, () => props.offset], () => {
      editor.commands.setContent(props.text, undefined, { preserveWhitespace: 'full' });
      if (props.offset !== undefined && props.selectedText) { // offset could be 0
        editor.commands.setTextSelection({ from: props.offset + 1, to: props.offset + 1 + props.selectedText.length });
        handleLookup(undefined, false);
      }
    }, { immediate: true });

    editor.on('selectionUpdate', (params) => {
      if (params.transaction.selection) {
        const selectedText = editor.state.doc.textBetween(
          params.transaction.selection.from,
          params.transaction.selection.to,
        );
        const text = selectedText ? editor.getText() : '';
        emit('select', {
          selectedText,
          offset: params.transaction.selection.from - 1,
          text,
        });
      }
    });

    // editor.on('blur', () => {
    //   emit('select', {
    //     selectedText: '',
    //     offset: 0,
    //     text: editor.getText(),
    //   });
    // });
  },
});

onBeforeUnmount(() => {
  unref(editor)!.destroy();
});

function handleCopy() {
  if (!editor.value) return;

  const { from, to } = editor.value.state.selection;
  const selectedText = editor.value.state.doc.textBetween(from, to);

  navigator.clipboard.writeText(selectedText);
  editor.value.commands.setTextSelection(to);
}

function clearEditorContent() {
  editor.value?.commands.setContent('');
  lock.value = false;
}

</script>

<template>
  <div>
    <BubbleMenu v-if="editor && showBubbleMenu" :editor="editor" :tippy-options="{ duration: 100 }"
      :should-show="({ state }: any) => { return !state.selection.empty; }">
      <div class="bg-white border rounded shadow-lg flex text-sm">
        <button @click="handleLookup" class="px-2 py-1 hover:bg-gray-100 text-left flex items-center">
          <IconSearch class="w-3 mr-1"></IconSearch>
          <span>Lookup</span>
        </button>
        <button @click="handleCopy" class="px-2 py-1 hover:bg-gray-100 text-left flex items-center">
          <IconCopy class="w-3 mr-1"></IconCopy>
          <span>Copy</span>
        </button>
      </div>
    </BubbleMenu>
    <div v-if="showOptions" class="text-xs text-gray-500 mb-1 flex">
      <div class="ml-auto flex items-center">
        <button @click="lock = !lock">
          <div v-if="!lock" class="flex items-center">
            <IconLockOpen2 class="w-3 mr-1"></IconLockOpen2>
            Lock
          </div>
          <div v-else class="flex items-center">
            <IconLock class="w-3 mr-1"></IconLock>
            Unlock
          </div>
        </button>
        <button @click="clearEditorContent" class="ml-2 flex items-center">
          <IconEraser class="w-3 mr-1"></IconEraser>
          Clear
        </button>
      </div>
    </div>
    <EditorContent :editor="editor" />
  </div>
</template>

<style>
/* light */
.tiptap {
  color-scheme: light;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  margin: 0;
  color: #1f2328;
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
}

.tiptap .octicon {
  display: inline-block;
  fill: currentColor;
  vertical-align: text-bottom;
}

.tiptap details,
.tiptap figcaption,
.tiptap figure {
  display: block;
}

.tiptap summary {
  display: list-item;
}

.tiptap [hidden] {
  display: none !important;
}

.tiptap a {
  background-color: transparent;
  color: #0969da;
  text-decoration: none;
}

.tiptap abbr[title] {
  border-bottom: none;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

.tiptap b,
.tiptap strong {
  font-weight: 600;
}

.tiptap dfn {
  font-style: italic;
}

.tiptap h1 {
  margin: .67em 0;
  font-weight: 600;
  padding-bottom: .3em;
  font-size: 2em;
  border-bottom: 1px solid #d1d9e0b3;
}

.tiptap mark {
  background-color: #fff8c5;
  color: #1f2328;
}

.tiptap small {
  font-size: 90%;
}

.tiptap sub,
.tiptap sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.tiptap sub {
  bottom: -0.25em;
}

.tiptap sup {
  top: -0.5em;
}

.tiptap img {
  border-style: none;
  max-width: 100%;
  box-sizing: content-box;
}

.tiptap code,
.tiptap kbd,
.tiptap pre,
.tiptap samp {
  font-family: monospace;
  font-size: 1em;
}

.tiptap figure {
  margin: 1em 2.5rem;
}

.tiptap hr {
  box-sizing: content-box;
  overflow: hidden;
  background: transparent;
  border-bottom: 1px solid #d1d9e0b3;
  height: .25em;
  padding: 0;
  margin: 1.5rem 0;
  background-color: #d1d9e0;
  border: 0;
}

.tiptap input {
  font: inherit;
  margin: 0;
  overflow: visible;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.tiptap [type=button],
.tiptap [type=reset],
.tiptap [type=submit] {
  -webkit-appearance: button;
  appearance: button;
}

.tiptap [type=checkbox],
.tiptap [type=radio] {
  box-sizing: border-box;
  padding: 0;
}

.tiptap table {
  border-spacing: 0;
  border-collapse: collapse;
  display: block;
  width: max-content;
  max-width: 100%;
  overflow: auto;
  font-variant: tabular-nums;
}

.tiptap td,
.tiptap th {
  padding: 0;
}

.tiptap details summary {
  cursor: pointer;
}

.tiptap a:focus,
.tiptap [role=button]:focus,
.tiptap input[type=radio]:focus,
.tiptap input[type=checkbox]:focus {
  outline: 2px solid #0969da;
  outline-offset: -2px;
  box-shadow: none;
}

.tiptap a:focus:not(:focus-visible),
.tiptap [role=button]:focus:not(:focus-visible),
.tiptap input[type=radio]:focus:not(:focus-visible),
.tiptap input[type=checkbox]:focus:not(:focus-visible) {
  outline: solid 1px transparent;
}

.tiptap a:focus-visible,
.tiptap [role=button]:focus-visible,
.tiptap input[type=radio]:focus-visible,
.tiptap input[type=checkbox]:focus-visible {
  outline: 2px solid #0969da;
  outline-offset: -2px;
  box-shadow: none;
}

.tiptap a:not([class]):focus,
.tiptap a:not([class]):focus-visible,
.tiptap input[type=radio]:focus,
.tiptap input[type=radio]:focus-visible,
.tiptap input[type=checkbox]:focus,
.tiptap input[type=checkbox]:focus-visible {
  outline-offset: 0;
}

.tiptap kbd {
  display: inline-block;
  padding: 0.25rem;
  font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  line-height: 10px;
  color: #1f2328;
  vertical-align: middle;
  background-color: #f6f8fa;
  border: solid 1px #d1d9e0b3;
  border-bottom-color: #d1d9e0b3;
  border-radius: 6px;
  box-shadow: inset 0 -1px 0 #d1d9e0b3;
}

.tiptap h1,
.tiptap h2,
.tiptap h3,
.tiptap h4,
.tiptap h5,
.tiptap h6 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.25;
}

.tiptap h2 {
  font-weight: 600;
  padding-bottom: .3em;
  font-size: 1.5em;
  border-bottom: 1px solid #d1d9e0b3;
}

.tiptap h3 {
  font-weight: 600;
  font-size: 1.25em;
}

.tiptap h4 {
  font-weight: 600;
  font-size: 1em;
}

.tiptap h5 {
  font-weight: 600;
  font-size: .875em;
}

.tiptap h6 {
  font-weight: 600;
  font-size: .85em;
  color: #59636e;
}

.tiptap p {
  margin-top: 0;
  margin-bottom: 10px;
}

.tiptap blockquote {
  margin: 0;
  padding: 0 1em;
  color: #59636e;
  border-left: .25em solid #d1d9e0;
}

.tiptap ul,
.tiptap ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 2em;
  list-style: revert;
}

.tiptap ol ol,
.tiptap ul ol {
  list-style-type: lower-roman;
}

.tiptap ul ul ol,
.tiptap ul ol ol,
.tiptap ol ul ol,
.tiptap ol ol ol {
  list-style-type: lower-alpha;
}

.tiptap dd {
  margin-left: 0;
}

.tiptap tt,
.tiptap code,
.tiptap samp {
  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  font-size: 12px;
}

.tiptap pre {
  margin-top: 0;
  margin-bottom: 0;
  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  font-size: 12px;
  word-wrap: normal;
}

.tiptap .octicon {
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.tiptap input::-webkit-outer-spin-button,
.tiptap input::-webkit-inner-spin-button {
  margin: 0;
  appearance: none;
}

.tiptap .mr-2 {
  margin-right: 0.5rem !important;
}

.tiptap::before {
  display: table;
  content: "";
}

.tiptap::after {
  display: table;
  clear: both;
  content: "";
}

.tiptap>*:first-child {
  margin-top: 0 !important;
}

.tiptap>*:last-child {
  margin-bottom: 0 !important;
}

.tiptap a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.tiptap h1:hover .anchor,
.tiptap h2:hover .anchor,
.tiptap h3:hover .anchor,
.tiptap h4:hover .anchor,
.tiptap h5:hover .anchor,
.tiptap h6:hover .anchor {
  text-decoration: none;
}

.tiptap h1:hover .anchor .octicon-link,
.tiptap h2:hover .anchor .octicon-link,
.tiptap h3:hover .anchor .octicon-link,
.tiptap h4:hover .anchor .octicon-link,
.tiptap h5:hover .anchor .octicon-link,
.tiptap h6:hover .anchor .octicon-link {
  visibility: visible;
}

.tiptap h1 tt,
.tiptap h1 code,
.tiptap h2 tt,
.tiptap h2 code,
.tiptap h3 tt,
.tiptap h3 code,
.tiptap h4 tt,
.tiptap h4 code,
.tiptap h5 tt,
.tiptap h5 code,
.tiptap h6 tt,
.tiptap h6 code {
  padding: 0 .2em;
  font-size: inherit;
}

.tiptap summary h1,
.tiptap summary h2,
.tiptap summary h3,
.tiptap summary h4,
.tiptap summary h5,
.tiptap summary h6 {
  display: inline-block;
}

.tiptap summary h1 .anchor,
.tiptap summary h2 .anchor,
.tiptap summary h3 .anchor,
.tiptap summary h4 .anchor,
.tiptap summary h5 .anchor,
.tiptap summary h6 .anchor {
  margin-left: -40px;
}

.tiptap summary h1,
.tiptap summary h2 {
  padding-bottom: 0;
  border-bottom: 0;
}

.tiptap ul.no-list,
.tiptap ol.no-list {
  padding: 0;
  list-style-type: none;
}

.tiptap ol[type="a s"] {
  list-style-type: lower-alpha;
}

.tiptap ol[type="A s"] {
  list-style-type: upper-alpha;
}

.tiptap ol[type="i s"] {
  list-style-type: lower-roman;
}

.tiptap ol[type="I s"] {
  list-style-type: upper-roman;
}

.tiptap table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.tiptap table img {
  background-color: transparent;
}

.tiptap img[align=right] {
  padding-left: 20px;
}

.tiptap img[align=left] {
  padding-right: 20px;
}

.tiptap .emoji {
  max-width: none;
  vertical-align: text-top;
  background-color: transparent;
}

.tiptap span.frame {
  display: block;
  overflow: hidden;
}

.tiptap span.frame>span {
  display: block;
  float: left;
  width: auto;
  padding: 7px;
  margin: 13px 0 0;
  overflow: hidden;
  border: 1px solid #d1d9e0;
}

.tiptap span.frame span img {
  display: block;
  float: left;
}

.tiptap span.frame span span {
  display: block;
  padding: 5px 0 0;
  clear: both;
  color: #1f2328;
}

.tiptap span.align-center {
  display: block;
  overflow: hidden;
  clear: both;
}

.tiptap code,
.tiptap tt {
  padding: .2em .4em;
  margin: 0;
  font-size: 85%;
  white-space: break-spaces;
  background-color: #818b981f;
  border-radius: 6px;
}

.tiptap code br,
.tiptap tt br {
  display: none;
}

.tiptap del code {
  text-decoration: inherit;
}

.tiptap samp {
  font-size: 85%;
}

.tiptap pre code {
  font-size: 100%;
}

.tiptap pre>code {
  padding: 0;
  margin: 0;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.tiptap .highlight {
  margin-bottom: 1rem;
}

.tiptap .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.tiptap [data-footnote-ref]::before {
  content: "[";
}

.tiptap [data-footnote-ref]::after {
  content: "]";
}

.tiptap .footnotes {
  font-size: 12px;
  color: #59636e;
  border-top: 1px solid #d1d9e0;
}

.tiptap .footnotes ol {
  padding-left: 1rem;
}

.tiptap .footnotes ol ul {
  display: inline-block;
  padding-left: 1rem;
  margin-top: 1rem;
}

.tiptap .footnotes li {
  position: relative;
}

.tiptap .footnotes li:target::before {
  position: absolute;
  top: calc(0.5rem*-1);
  right: calc(0.5rem*-1);
  bottom: calc(0.5rem*-1);
  left: calc(1.5rem*-1);
  pointer-events: none;
  content: "";
  border: 2px solid #0969da;
  border-radius: 6px;
}

.tiptap .footnotes li:target {
  color: #1f2328;
}

.tiptap .footnotes .data-footnote-backref g-emoji {
  font-family: monospace;
}

.tiptap body:has(:modal) {
  padding-right: var(--dialog-scrollgutter) !important;
}

.tiptap .pl-c {
  color: #59636e;
}

.tiptap .pl-c1,
.tiptap .pl-s .pl-v {
  color: #0550ae;
}

.tiptap .pl-e,
.tiptap .pl-en {
  color: #6639ba;
}

.tiptap .pl-smi,
.tiptap .pl-s .pl-s1 {
  color: #1f2328;
}

.tiptap .pl-ent {
  color: #0550ae;
}

.tiptap .pl-k {
  color: #cf222e;
}

.tiptap .pl-s,
.tiptap .pl-pds,
.tiptap .pl-s .pl-pse .pl-s1,
.tiptap .pl-sr,
.tiptap .pl-sr .pl-cce,
.tiptap .pl-sr .pl-sre,
.tiptap .pl-sr .pl-sra {
  color: #0a3069;
}

.tiptap .pl-v,
.tiptap .pl-smw {
  color: #953800;
}

.tiptap .pl-bu {
  color: #82071e;
}

.tiptap .pl-ii {
  color: #f6f8fa;
  background-color: #82071e;
}

.tiptap .pl-c2 {
  color: #f6f8fa;
  background-color: #cf222e;
}

.tiptap [role=button]:focus:not(:focus-visible),
.tiptap [role=tabpanel][tabindex="0"]:focus:not(:focus-visible),
.tiptap button:focus:not(:focus-visible),
.tiptap summary:focus:not(:focus-visible),
.tiptap a:focus:not(:focus-visible) {
  outline: none;
  box-shadow: none;
}

.tiptap [tabindex="0"]:focus:not(:focus-visible),
.tiptap details-dialog:focus:not(:focus-visible) {
  outline: none;
}

.tiptap g-emoji {
  display: inline-block;
  min-width: 1ch;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1em;
  font-style: normal !important;
  font-weight: 400;
  line-height: 1;
  vertical-align: -0.075em;
}

.tiptap g-emoji img {
  width: 1em;
  height: 1em;
}

.tiptap .task-list-item {
  list-style-type: none;
}

.tiptap .markdown-alert.markdown-alert-warning {
  border-left-color: #9a6700;
}

.tiptap .markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: #9a6700;
}

.tiptap .markdown-alert.markdown-alert-tip {
  border-left-color: #1a7f37;
}

.tiptap .markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: #1a7f37;
}

.tiptap .markdown-alert.markdown-alert-caution {
  border-left-color: #cf222e;
}

.tiptap .markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: #d1242f;
}

.tiptap>*:first-child>.heading-element:first-child {
  margin-top: 0 !important;
}

.tiptap .highlight pre:has(+.zeroclipboard-container) {
  min-height: 52px;
}
</style>
