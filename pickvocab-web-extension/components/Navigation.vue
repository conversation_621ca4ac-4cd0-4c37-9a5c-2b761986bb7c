<script lang="ts" setup>
// @ts-ignore
import IconLogin2 from '@tabler/icons-vue/dist/esm/icons/IconLogin2.mjs';
// @ts-ignore
import IconSettings from '@tabler/icons-vue/dist/esm/icons/IconSettings.mjs';
// @ts-ignore
import IconLogout2 from '@tabler/icons-vue/dist/esm/icons/IconLogout2.mjs';
import { sendMessage } from 'webext-bridge/popup';
import { Separator } from '@/components/ui/separator';
import { useAuthStore } from '@/entrypoints/popup/stores/auth';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';

const router = useRouter();
const authStore = useAuthStore();

let { user } = storeToRefs(authStore);

function openOptionsPage() {
  browser.runtime.openOptionsPage();
}

async function signIn() {
  await sendMessage('googleSignIn', {}, 'background') as string;
  // reload new user data from storage
  authStore.isHydrated = false;
  await authStore.hydrate();
}

async function signOut() {
  await authStore.signOut();
  router.push({ name: 'Lookup' });
}
</script>

<template>
  <div>
    <div class="px-4 h-14 flex items-center">
      <div class="self-center text-xl text-gray-700 font-semibold sm:text-2xl whitespace-nowrap dark:text-white">pickvocab</div>
      <div class="ml-auto flex items-center">
        <button @click="openOptionsPage">
          <icon-settings class="inline-block w-5 h-5 mr-2"></icon-settings>
        </button>
        <button
          v-if="!authStore.isAuthenticated"
          @click="signIn"
          class="flex items-center">
          <icon-login-2 class="inline-block w-5 h-5 mr-2"></icon-login-2>
          <p class="text-xs">Sign in</p>
        </button>
        <DropdownMenu v-else>
            <DropdownMenuTrigger>
            <button type="button"
              class="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
              aria-expanded="false">
              <span class="sr-only">Open user menu</span>
              <img class="w-6 h-6 rounded-full" :src="getProfilePicture(user!)" alt="user photo">
            </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>
                <p class="text-sm text-gray-900 dark:text-white" role="none">
                  {{ getProfileName(user!) }}
                </p>
                <p class="text-sm font-medium text-gray-900 truncate dark:text-gray-300" role="none">
                  {{ user?.email }}
                </p>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem class="cursor-pointer" @click="signOut()">
                <icon-logout-2 class="inline-block w-5 h-5"></icon-logout-2>
                <span class="">Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
      </div>
    </div>
    <Separator></Separator>
  </div>
</template>

<style scoped></style>
