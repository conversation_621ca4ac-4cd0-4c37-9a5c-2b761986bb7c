<script setup lang="ts">
import { ref } from 'vue'
import { ComboboxAnchor, ComboboxContent, ComboboxEmpty, ComboboxGroup, ComboboxInput, ComboboxItem, ComboboxItemIndicator, ComboboxLabel, ComboboxRoot, ComboboxSeparator, ComboboxTrigger, ComboboxViewport } from 'radix-vue'
import { uniq } from 'lodash-es';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconChevronDown from '@tabler/icons-vue/dist/esm/icons/IconChevronDown.mjs';
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
import { watchDebounced } from '@vueuse/core';
import { RemoteDictionaryApi } from '@/api/dictionary/remote';

const props = defineProps<{
  placeholder?: string,
}>();

const value = defineModel<string>('value');
const typingValue = ref('');

const emit = defineEmits(['submit']);

const options: Ref<string[]> = ref([]);

function handleInput(event: Event) {
  typingValue.value = (event.target as HTMLInputElement).value;
  if (typingValue.value) {
    options.value = [typingValue.value];
  }
}

let currentController: AbortController | undefined = undefined;

watchDebounced(typingValue, async () => {
  if (typingValue.value === '') {
    options.value = [];
  } else {
    if (currentController) {
      currentController.abort();
    }
    currentController = new AbortController();

    const remoteApi = new RemoteDictionaryApi();
    const suggestedWords = await remoteApi.search(typingValue.value, currentController.signal);
    options.value = uniq([typingValue.value, ...suggestedWords]);
  }
}, { debounce: 300 });

function submit() {
  value.value = typingValue.value;
  emit('submit', typingValue.value);
}
</script>

<template>
  <div class="flex">
    <ComboboxRoot v-model="value" class="relative w-full h-full">
      <ComboboxAnchor
        class="border border-gray-300 min-w-[160px] w-full h-full inline-flex items-center justify-between rounded px-[15px] text-[13px] leading-none gap-[5px] bg-white text-blue-500 data-[placeholder]:text-grass9 outline-none">
        <ComboboxInput
          class="w-full text-sm !bg-transparent outline-none text-gray-700 h-full selection:bg-blue-200 placeholder-mauve8"
          :placeholder="props.placeholder" @input="handleInput" :auto-focus="true" />
        <!-- <ComboboxTrigger>
          <icon-chevron-down class="h-4 w-4 text-blue-500" />
        </ComboboxTrigger> -->
      </ComboboxAnchor>

      <ComboboxContent
        class="absolute z-10 w-full mt-2 min-w-[160px] w-full bg-white overflow-hidden rounded border border-gray-200 will-change-[opacity,transform] data-[side=top]:animate-slideDownAndFade data-[side=right]:animate-slideLeftAndFade data-[side=bottom]:animate-slideUpAndFade data-[side=left]:animate-slideRightAndFade">
        <ComboboxViewport class="p-[5px]">
          <ComboboxEmpty class="text-mauve8 text-xs font-medium text-center py-2" />

          <ComboboxGroup>
            <!-- <ComboboxLabel class="px-[25px] text-xs leading-[25px] text-mauve11">
              Suggestions
            </ComboboxLabel> -->

            <ComboboxItem v-for="(option) in options" :key="option"
              class="text-[13px] leading-none text-blue-500 rounded-[3px] flex items-center h-[25px] pr-[35px] pl-[10px] relative select-none data-[disabled]:text-mauve8 data-[disabled]:pointer-events-none data-[highlighted]:outline-none data-[highlighted]:bg-blue-400 data-[highlighted]:text-white"
              :value="option">
              <ComboboxItemIndicator class="absolute left-0 w-[25px] inline-flex items-center justify-center">
                <icon-check class="w-4 h-4"></icon-check>
              </ComboboxItemIndicator>
              <span>
                {{ option }}
              </span>
            </ComboboxItem>
            <ComboboxSeparator class="h-[1px] bg-blue-200 m-[5px]" />
          </ComboboxGroup>
        </ComboboxViewport>
      </ComboboxContent>
    </ComboboxRoot>

    <button
      class="p-2.5 ms-2 text-sm cursor-pointer font-medium text-white bg-blue-700 rounded-lg border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
      @click="submit">
      <icon-search class="w-4 h-4"></icon-search>
    </button>
  </div>
</template>

<style scoped></style>
