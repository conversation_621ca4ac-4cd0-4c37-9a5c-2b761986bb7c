<script lang="ts" setup>
// @ts-ignore
import IconReload from '@tabler/icons-vue/dist/esm/icons/IconReload.mjs';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
// @ts-ignore
import IconDotsVertical from '@tabler/icons-vue/dist/esm/icons/IconDotsVertical.mjs';
// @ts-ignore
import IconScale from '@tabler/icons-vue/dist/esm/icons/IconScale.mjs';
// @ts-ignore
import IconBookmark from '@tabler/icons-vue/dist/esm/icons/IconBookmark.mjs';
import { DefinitionDetails, Dictionary, DictionarySource, PartOfSpeech, WordEntry } from 'pickvocab-dictionary';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  SelectScrollUpButton,
  SelectScrollDownButton,
} from '@/components/ui/select';
import { SelectViewport } from 'radix-vue';
import SearchInput from './SearchInput.vue';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { useLLMStore } from '@/entrypoints/popup/stores/llm';
import { languages } from '@/utils/languages';
import { stylesForPartOfSpeech } from '@/utils/utils';
import { RemoteDictionaryApi } from '@/api/dictionary/remote';
import Spinner from '../Spinner.vue';
import DictionaryWordViewInfoAlert from './DictionaryWordViewInfoAlert.vue';
import DictionaryWordViewErrorAlert from './DictionaryWordViewErrorAlert.vue';
import { sendMessage } from 'webext-bridge/popup';
import { useAuthStore } from '@/entrypoints/popup/stores/auth';
import { useAppStore } from '@/entrypoints/popup/stores/app';
import { SubmitButton } from '@/components/ui/submit-button';

const llmStore = useLLMStore();
const authStore = useAuthStore();
const appStore = useAppStore();
appStore.hydrate();

const isMac = computed(() => {
  // Check for macOS using userAgent string
  return /Mac|iPhone|iPad|iPod/i.test(navigator.userAgent);
});

const word = ref('');
const wordEntry: Ref<WordEntry | undefined> = ref();
const selectedLanguage = ref<string | undefined>('English');
const isDefinitionForLanguageLoading = ref(false);
const isMoreExamplesLoading = ref(false);
const isMoreSynonymsLoading = ref(false);
const errorMessage = ref('');
const fromCache = ref(true);
const wordDefinitionElement = useTemplateRef('word-definition-element');
const isScrolled = ref(false);
const showReloadMessage = ref(false);
const isAddCardLoading = ref(false);
const isSignInLoading = ref(false);

const handleSettingChange = () => {
  showReloadMessage.value = true;
};

const llmModel = computed(() => {
  return wordEntry.value ? llmStore.getModelById(wordEntry.value?.llm_model) : undefined;
});

const dictionary = computed(() => {
  let sources: DictionarySource[] = [llmStore.pickvocabDictionarySource];
  if (llmStore.activeUserModel) {
    sources = [llmStore.createDictionarySource(llmStore.activeUserModel), ...sources];
  }
  const dictionary = new Dictionary(sources);
  return dictionary;
});

watch([word, selectedLanguage], (newValues, oldValues) => {
  // newValues and oldValues will be arrays in the order of your dependencies
  if (word.value) {
    if (newValues.some((val, index) => val !== (oldValues as any)[index])) {
      resetAndLookupWord(word.value, false, selectedLanguage.value);
    }
  }
}, { immediate: true }); // Only if you truly need the immediate first call

watch(wordEntry, () => {
  if (wordEntry.value !== undefined) {
    if (!isScrolled.value) {
      nextTick(() => {
        (wordDefinitionElement.value as any).scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      });
      isScrolled.value = true;
    }
  } else {
    isScrolled.value = false;
  }
});

function resetScroll() {
  const parent = document.getElementById('scrollable-container');
  parent?.scrollTo(0, 0);
}

async function resetAndLookupWord(text: string, fresh = false, language?: string) {
  wordEntry.value = undefined;
  // word.value = text;
  resetScroll();
  return lookupWord(text, fresh, language);
}

async function listAllMeanings(word: string, fresh = false): Promise<{ word: WordEntry, changed: boolean }> {
  const api = new RemoteDictionaryApi();
  if (!fresh) {
    const words = await api.list({ word });
    if (words.length > 0) return { word: words[0], changed: false };
  }
  fromCache.value = false;
  const wordResult = await dictionary.value.listAllMeanings(word);
  const created = await api.create(wordResult.word);
  return { word: created, changed: wordResult.changed };
}

async function lookupWordForLanguage(language?: string, fresh = false) {
  if (!language || language === 'English') return;
  if (!wordEntry.value) throw new Error('Word entry is undefined');

  try {
    if (fresh) {
      wordEntry.value.definitions.forEach((definition) => {
        delete definition.languages?.[language];
      });
    }

    isDefinitionForLanguageLoading.value = true;
    const { word: entryWithDefinitionForLanguage, changed } = await dictionary.value.listAllMeaningsForLanguage(toRaw(wordEntry.value), language);
    wordEntry.value = { ...toRaw(wordEntry.value!), ...entryWithDefinitionForLanguage };

    if (changed) {
      const api = new RemoteDictionaryApi();
      api.put(wordEntry.value);
    }
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
  } finally {
    isDefinitionForLanguageLoading.value = false;
  }
}

async function getMoreExamples() {
  if (!wordEntry.value) throw new Error('Word entry is undefined');

  try {
    isMoreExamplesLoading.value = true;
    const { word: entryWithExamples, changed } = await dictionary.value.getMoreExamples(toRaw(wordEntry.value!));
    isMoreExamplesLoading.value = false;
    wordEntry.value = { ...toRaw(wordEntry.value!), ...entryWithExamples };

    if (changed) {
      const api = new RemoteDictionaryApi();
      api.put(wordEntry.value);
    }
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
  } finally {
    isMoreExamplesLoading.value = false;
  }
}

async function getMoreSynonyms() {
  if (!wordEntry.value) throw new Error('Word entry is undefined');

  try {
    isMoreSynonymsLoading.value = true;
    const { word: entryWithSynonyms, changed } = await dictionary.value.getMoreSynonyms(toRaw(wordEntry.value))
    isMoreSynonymsLoading.value = false;
    wordEntry.value = { ...toRaw(wordEntry.value!), ...entryWithSynonyms };

    if (changed) {
      const api = new RemoteDictionaryApi();
      api.put(wordEntry.value);
    }
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
  } finally {
    isMoreSynonymsLoading.value = false;
  }
}

async function lookupWord(word: string, fresh = false, language?: string) {
  errorMessage.value = '';

  try {
    wordEntry.value = (await listAllMeanings(word, fresh)).word;

    lookupWordForLanguage(language);
    getMoreExamples();
    getMoreSynonyms();
  } catch (err) {
    errorMessage.value = `${err}`;
  }
}

function renderDefinitionForLanguage(definition: DefinitionDetails, language: string) {
  return definition.languages![language].word ?
    `${definition.languages![language].word} - ${definition.languages![language].definition}`
    : definition.languages![language].definition;
}

async function refresh() {
  if (word.value) {
    if (selectedLanguage.value !== 'English') {
      await lookupWordForLanguage(selectedLanguage.value, true);
    } else {
      await resetAndLookupWord(word.value, true, selectedLanguage.value);
    }
  }
}

const showInfoMessage = computed(() => {
  return !fromCache.value && llmStore.shouldShowAPIKeyAlert();
});

function setupApiKey() {
  browser.runtime.openOptionsPage();
}

async function hideApiKeyAlert() {
  llmStore.lastShowAPIKeyAlert = Date.now();
}

// --- Add card dialog ---
const openAddCardDialog = ref(false);
const defIdx = ref(0);
const partOfSpeech = ref('noun');
const definition = ref('');
const pickDefinitionToggle = ref(false);
const examples: Ref<string[]> = ref(['']);
const isShowAddDecks = ref(false);
const searchDeckText = ref('');
const selectedDecks = ref<Deck[]>([]);
const deckSelectedIdx = ref(0);
let suggestedDecks: Ref<Deck[]> = ref([]);

const addCardDialogCurrentView: Ref<'editCard' | 'addCardResult'> = ref('editCard');
const addCardError = ref('');
const cardUrl: Ref<string | undefined> = ref(undefined);

watch([wordEntry, defIdx], () => {
  definition.value = wordEntry.value?.definitions[defIdx.value].definition || '';
  partOfSpeech.value = wordEntry.value?.definitions[defIdx.value].partOfSpeech || 'noun';
});

watch(openAddCardDialog, async () => {
  if (openAddCardDialog.value) {
    suggestedDecks.value = await sendMessage(
      'deck:search',
      { text: searchDeckText.value },
      'background'
    ) as any as Deck[];
  }
});

function togglePickDefinition() {
  pickDefinitionToggle.value = !pickDefinitionToggle.value;
}

function addMoreExample() {
  // const idx = currentExamples.value.length;
  // if (get(currentAddWord.value, `definitions[0].examples[${idx}]`)) {
  //   currentExamples.value.push(currentAddWord.value!.definitions[0].examples![idx]);
  // } else {
  //   currentExamples.value.push('');
  // }
  examples.value.push('');
}

function removeDeck(idx: number) { // selectedDeck idx
  if (idx >= 0) {
    selectedDecks.value.splice(idx, 1);
  }
}

function isInSelectedDecks(deck: Deck) {
  return selectedDecks.value.find((d) => d.id === deck.id);
}

function addSelectedDeck(idx: number) { // suggestedDecks idx
  const selectedDeckIdx = selectedDecks.value.findIndex((deck) => deck.id === suggestedDecks.value[idx].id);
  if (selectedDeckIdx !== -1) {
    removeDeck(selectedDeckIdx);
    return;
  }
  selectedDecks.value.push(suggestedDecks.value[idx]);
}

async function addCard() {
  isAddCardLoading.value = true;
  
  const baseCard: BaseDefinitionCard = {
    word: word.value,
    definition: {
      definition: definition.value ? definition.value : undefined,
      examples: examples.value
        .concat(wordEntry.value?.definitions[defIdx.value].examples || [])
        .filter((e) => e), // filter empty value ''
      languages: wordEntry.value?.definitions[defIdx.value].languages,
      context: wordEntry.value?.definitions[defIdx.value].context,
      partOfSpeech: partOfSpeech.value
    },
    // @ts-ignore
    referenceWord: toRaw(wordEntry.value),
    refDefIdx: defIdx.value,
  };
  try {
    const card = (await sendMessage(
      "definitionCard:create",
      { card: baseCard as any },
      "background"
    )) as any as WordEntry;
    const addPromises = selectedDecks.value.map((deck) => sendMessage(
      "deck:addCard",
      { deckId: deck.id, cardId: card.id },
      "background"
    ));
    await Promise.allSettled(addPromises);
    // openAddCardDialog.value = false;
    cardUrl.value = `${import.meta.env.WXT_CLIENT_API_URL}/app/cards/${card.id}`;
    addCardDialogCurrentView.value = 'addCardResult';
  } catch (err) {
    console.log(err);
    addCardError.value = `${err}`;
    addCardDialogCurrentView.value = 'addCardResult';
  } finally {
    isAddCardLoading.value = false;
  }
}

function goBackToEditCardDialog() {
  addCardError.value = '';
  addCardDialogCurrentView.value = 'editCard';
}

async function signIn() {
  await sendMessage('googleSignIn', {}, 'background') as string;
  // reload new user data from storage
  authStore.isHydrated = false;
  await authStore.hydrate();
}

async function addCardSignIn() {
  isSignInLoading.value = true;
  try {
    await signIn();
    await addCard();
  } finally {
    isSignInLoading.value = false;
  }
}

function showAddCardModal(idx: number) {
  defIdx.value = idx;
  openAddCardDialog.value = true;
}
// -----------------------
</script>

<template>
  <div class="w-full">
    <div class="mt-24 flex flex-col items-center justify-center">
      <div class="mb-8 text-4xl font-extrabold tracking-tight leading-none text-gray-900 md:text-5xl lg:text-6xl">
        AI-powered English Dictionary
      </div>
      <SearchInput class="w-[400px] h-[38px] mb-4" v-model:value="word"
        :placeholder="'Lookup any word, idiom or phrase...'"
        @submit="(text: string) => { word = text; }"/>
      <div class="flex flex-col items-center space-y-2">
        <div class="flex items-center space-x-2">
          <Checkbox 
            id="floating-lookup" 
            v-model:checked="appStore.enableFloatingLookupButton" 
            @update:checked="handleSettingChange"
            class="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600 focus-visible:ring-blue-600"
          />
          <label for="floating-lookup" class="text-gray-600 cursor-pointer">Show floating lookup button</label>
        </div>
        <span v-if="!appStore.enableFloatingLookupButton" class="text-gray-400">Tip: Highlight text, right-click or press {{ isMac ? '⌘' : 'Ctrl' }}+Shift+H to look it up!</span>
      </div>
      <div v-if="showReloadMessage" class="mt-2 p-3 bg-blue-50 text-blue-700 rounded-lg text-sm max-w-[400px]">
        Please reload your tabs or restart your browser for the changes to take effect
      </div>
      <div v-if="word && wordEntry === undefined && !errorMessage" class="mt-8 flex items-center justify-center h-full">
        <Spinner 
          :size="6" 
          :show-shortcut-tip="true" 
          :shortcut-tip="isMac ? 'Cmd+Shift+K' : 'Ctrl+Shift+K'"
          shortcut-message="to quickly open extension popup"
        />
      </div>
    </div>

    <div ref="word-definition-element" class="mt-10 invisible">&nbsp;</div>

    <div class="px-8">
      <DictionaryWordViewInfoAlert v-if="wordEntry && showInfoMessage" class="mb-4" @setup="setupApiKey()"
        @dismiss="hideApiKeyAlert()">
      </DictionaryWordViewInfoAlert>
      <DictionaryWordViewErrorAlert class="mb-4"
        v-if="errorMessage"
        @retry="refresh()"
        @setup="setupApiKey()"
        :message="errorMessage"
        :is-active-user-model="llmStore.activeUserModel ? true : false" />
    </div>

    <div v-if="wordEntry !== undefined" class="mt-10 mb-12 mx-8 py-4 px-8 border border-gray-200 rounded">
      <div class="flex items-center py-2 border-b">
        <p class="text-3xl text-gray-700 font-semibold">{{ wordEntry ? wordEntry.word : word }}</p>

        <TooltipProvider :delay-duration="100">
          <Tooltip>
            <TooltipTrigger class="ml-2">
              <icon-reload @click="refresh()" stroke-width="2.5"
                class="inline-block text-gray-400 w-4 h-4 cursor-pointer hover:text-blue-500"></icon-reload>
            </TooltipTrigger>
            <TooltipContent>
              <div v-if="wordEntry" class="flex items-center font-medium text-base bg-white">
                <span v-if="llmModel" class="flex items-center">
                  <img :src="llmStore.providerMap[llmModel.provider].logo" alt="llm-model-img" class="w-4 h-4">
                  <span class="ml-1">{{ llmModel.name }}&nbsp;</span>
                </span>
                <span v-if="wordEntry.createdAt">{{ new Date(wordEntry.createdAt).toLocaleString() }}</span>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <Dialog v-model:open="openAddCardDialog">
          <DialogTrigger class="ml-auto">
            <button
              class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-3 py-1.5 text-center me-2"
            >
              Save
            </button>
          </DialogTrigger>
          <DialogContent class="sm:max-w-[425px] max-h-[90dvh] overflow-y-auto">
            <template v-if="addCardDialogCurrentView === 'editCard'">
              <template v-if="authStore.isAuthenticated">
                <DialogHeader class="text-left">
                  <DialogTitle class="flex items-center">
                    <icon-cards stroke-width="2.5" class="inline-block w-5 h-5 me-2"></icon-cards>
                    <span>Create card</span>
                  </DialogTitle>
                </DialogHeader>
                <form>
                  <div class="grid gap-4 mb-4 grid-cols-2">
                    <div class="col-span-2">
                      <label for="name" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Word/Phrase</label>
                      <input type="text"
                        class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        placeholder="ex: Go down rabbit holes" required v-model="word" :disabled="wordEntry !== undefined">
                    </div>

                    <div class="max-w-[150px]">
                      <Select v-model="partOfSpeech">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent class="max-h-[150px]">
                          <SelectGroup>
                            <SelectItem value="noun">
                              <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                                :class="stylesForPartOfSpeech('noun')">
                                noun
                              </span>
                            </SelectItem>
                            <SelectItem value="verb">
                              <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                                :class="stylesForPartOfSpeech('verb')">
                                verb
                              </span>
                            </SelectItem>
                            <SelectItem value="adjective">
                              <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                                :class="stylesForPartOfSpeech('adjective')">
                                adjective
                              </span>
                            </SelectItem>
                            <SelectItem value="phrase">
                              <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                                :class="stylesForPartOfSpeech('phrase')">
                                phrase
                              </span>
                            </SelectItem>
                            <SelectItem value="adverb">
                              <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                                :class="stylesForPartOfSpeech('adverb')">
                                adverb
                              </span>
                            </SelectItem>
                            <SelectItem value="preposition">
                              <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                                :class="stylesForPartOfSpeech('preposition')">
                                preposition
                              </span>
                            </SelectItem>
                            <SelectItem value="conjunction">
                              <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                                :class="stylesForPartOfSpeech('conjunction')">
                                conjunction
                              </span>
                            </SelectItem>
                            <SelectItem value="others">
                              <span class="inline-block text-sm border rounded-xl px-2 align-middle"
                                :class="stylesForPartOfSpeech('others')">
                                others
                              </span>
                            </SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                    <div class="col-span-2">
                      <label for="description"
                        class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Definition</label>
                      <div class="flex mb-2">
                        <textarea id="description" rows="5"
                          class="block p-2.5 w-full text-sm text-gray-600 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                          v-model="definition"
                          placeholder='Metaphorically, "going down the rabbit holes" means delving deeply into a subject or exploring a complex topic or idea in great detail. It implies a willingness to explore the unknown or unconventional paths of inquiry, often leading to unexpected discoveries or insights.'></textarea>
                      </div>
                      <div>
                        <button class="flex items-center" v-if="!pickDefinitionToggle" @click="togglePickDefinition()">
                          <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="m9 5 7 7-7 7" />
                          </svg>
                          <span class="text-xs text-blue-700">Pick other definitions</span>
                        </button>
                        <button class="flex items-center" v-else @click="togglePickDefinition()">
                          <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="m19 9-7 7-7-7" />
                          </svg>
                          <span class="text-xs text-blue-700">Pick other definitions</span>
                        </button>
                        <template v-if="pickDefinitionToggle">
                          <ul class="my-4 space-y-3">
                            <li v-for="(definition, idx) in wordEntry.definitions">
                              <div
                                class="flex items-center p-3 text-sm text-gray-600 rounded-lg border-2 hover:bg-gray-50 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white cursor-pointer"
                                :class="{ 'border-blue-600': idx === defIdx, 'border-gray-200': idx !== defIdx }"
                                @click="defIdx = idx">
                                <span class="flex-1">{{ definition.definition }}</span>
                              </div>
                            </li>
                          </ul>
                        </template>
                      </div>
                    </div>
                  </div>
                  <div class="col-span-2">
                    <label for="description" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Your own
                      examples
                      (optional)</label>
                    <template v-if="examples.length > 0">
                      <template v-for="(_, idx) in examples">
                        <div class="flex mb-2">
                          <textarea id="description" rows="3"
                            class="block p-2.5 w-full text-sm text-gray-600 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            v-model="examples[idx]"></textarea>
                        </div>
                      </template>
                    </template>
                    <template v-else>
                      <div class="flex mb-2">
                        <textarea id="description" rows="3"
                          class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                          v-model="examples[0]"
                          placeholder="Have the courage to go down rabbit holes that are deeply meaningful to you, not just trendy topics."></textarea>
                      </div>
                    </template>
                    <button type="button" class="flex items-center" @click="addMoreExample()">
                      <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M5 12h14m-7 7V5" />
                      </svg>
                      <span class="text-xs text-blue-700">Add more examples</span>
                    </button>
                    <button type="button" class="mt-1 flex items-center" v-if="!isShowAddDecks" @click="isShowAddDecks = true">
                      <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M5 12h14m-7 7V5" />
                      </svg>
                      <span class="text-xs text-blue-700">Add to notebooks</span>
                    </button>
                    <div class="col-span-2 mt-4" v-if="isShowAddDecks">
                      <label for="name" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Notebooks</label>
                      <input type="text"
                        class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        required v-model="searchDeckText">

                      <div class="flex flex-wrap items-center mt-2">
                        <div v-for="(deck, idx) in selectedDecks"
                          class="flex items-center bg-gray-50 py-1 px-2 rounded border border-gray-300 text-gray-600 text-sm mr-2 mt-1">
                          <span class="mr-3">{{ deck.name }}</span>
                          <icon-x @click="removeDeck(idx)" class="inline-block w-3 h-3 cursor-pointer"></icon-x>
                        </div>
                      </div>

                      <div class="pb-4">
                        <div class="mt-4 max-h-[360px] overflow-y-auto">
                          <div class="pr-4">
                            <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Your notebooks</p>
                            <ul class="my-4 space-y-3">
                              <li v-for="(deck, idx) in suggestedDecks" ref="deckRefs"
                                class="cursor-pointer p-3 text-sm font-bold text-gray-600 border-2 rounded-lg hover:bg-gray-200 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
                                :class="{
                                  'bg-gray-200': idx === deckSelectedIdx,
                                  'bg-gray-50': idx !== deckSelectedIdx,
                                  'border-blue-600': isInSelectedDecks(deck),
                                }"
                                @click="addSelectedDeck(idx)">
                                <div class="flex items-center ms-3">
                                  <span class="">{{ deck.name }}</span>
                                </div>
                                <p class="ms-3 font-normal text-gray-500" v-if="deck.description">{{ deck.description }}
                                </p>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
                <DialogFooter class="flex flex-row">
                  <SubmitButton 
                    text="Add" 
                    :is-loading="isAddCardLoading" 
                    @click="addCard()" 
                    is-primary
                  />
                </DialogFooter>
              </template>
              <template v-else>
                <p class="text-lg font-semibold text-gray-800">You need to sign in to save this word</p>
                <div class="flex mt-4 space-x-2">
                  <SubmitButton 
                    class="text-white bg-blue-800 hover:bg-blue-900 focus:ring-4 focus:outline-none focus:ring-blue-200 font-medium rounded-lg text-xs px-3 py-1.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                    text="Sign In" 
                    loading-text="Signing in..."
                    :is-loading="isSignInLoading" 
                    @click="addCardSignIn()" 
                    is-primary
                  />
                </div>
              </template>
            </template>
            <template v-else-if="cardUrl">
              <div
                class="flex flex-col items-center justify-center"
              >
                <p class="text-lg font-semibold text-gray-800">
                  Word saved successfully!
                </p>
                <a
                  :href="cardUrl"
                  target="_blank"
                  class="text-blue-700 hover:text-blue-800 mt-2"
                >
                  View card
                </a>
              </div>
              <DialogFooter class="flex flex-row">
                <button
                  class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  @click="openAddCardDialog = false">
                  Close
                </button>
              </DialogFooter>
            </template>
            <template v-else>
              <div
                class="flex flex-col items-center justify-center"
              >
                <p class="text-lg font-semibold text-gray-800">
                  Error occurred while saving the word.
                </p>
                <p class="text-sm text-gray-600 mt-2">
                  {{ addCardError }}
                </p>
              </div>
              <DialogFooter class="flex flex-row">
                <button
                  class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  @click="goBackToEditCardDialog()">
                  Go back
                </button>
              </DialogFooter>
            </template>
          </DialogContent>
        </Dialog>
      </div>

      <div class="mt-8">
        <Select v-model="selectedLanguage">
          <SelectTrigger class="sm:w-48">
            <SelectValue placeholder="Select Language" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem v-for="language in languages" :key="language.value" :value="language.value">
                {{ language.name }}
              </SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>

      <div v-if="wordEntry !== undefined" class="pb-10">
        <div v-for="(definition, index) in wordEntry.definitions"
          :class="{ 'mt-16': index > 0, 'border-t': index > 0 }">
          <div class="flex mt-8 items-center">
            <p class="text-base text-gray-600">{{ index + 1 }}.
              <span class="inline-block text-base border rounded-xl px-2 align-middle ml-1"
                :class="stylesForPartOfSpeech(definition.partOfSpeech)">
                {{ definition.partOfSpeech }}
              </span>
              <span class="ml-1" v-if="!selectedLanguage || selectedLanguage === 'English'">{{ definition.definition
                }}</span>
              <span class="ml-1"
                v-else-if="definition.languages && definition.languages[selectedLanguage] && definition.languages[selectedLanguage].definition">{{
                  renderDefinitionForLanguage(definition, selectedLanguage) }}</span>
              <Spinner 
                :size="5" 
                v-if="isDefinitionForLanguageLoading" 
                :show-shortcut-tip="true" 
                :shortcut-tip="isMac ? 'Cmd+Shift+K' : 'Ctrl+Shift+K'"
                shortcut-message="to explore more vocabulary"
              />
            </p>
            <div @click="showAddCardModal(index)" class="ml-1 text-gray-700">
              <div class="p-1 hover:rounded hover:bg-gray-100">
                <icon-bookmark class="w-4 h-4 cursor-pointer flex-shrink-0"></icon-bookmark>
              </div>
            </div>
          </div>

          <!-- <div class="mt-8">
          <Spinner :show-message="false" :size="'5'" v-if="isDefinitionForLanguageLoading"></Spinner>
          <ul v-if="definition.languages" class="mt-8 text-base text-gray-500 list-disc list-inside">
            <li v-for="language in Object.entries(definition.languages)">
              <span class="font-semibold">{{ language[0] }}: </span>
              <span>{{ language[1].definition }}</span>
            </li>
          </ul>
        </div> -->

          <div class="mt-8" v-if="definition.context">
            <p class="text-2xl text-gray-700 font-semibold">Usage context</p>
            <p class="text-base text-gray-600 mt-2" v-if="!selectedLanguage || selectedLanguage === 'English'">{{
              definition.context }}</p>
            <p class="text-base text-gray-600 mt-2"
              v-else-if="definition.languages && definition.languages[selectedLanguage] && definition.languages[selectedLanguage].definition">
              {{ definition.languages[selectedLanguage].context }}</p>
            <Spinner :size="5" class="mt-2" v-else></Spinner>
          </div>

          <div class="mt-8">
            <p class="text-2xl text-gray-700 font-semibold">Examples</p>
            <ol class="list-decimal list-inside text-base text-gray-600">
              <li v-for="example in definition.examples" class="p-1 mt-2">
                <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">{{ example }}</blockquote>
              </li>
            </ol>
            <Spinner 
              :size="5" 
              class="mt-2" 
              v-if="isMoreExamplesLoading"
              :show-shortcut-tip="true" 
              :shortcut-tip="isMac ? 'Cmd+Shift+K' : 'Ctrl+Shift+K'"
              shortcut-message="to access your vocabulary anytime"
            ></Spinner>
          </div>
          <div class="mt-8">
            <div class="flex items-center">
              <p class="text-2xl text-gray-700 font-semibold">Synonyms</p>
              <!-- <Dropdown :offsetDistance="-10" :offsetSkidding="-20">
                <template #trigger>
                  <div class="p-2 hover:rounded hover:bg-gray-100 cursor-pointer">
                    <icon-dots-vertical class="w-4 h-4"></icon-dots-vertical>
                  </div>
                </template>
                <template #body>
                  <div
                    class="z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600">
                    <ul class="py-1" role="none">
                      <li>
                        <div @click="compareWords(wordEntry, index)"
                          class="flex items-center px-5 py-2 text-base text-gray-600 hover:bg-gray-100 cursor-pointer"
                          role="menuitem">
                          <icon-scale class="inline-block w-4 h-4"></icon-scale>
                          <span class="ml-2">Compare words</span>
                        </div>
                      </li>
                    </ul>
                  </div>
                </template>
              </Dropdown> -->
            </div>
            <ol class="text-base text-gray-600 mt-4">
              <li v-for="synonym in definition.synonyms" class="mt-4">
                <!-- <NuxtLink :to="{ name: 'app-dictionary', query: { word: synonym.synonym } }" rel="nofollow"
                  class="font-semibold underline text-blue-800">{{ synonym.synonym }}</NuxtLink> -->
                <div
                  class="font-semibold underline text-blue-800 cursor-pointer"
                  @click="resetAndLookupWord(synonym.synonym)">
                  {{  synonym.synonym }}
                </div>
                <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">{{ synonym.example }}
                </blockquote>
              </li>
            </ol>
            <Spinner 
              :size="5" 
              class="mt-3" 
              v-if="isMoreSynonymsLoading"
              :show-shortcut-tip="true" 
              :shortcut-tip="isMac ? 'Cmd+Shift+K' : 'Ctrl+Shift+K'"
              shortcut-message="to build your vocabulary collection"
            ></Spinner>
          </div>
        </div>
      </div>
    </div>

    <div v-if="isDefinitionForLanguageLoading" class="px-4 py-8 flex justify-center">
      <Spinner 
        :size="8" 
        :show-shortcut-tip="true" 
        :shortcut-tip="isMac ? 'Cmd+Shift+K' : 'Ctrl+Shift+K'"
        shortcut-message="to explore more vocabulary"
      />
    </div>
  </div>
</template>

<style scoped></style>
