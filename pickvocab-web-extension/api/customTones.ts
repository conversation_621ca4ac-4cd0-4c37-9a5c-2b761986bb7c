import { getAxiosInstance } from '@/utils/axiosInstance';
import type { CustomTone } from '@/components/write/types';

// API response type (snake_case)
interface ApiCustomTone {
  id: number;
  owner: number;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  bg_color?: string;
  text_color?: string;
}

// Payload for creating/updating (camelCase)
export interface CustomTonePayload {
  name: string;
  description?: string;
  bgColor?: string;
  textColor?: string;
}

// Payload for API (snake_case)
interface ApiCustomTonePayload {
  name: string;
  description?: string;
  bg_color?: string;
  text_color?: string;
}

// --- Mapping Functions ---
function toCustomTone(apiTone: ApiCustomTone): CustomTone {
  return {
    id: apiTone.id,
    ownerId: apiTone.owner,
    name: apiTone.name,
    description: apiTone.description,
    createdAt: apiTone.created_at,
    updatedAt: apiTone.updated_at,
    bgColor: apiTone.bg_color || 'bg-gray-100',
    textColor: apiTone.text_color || 'text-gray-800',
  };
}

function toApiCustomTonePayload(payload: CustomTonePayload): ApiCustomTonePayload {
  const apiPayload: ApiCustomTonePayload = {
    name: payload.name,
  };
  if (payload.description !== undefined && payload.description.trim() !== '') {
    apiPayload.description = payload.description;
  } else {
    apiPayload.description = '';
  }
  if (payload.bgColor) {
    apiPayload.bg_color = payload.bgColor;
  }
  if (payload.textColor) {
    apiPayload.text_color = payload.textColor;
  }
  return apiPayload;
}

// --- RemoteCustomTonesApi Class ---
export class RemoteCustomTonesApi {
  private axios = getAxiosInstance();
  private baseUrl = '/custom_tones/';

  /**
   * List user's custom writing tones
   */
  async list(): Promise<CustomTone[]> {
    const response = await this.axios.get(this.baseUrl);
    return response.data.results.map(toCustomTone);
  }

  /**
   * Create a new custom writing tone
   */
  async create(payload: CustomTonePayload): Promise<CustomTone> {
    const apiPayload = toApiCustomTonePayload(payload);
    const response = await this.axios.post<ApiCustomTone>(this.baseUrl, apiPayload);
    return toCustomTone(response.data);
  }

  /**
   * Update a specific custom writing tone (PATCH)
   */
  async update(id: number, payload: Partial<CustomTonePayload>): Promise<CustomTone> {
    const apiPayload: Partial<ApiCustomTonePayload> = {};
    if (payload.name !== undefined) {
      apiPayload.name = payload.name;
    }
    if (payload.description !== undefined) {
      apiPayload.description = payload.description.trim() === '' ? '' : payload.description;
    }
    if (payload.bgColor !== undefined) {
      apiPayload.bg_color = payload.bgColor;
    }
    if (payload.textColor !== undefined) {
      apiPayload.text_color = payload.textColor;
    }
    const response = await this.axios.patch<ApiCustomTone>(`${this.baseUrl}${id}/`, apiPayload);
    return toCustomTone(response.data);
  }

  /**
   * Delete a specific custom writing tone
   */
  async delete(id: number): Promise<void> {
    await this.axios.delete(`${this.baseUrl}${id}/`);
  }
} 