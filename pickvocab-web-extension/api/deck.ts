import { toCard, type ApiGenericCard } from "./genericCard";

interface ApiDeck {
  id: number;
  name: string;
  description?: string;
  card_set?: {
    count: number;
    previous: string | null;
    next: string | null;
    results: {
      id: number;
      deck: number;
      card: ApiGenericCard;
      created_at: string;
      updated_at: string;
    }[];
  };
  owner: number;
  is_demo: boolean;
  created_at: string;
  updated_at: string;
}

interface ApiBaseDeck {
  name: string;
  description?: string;
  cards?: CardId[];
}

function toDeck(apiDeck: ApiDeck): Deck {
  return {
    id: apiDeck.id,
    name: apiDeck.name,
    description: apiDeck.description,
    cards: apiDeck.card_set ? apiDeck.card_set.results.map((deckCard) => toCard(deckCard.card)) : [],
    owner: apiDeck.owner,
    isDemo: apiDeck.is_demo,
    totalCards: apiDeck.card_set ? apiDeck.card_set?.count : 0,
  }
}

function toApiBaseDeck(deck: BaseDeck): ApiBaseDeck {
  return {
    name: deck.name,
    description: deck.description,
    // cards: deck.cards.map(card => card.id),
  }
}

export class RemoteDecksApi {
  async list(params?: { page?: number, [key: string]: unknown }): Promise<Deck[]> {
    const axios = getAxiosInstance();
    const response = await axios.get('/decks2/', { params });
    return response.data.results.map(toDeck);
  }
  async get(id: DeckId, page?: number): Promise<Deck | undefined> {
    const axios = getAxiosInstance();
    const response = await axios.get(`/decks2/${id}/`, { params: { page } });
    return toDeck(response.data);
  }
  async create(deck: BaseDeck): Promise<Deck> {
    const apiBase = toApiBaseDeck(deck);
    const axios = getAxiosInstance();
    const response = await axios.post('/decks2/', apiBase);
    return toDeck(response.data as ApiDeck);
  }
  async delete(id: DeckId): Promise<void> {
    const axios = getAxiosInstance();
    await axios.delete(`/decks2/${id}/`);
  }
  async search(text: string, abortSignal?: AbortSignal): Promise<Deck[]> {
    const axios = getAxiosInstance();
    try {
      const response = await axios.get(`/decks2/search?text=${text}`, { signal: abortSignal });
      return response.data.map(toDeck);
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return [];
      }
      throw err;
    }
  }
  async addCard(deckId: DeckId, cardId: CardId): Promise<Deck> {
    const axios = getAxiosInstance();
    const response = await axios.post(`/decks2/${deckId}/add_cards/`, { cards: [cardId] });
    return toDeck(response.data);
  }
  async removeCard(deckId: DeckId, cardId: CardId): Promise<Deck> {
    const axios = getAxiosInstance();
    const response = await axios.post(`/decks2/${deckId}/remove_cards/`, { cards: [cardId] });
    return toDeck(response.data);
  }
}
