import { LLMModel } from "@/utils/llm";

interface ApiLLMModel {
  id: number;
  name: string;
  label: string;
  provider: string;
  is_free: boolean;
  is_hidden: boolean;
  is_thinking: boolean;
  arena_score: number;
  created_at: string;
  updated_at: string;
}

function toLLMModel(apiLLMModel: ApiLLMModel): LLMModel {
  return {
    id: apiLLMModel.id,
    name: apiLLMModel.name,
    label: apiLLMModel.label,
    provider: apiLLMModel.provider,
    isFree: apiLLMModel.is_free,
    isHidden: apiLLMModel.is_hidden,
    arenaScore: apiLLMModel.arena_score,
    isActive: false,
    isThinking: apiLLMModel.is_thinking || false,
  }
}

export class LLMModelsApi {
  async list(): Promise<LLMModel[]> {
    const axios = getAxiosInstance();
    const response = await axios.get('/llm_models/');
    return response.data.map(toLLMModel);
  }
}
