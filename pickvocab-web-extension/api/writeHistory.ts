import { getAxiosInstance } from '@/utils/axiosInstance';
import type { RevisionData } from '@/components/write/types';

// API payload and response types
export interface ApiWritingRevisionHistory {
  id: number;
  owner: number;
  created_at: string;
  trigger_type: string;
  original_text: string;
  use_my_vocabulary: boolean;
  selected_tone: string;
  revisions: RevisionData[];
}

export interface WritingRevisionHistory {
  id: number;
  createdAt: string;
  triggerType: 'Revise' | 'Refresh' | 'GrammarCheck';
  originalText: string;
  useMyVocabulary: boolean;
  selectedTone: string;
  revisions: RevisionData[];
}

function toApiWritingRevisionHistory(history: Partial<WritingRevisionHistory>): Partial<ApiWritingRevisionHistory> {
  return {
    trigger_type: history.triggerType,
    original_text: history.originalText,
    use_my_vocabulary: history.useMyVocabulary,
    selected_tone: history.selectedTone,
    revisions: history.revisions
  };
}

// Converts API response (snake_case) to frontend format (camelCase)
export function toWritingRevisionHistory(apiHistory: ApiWritingRevisionHistory): WritingRevisionHistory {
  return {
    id: apiHistory.id,
    createdAt: apiHistory.created_at,
    triggerType: apiHistory.trigger_type as 'Revise' | 'Refresh' | 'GrammarCheck',
    originalText: apiHistory.original_text,
    useMyVocabulary: apiHistory.use_my_vocabulary,
    selectedTone: apiHistory.selected_tone,
    revisions: apiHistory.revisions
  };
}

export class RemoteWriteHistoryApi {
  private axios = getAxiosInstance();
  private baseUrl = '/write/history/';

  /**
   * Create a new writing revision history entry
   */
  async create(history: Partial<WritingRevisionHistory>, cardIds?: string[]): Promise<WritingRevisionHistory> {
    const apiHistory = toApiWritingRevisionHistory(history);
    const payload = {
      ...apiHistory,
      cardIds
    };
    const response = await this.axios.post(this.baseUrl, payload);
    return toWritingRevisionHistory(response.data);
  }

  /**
   * Update an existing writing revision history entry
   */
  async update(historyId: number, updateData: { revisions: RevisionData[] }): Promise<WritingRevisionHistory> {
    const url = `${this.baseUrl}${historyId}/`;
    const payload = {
      revisions: updateData.revisions
    };
    const response = await this.axios.patch(url, payload);
    return toWritingRevisionHistory(response.data);
  }
}
