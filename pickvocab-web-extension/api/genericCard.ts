import type { DefinitionDetails } from "pickvocab-dictionary";
import { toWordEntry, type ApiWordEntry } from "./dictionary/remote";
import { toWordInContextEntry, type ApiWordInContextEntry } from "./wordInContext";
import { CardType, type BaseContextCard, type BaseDefinitionCard, type ContextCard, type DefinitionCard } from "@/utils/card";

interface ApiBaseDefinitionCard {
  word: string;
  word_ref?: number | string;
  word_def_idx: number;
  definition: Partial<DefinitionDetails>;
}

interface ApiBaseContextCard {
  word_in_context: number | string;
}

export interface ApiDefinitionCard extends ApiBaseDefinitionCard {
  id: number;
  word_ref_detail?: ApiWordEntry;
  created_at: string;
  updated_at: string;
}

export interface ApiContextCard extends ApiBaseContextCard {
  id: number;
  word_in_context_detail: ApiWordInContextEntry;
  created_at: string;
  updated_at: string;
}

export interface ApiGenericCard {
  id: number;
  card: ApiDefinitionCard | ApiContextCard;
  card_content_type: number;
  card_object_id: number;
  owner: number;
  progress_score: number;
  created_at: string;
  updated_at: string;
}

export function toCard(apiCard: ApiGenericCard): Card {
  const definitionCardId = Number(import.meta.env.WXT_DEFINITION_CARD_ID);
  const contextCardId = Number(import.meta.env.WXT_CONTEXT_CARD_ID);
  switch (apiCard.card_content_type) {
    case definitionCardId: {
      return {
        cardType: CardType.DefinitionCard,
        id: apiCard.id,
        word: (apiCard.card as ApiDefinitionCard).word,
        referenceWord: (apiCard.card as ApiDefinitionCard).word_ref_detail ? toWordEntry((apiCard.card as ApiDefinitionCard).word_ref_detail!) : undefined,
        refDefIdx: (apiCard.card as ApiDefinitionCard).word_def_idx,
        definition: (apiCard.card as ApiDefinitionCard).definition,
        owner: apiCard.owner,
        createdAt: apiCard.created_at,
        updatedAt: apiCard.updated_at
      }
    }
    case contextCardId: {
      return {
        cardType: CardType.ContextCard,
        id: apiCard.id,
        wordInContext: toWordInContextEntry((apiCard.card as ApiContextCard).word_in_context_detail),
        owner: apiCard.owner,
        createdAt: apiCard.created_at,
        updatedAt: apiCard.updated_at,
      }
    }

    default:
      throw new Error('Unsupported card type');
  }
}

function toApiBaseDefinitionCard(card: BaseDefinitionCard): ApiBaseDefinitionCard {
  return {
    word: card.word,
    definition: card.definition,
    word_ref: card.referenceWord?.id,
    word_def_idx: card.refDefIdx ? card.refDefIdx : 0,
  }
}

function toApiBaseContextCard(card: BaseContextCard): ApiBaseContextCard {
  return {
    word_in_context: card.wordInContext.id,
  }
}

export class RemoteGenericCardsApi {
  async list(params?: { page?: number, [key: string]: unknown }): Promise<{
    result: Card[],
    totalPages: number
  }> {
    const pageSize = Number(import.meta.env.WXT_PAGE_SIZE || 20);
    const axios = getAxiosInstance();
    const response = await axios.get('/generic_cards/', { params });
    return {
      result: response.data.results.map(toCard),
      totalPages: Math.ceil(response.data.count / pageSize),
    }
  }
  async get(id: CardId): Promise<Card | undefined> {
    const axios = getAxiosInstance();
    const response = await axios.get(`/generic_cards/${id}`);
    return toCard(response.data);
  }
  async delete(id: CardId): Promise<void> {
    const axios = getAxiosInstance();
    await axios.delete(`/generic_cards/${id}`);
  }
  async createDefinitionCard(card: BaseDefinitionCard): Promise<DefinitionCard> {
    const apiBase = toApiBaseDefinitionCard(card);
    const axios = getAxiosInstance();
    const response = await axios.post('/generic_cards/create_definition_card/', apiBase);
    return toCard(response.data as ApiGenericCard) as DefinitionCard;
  }
  async createContextCard(card: BaseContextCard): Promise<ContextCard> {
    const apiBase = toApiBaseContextCard(card);
    const axios = getAxiosInstance();
    const response = await axios.post('/generic_cards/create_context_card/', apiBase);
    return toCard(response.data as ApiGenericCard) as ContextCard;
  }
  async updateDefinitionCard(card: DefinitionCard): Promise<DefinitionCard> {
    const axios = getAxiosInstance();
    const response = await axios.put(`/generic_cards/${card.id}/update_definition_card/`, {
      definition: card.definition
    });
    return toCard(response.data as ApiGenericCard) as DefinitionCard;
  }
  async search(text: string, abortSignal?: AbortSignal): Promise<Card[]> {
      const axios = getAxiosInstance();
      try {
        const response = await axios.get(`/generic_cards/search?text=${text}`, { signal: abortSignal });
        return response.data.map(toCard);
      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          return [];
        }
        throw err;
      }
    }
  async startSimilaritySearch(query: string, options?: { limit?: number, threshold?: number }): Promise<{
    taskId: string,
    status: string,
    message: string
  }> {
    const axios = getAxiosInstance();
    const payload = {
      query,
      ...(options?.limit !== undefined && { limit: options.limit }),
      ...(options?.threshold !== undefined && { threshold: options.threshold })
    };
    const response = await axios.post('/generic_cards/start_similarity_search/', payload);
    return {
      taskId: response.data.task_id,
      status: response.data.status,
      message: response.data.message
    };
  }

  async getSimilaritySearchResults(taskId: string): Promise<{
    status: string;
    results?: { card: Card; similarityScore: number }[];
    error?: string;
    message?: string;
  }> {
    const axios = getAxiosInstance();
    const response = await axios.get(`/generic_cards/get_similarity_search_results/`, {
      params: { task_id: taskId }
    });
    let transformedResults: { card: Card; similarityScore: number }[] | undefined;
    if (response.data.results && response.data.results.success && Array.isArray(response.data.results.results)) {
      transformedResults = response.data.results.results.map((result: {
        generic_card: ApiGenericCard,
        similarity_score: number,
      }) => {
        return {
          card: toCard(result.generic_card),
          similarityScore: result.similarity_score || 0
        };
      });
    }
    return {
      status: response.data.status,
      results: transformedResults,
      error: response.data.error,
      message: response.data.message
    };
  }
}
