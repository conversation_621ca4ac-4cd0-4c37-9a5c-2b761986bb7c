import { getAxiosInstance } from "@/utils/axiosInstance";
import { User } from "@/utils/user";

export async function authenticateGoogleUser(code: string): Promise<{ user: User, key: string }> {
  const axios = getAxiosInstance();
  const response = await axios.post(`/dj-rest-auth/extension-google/`, {
    code
  });

  return { user: response.data.user, key: response.data.key };
}

export async function registerAnnonymousUser(): Promise<{ user: User, key: string }> {
  const axios = getAxiosInstance();
  const response = await axios.post('/accounts/annonymous-registration/', {});
  return { user: response.data.user, key: response.data.key };
}

export async function getUser(token: string): Promise<User | undefined> {
  const axios = getAxiosInstance();
  const response = await axios.get('/dj-rest-auth/user/', {
    headers: {
      Authorization: `Token ${token}`
    }
  });
  if (response.status === 200) {
    return response.data;
  } else {
    return undefined;
  }
}
