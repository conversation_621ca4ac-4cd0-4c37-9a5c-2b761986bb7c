// CodestralChatClient: Proxy chat client for Codestral, for browser use (avoids CORS by calling your backend)
import type { ChatMessage, ChatSource, ChatResponse } from "pickvocab-dictionary";

export class CodestralChatClient implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  maxTokens = 2000;
  messages: ChatMessage[] = [];

  constructor(config: { modelName: string, modelId: number, maxToken?: number, apiKey: string }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    const response = await $fetch('/api/codestralChat', {
      method: 'POST',
      body: {
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        endpoint: 'sendMessage',
        args: [this.messages, message]
      }
    });
    this.messages.push({ role: 'user', content: message });
    this.messages.push({
      role: 'assistant',
      content: response!.message
    });
    return response as ChatResponse;
  }
} 