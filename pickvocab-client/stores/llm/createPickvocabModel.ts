import { CerebrasChat, CerebrasSource, GeminiChat, GeminiSource, GroqChat, GroqSource, PickvocabChat, PickvocabSource } from "pickvocab-dictionary";
import type { LLMModel } from "~/utils/llm";

export function createPickvocabSource(
  getModelByName: (name: string) => LLMModel | undefined,
  pickvocabModelName: string,
  pickvocabModelId: number
) {
  const config = useRuntimeConfig();
  return new PickvocabSource([
    // Groq
    new GroqSource({
      modelId: getModelByName('meta-llama/llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'meta-llama/llama-4-scout-17b-16e-instruct',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqSource({
      modelId: getModelByName('meta-llama/llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'meta-llama/llama-4-scout-17b-16e-instruct',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqSource({
      modelId: getModelByName('meta-llama/llama-4-maverick-17b-128e-instruct')!.id,
      modelName: 'meta-llama/llama-4-maverick-17b-128e-instruct',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqSource({
      modelId: getModelByName('meta-llama/llama-4-maverick-17b-128e-instruct')!.id,
      modelName: 'meta-llama/llama-4-maverick-17b-128e-instruct',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqSource({
      modelId: getModelByName('llama-3.3-70b-versatile')!.id,
      modelName: 'llama-3.3-70b-versatile',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqSource({
      modelId: getModelByName('llama-3.3-70b-versatile')!.id,
      modelName: 'llama-3.3-70b-versatile',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqSource({
      modelId: getModelByName('llama3-70b-8192')!.id,
      modelName: 'llama3-70b-8192',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    // Gemini
    new GeminiSource({
      modelId: getModelByName('gemini-1.5-flash-latest')!.id,
      modelName: 'gemini-1.5-flash-latest',
      apiKey: config.public.GOOGLE_API_KEY_1,
    }),
    new GeminiSource({
      modelId: getModelByName('gemini-1.5-flash-latest')!.id,
      modelName: 'gemini-1.5-flash-latest',
      apiKey: config.public.GOOGLE_API_KEY_2,
    }),
    new GeminiSource({
      modelId: getModelByName('gemini-1.5-flash-8b')!.id,
      modelName: 'gemini-1.5-flash-8b',
      apiKey: config.public.GOOGLE_API_KEY_1,
    }),
    new GeminiSource({
      modelId: getModelByName('gemini-1.5-flash-8b')!.id,
      modelName: 'gemini-1.5-flash-8b',
      apiKey: config.public.GOOGLE_API_KEY_2,
    }),
    new GeminiSource({
      modelId: getModelByName('gemini-2.0-flash-001')!.id,
      modelName: 'gemini-2.0-flash-001',
      apiKey: config.public.GOOGLE_API_KEY_1,
    }),
    new GeminiSource({
      modelId: getModelByName('gemini-2.0-flash-001')!.id,
      modelName: 'gemini-2.0-flash-001',
      apiKey: config.public.GOOGLE_API_KEY_2,
    }),
    // Cerebras
    new CerebrasSource({
      modelId: getModelByName('llama-3.3-70b')!.id,
      modelName: 'llama-3.3-70b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('llama-3.3-70b')!.id,
      modelName: 'llama-3.3-70b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('llama-3.3-70b')!.id,
      modelName: 'llama-3.3-70b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('llama-3.3-70b')!.id,
      modelName: 'llama-3.3-70b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('qwen-3-32b')!.id,
      modelName: 'qwen-3-32b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('qwen-3-32b')!.id,
      modelName: 'qwen-3-32b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('qwen-3-32b')!.id,
      modelName: 'qwen-3-32b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('qwen-3-32b')!.id,
      modelName: 'qwen-3-32b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
  ], [
    new GroqSource({
      modelId: getModelByName('llama3-8b-8192')!.id,
      modelName: 'llama3-8b-8192',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqSource({
      modelId: getModelByName('llama-3.1-8b-instant')!.id,
      modelName: 'llama-3.1-8b-instant',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqSource({
      modelId: getModelByName('gemma2-9b-it')!.id,
      modelName: 'gemma2-9b-it',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    // Cerebras
    new CerebrasSource({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasSource({
      modelId: getModelByName('llama-3.1-8b')!.id,
      modelName: 'llama-3.1-8b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
  ], {
    modelId: pickvocabModelId,
    modelName: pickvocabModelName
  });
}

export function createPickvocabChat(
  getModelByName: (name: string) => LLMModel | undefined,
  pickvocabModelName: string,
  pickvocabModelId: number
) {
  const config = useRuntimeConfig();
  return new PickvocabChat([
    // Groq
    new GroqChat({
      modelId: getModelByName('meta-llama/llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'meta-llama/llama-4-scout-17b-16e-instruct',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqChat({
      modelId: getModelByName('meta-llama/llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'meta-llama/llama-4-scout-17b-16e-instruct',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqChat({
      modelId: getModelByName('meta-llama/llama-4-maverick-17b-128e-instruct')!.id,
      modelName: 'meta-llama/llama-4-maverick-17b-128e-instruct',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqChat({
      modelId: getModelByName('meta-llama/llama-4-maverick-17b-128e-instruct')!.id,
      modelName: 'meta-llama/llama-4-maverick-17b-128e-instruct',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqChat({
      modelId: getModelByName('llama-3.3-70b-versatile')!.id,
      modelName: 'llama-3.3-70b-versatile',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqChat({
      modelId: getModelByName('llama3-70b-8192')!.id,
      modelName: 'llama3-70b-8192',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    // Gemini
    new GeminiChat({
      modelId: getModelByName('gemini-1.5-flash-latest')!.id,
      modelName: 'gemini-1.5-flash-latest',
      apiKey: config.public.GOOGLE_API_KEY_1,
    }),
    new GeminiChat({
      modelId: getModelByName('gemini-1.5-flash-latest')!.id,
      modelName: 'gemini-1.5-flash-latest',
      apiKey: config.public.GOOGLE_API_KEY_2,
    }),
    new GeminiChat({
      modelId: getModelByName('gemini-1.5-flash-8b')!.id,
      modelName: 'gemini-1.5-flash-8b',
      apiKey: config.public.GOOGLE_API_KEY_1,
    }),
    new GeminiChat({
      modelId: getModelByName('gemini-1.5-flash-8b')!.id,
      modelName: 'gemini-1.5-flash-8b',
      apiKey: config.public.GOOGLE_API_KEY_2,
    }),
    new GeminiChat({
      modelId: getModelByName('gemini-2.0-flash-001')!.id,
      modelName: 'gemini-2.0-flash-001',
      apiKey: config.public.GOOGLE_API_KEY_1,
    }),
    new GeminiChat({
      modelId: getModelByName('gemini-2.0-flash-001')!.id,
      modelName: 'gemini-2.0-flash-001',
      apiKey: config.public.GOOGLE_API_KEY_2,
    }),
    // Cerebras
    new CerebrasChat({
      modelId: getModelByName('llama-3.3-70b')!.id,
      modelName: 'llama-3.3-70b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('llama-3.3-70b')!.id,
      modelName: 'llama-3.3-70b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('llama-3.3-70b')!.id,
      modelName: 'llama-3.3-70b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('llama-3.3-70b')!.id,
      modelName: 'llama-3.3-70b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('qwen-3-32b')!.id,
      modelName: 'qwen-3-32b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('qwen-3-32b')!.id,
      modelName: 'qwen-3-32b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('qwen-3-32b')!.id,
      modelName: 'qwen-3-32b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('qwen-3-32b')!.id,
      modelName: 'qwen-3-32b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
  ], [
    new GroqChat({
      modelId: getModelByName('llama3-8b-8192')!.id,
      modelName: 'llama3-8b-8192',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqChat({
      modelId: getModelByName('llama-3.1-8b-instant')!.id,
      modelName: 'llama-3.1-8b-instant',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    new GroqChat({
      modelId: getModelByName('gemma2-9b-it')!.id,
      modelName: 'gemma2-9b-it',
      apiKey: config.public.GROQ_API_KEY_1,
    }),
    // Cerebras
    new CerebrasChat({
      modelId: getModelByName('llama-4-scout-17b-16e-instruct')!.id,
      modelName: 'llama-4-scout-17b-16e-instruct',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
    new CerebrasChat({
      modelId: getModelByName('llama-3.1-8b')!.id,
      modelName: 'llama-3.1-8b',
      apiKey: config.public.CEREBRAS_API_KEY_1,
    }),
  ], {
    modelId: pickvocabModelId,
    modelName: pickvocabModelName
  });
}
