// CodestralSourceClient: Proxy client for Codestral, for browser use (avoids CORS by calling your backend)
import type {
  BaseWordEntry,
  BaseWordInContextEntry,
  DefinitionForLanguagePromptResult,
  DictionarySource,
  ExamplePromptResult,
  SynonymPromptResult
} from "pickvocab-dictionary";

export class CodestralSourceClient implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 2000;

  constructor(config: { modelId: number, modelName: string, maxToken?: number, apiKey?: string }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const response = await $fetch('/api/codestral', {
      method: 'POST',
      body: {
        endpoint: 'listAllMeanings',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        args: [word]
      }
    });
    return response as BaseWordEntry;
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const response = await $fetch('/api/codestral', {
      method: 'POST',
      body: {
        endpoint: 'listAllMeaningsForLanguage',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        args: [input, language]
      }
    });
    return response as DefinitionForLanguagePromptResult;
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const response = await $fetch('/api/codestral', {
      method: 'POST',
      body: {
        endpoint: 'getMoreExamples',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        args: [input]
      }
    });
    return response as ExamplePromptResult;
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const response = await $fetch('/api/codestral', {
      method: 'POST',
      body: {
        endpoint: 'getMoreSynonymsForDefinition',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        args: [input]
      }
    });
    return response as SynonymPromptResult;
  }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const response = await $fetch('/api/codestral', {
      method: 'POST',
      body: {
        endpoint: 'getMeaningInContext',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        args: [word, context, offset]
      }
    });
    return response as BaseWordInContextEntry;
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const response = await $fetch('/api/codestral', {
      method: 'POST',
      body: {
        endpoint: 'getMeaningInContextShort',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        args: [word, context, offset]
      }
    });
    return response as BaseWordInContextEntry;
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    const response = await $fetch('/api/codestral', {
      method: 'POST',
      body: {
        endpoint: 'getMeaningInContextShortForLanguage',
        apiKey: this.apiKey,
        modelId: this.modelId,
        modelName: this.modelName,
        maxToken: this.maxTokens,
        args: [word, context, offset, language]
      }
    });
    return response as string;
  }
} 