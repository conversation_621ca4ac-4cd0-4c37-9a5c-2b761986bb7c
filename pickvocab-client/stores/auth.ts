import { getUser, registerAnnonymousUser } from "~/api/auth";

export const useAuthStore = defineStore('auth', () => {
  const user: Ref<User | undefined> = ref(undefined);
  const token: Ref<string | undefined> = ref(undefined);
  const version: Ref<number | undefined> = ref(undefined);
  const isShowLoginModal = ref(false);

  const isAuthenticated = computed(() => !!user.value);

  function showLoginModal() {
    isShowLoginModal.value = true;
  }
  function hideLoginModal() {
    isShowLoginModal.value = false;
  }
  function setToken(value: string) {
    const axios = getAxiosInstance();

    token.value = value;
    axios.defaults.headers.common['Authorization'] = `Token ${value}`;
  }
  function setUser(value: User) {
    user.value = value;
    // initSync();
  }
  async function signIn(key: string) {
    setToken(key);
    const user = await getUser(key);
    if (user) setUser(user);
  }
  function signOut() {
    const axios = getAxiosInstance();

    user.value = undefined;
    token.value = undefined;
    delete axios.defaults.headers.common['Authorization'];
    // set annonymous user and token (assign token to axios headers) here
    initUser();
    window.location.reload();
  }

  const annonymousUser: Ref<User | undefined> = ref(undefined);
  const annonymousToken: Ref<string | undefined> = ref(undefined);

  async function signInAnnonymous(key: string) {
    setAnnonymousToken(key);
    const user = await getUser(key);
    if (user) setAnnonymousUser(user);
  }

  async function createAnnonymousUser() {
    const result = await registerAnnonymousUser();
    await signInAnnonymous(result.key);
  }

  function setAnnonymousUser(user: User) {
    annonymousUser.value = user;
  }

  function setAnnonymousToken(aToken: string) {
    const axios = getAxiosInstance();

    annonymousToken.value = aToken;
    if (!token.value) {
      axios.defaults.headers.common['Authorization'] = `Token ${aToken}`;
    }
  }

  async function initUser() {
    const config = useRuntimeConfig();
    const storeVersion = Number(config.public.AUTH_STORE_VERSION || 0);

    if (!version.value || version.value < storeVersion) {
      token.value = undefined;
      user.value = undefined;
      annonymousToken.value = undefined;
      annonymousUser.value = undefined;
      await createAnnonymousUser();
      version.value = storeVersion;
      return;
    }

    if (token.value !== undefined) {
      await signIn(token.value);
    } else if (annonymousToken.value) {
      await signInAnnonymous(annonymousToken.value);
    } else {
      await createAnnonymousUser();
    }
  }

  const currentUser = computed(() => user.value || annonymousUser.value);

  return {
    user,
    isShowLoginModal,
    token,
    isAuthenticated,
    currentUser,
    annonymousUser,
    annonymousToken,
    version,
    showLoginModal,
    hideLoginModal,
    setToken,
    setUser,
    signIn,
    signOut,
    createAnnonymousUser,
    setAnnonymousUser,
    setAnnonymousToken,
    signInAnnonymous,
    initUser,
  }
}, {
  persist: {
    storage: piniaPluginPersistedstate.localStorage(),
    pick: ['user', 'token', 'annonymousUser', 'annonymousToken', 'version'],
  }
});