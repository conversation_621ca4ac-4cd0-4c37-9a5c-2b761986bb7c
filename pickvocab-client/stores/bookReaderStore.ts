import { defineStore } from 'pinia';
import { ref, type Ref } from 'vue';

// Ensure FileSystemFileHandle is available or use 'any' if type definitions are problematic
// For a robust solution, ensure @types/wicg-file-system-access is in devDependencies
// For this task, you can assume the type is available or use 'any' if necessary.

export const useBookReaderStore = defineStore('bookReader', () => {
  const bookData: Ref<ArrayBuffer | null> = ref(null);
  const bookName: Ref<string | null> = ref(null);
  const bookHandle: Ref<FileSystemFileHandle | null> = ref(null); // Or Ref<any | null>

  function setCurrentBookToRead(payload: {
    data: ArrayBuffer;
    name: string;
    handle: FileSystemFileHandle | null; // Or any | null
  }) {
    bookData.value = payload.data;
    bookName.value = payload.name;
    bookHandle.value = payload.handle;
  }

  function consumeBookData(): {
    data: ArrayBuffer | null;
    name: string | null;
    handle: FileSystemFileHandle | null; // Or any | null
  } | null {
    if (bookData.value) {
      const consumed = {
        data: bookData.value,
        name: bookName.value,
        handle: bookHandle.value,
      };
      bookData.value = null;
      bookName.value = null;
      bookHandle.value = null;
      return consumed;
    }
    return null;
  }

  return {
    bookData, // Expose for potential direct read, though consumeBookData is preferred
    bookName,
    bookHandle,
    setCurrentBookToRead,
    consumeBookData,
  };
});