import { defineStore } from 'pinia';
import { ref, computed, type Ref } from 'vue';
import { CustomTonesApi, type CustomTone, type CustomTonePayload } from '~/api/write/customTones';
import { isSelectedToneIdentifier, parseToneIdentifier, getDefaultToneColors } from '~/utils/toneUtils';

// Define the structure for predefined tones
export interface PredefinedTone {
  name: string;
  description: string;
  bgColor: string;
  textColor: string;
}

// Define the structure for the selected tone identifier
export interface SelectedToneIdentifier {
  type: 'predefined' | 'custom';
  identifier: string | number; // name for predefined, id for custom
}

export const useToneStore = defineStore('tone', () => {
  // --- State ---
  const predefinedTones = ref<PredefinedTone[]>([
    // Placeholder - Load actual predefined tones here
    {
      name: 'Professional',
      description: 'Formal language suitable for business and professional contexts.',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-800'
    },
    {
      name: 'Casual',
      description: 'Informal and relaxed language for everyday conversations.',
      bgColor: 'bg-teal-100',
      textColor: 'text-teal-800'
    },
    {
      name: 'Creative',
      description: 'Imaginative and creative language, great for poetry and storytelling.',
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-800'
    },
    {
      name: 'Academic',
      description: 'Scholarly language with precise terminology and formal structure.',
      bgColor: 'bg-indigo-100',
      textColor: 'text-indigo-800'
    },
    {
      name: 'Funny',
      description: 'Humorous and lighthearted style, great for jokes and witty remarks.',
      bgColor: 'bg-orange-100',
      textColor: 'text-orange-800'
    },
    {
      name: 'Technical Writing',
      description: 'Clear, concise language focused on technical accuracy and documentation.',
      bgColor: 'bg-green-100',
      textColor: 'text-green-800'
    },
    {
      name: 'Reddit-vibe',
      description: 'Conversational internet slang with references and casual humor typical of Reddit.',
      bgColor: 'bg-orange-400',
      textColor: 'text-orange-800'
    },
  ]);
  const customTones = ref<CustomTone[]>([]);
  const selectedTone = ref<SelectedToneIdentifier | null>(null); // Initially null or default
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  const api = new CustomTonesApi();

  // --- Actions ---

  // Load predefined tones (currently hardcoded)
  function loadPredefinedTones() {
    // In the future, this could load from a config file or API endpoint
    // For now, the default hardcoded list is used.
    // If a default tone should be selected initially:
    if (!selectedTone.value && predefinedTones.value.length > 0) {
       // selectTone({ type: 'predefined', identifier: predefinedTones.value[0].name });
    }
  }

  // Fetch custom tones from the API
  async function fetchCustomTones() {
    isLoading.value = true;
    error.value = null;
    try {
      customTones.value = await api.list();
    } catch (err: any) {
      console.error('Error fetching custom tones:', err);
      error.value = err.response?.data?.detail || err.message || 'Failed to fetch custom tones.';
      customTones.value = []; // Clear on error
    } finally {
      isLoading.value = false;
    }
  }

  // Add a new custom tone
  async function addTone(payload: CustomTonePayload) {
    isLoading.value = true;
    error.value = null;
    try {
      const newTone = await api.create(payload);
      customTones.value.push(newTone);
      // Optionally select the newly added tone
      // selectTone({ type: 'custom', identifier: newTone.id });
    } catch (err: any) {
      console.error('Error adding custom tone:', err);
      error.value = err.response?.data?.detail || err.response?.data?.name?.[0] || err.message || 'Failed to add custom tone.';
      // Re-throw or handle specific validation errors if needed
      throw err; // Re-throw to allow components to handle UI feedback on failure
    } finally {
      isLoading.value = false;
    }
  }

  // Update an existing custom tone
  async function updateTone(id: number, payload: Partial<CustomTonePayload>) {
    isLoading.value = true;
    error.value = null;
    try {
      const updatedTone = await api.update(id, payload);
      const index = customTones.value.findIndex(t => t.id === id);
      if (index !== -1) {
        customTones.value[index] = updatedTone;
        // If the updated tone was the selected one, update the selection details if needed
        if (selectedTone.value?.type === 'custom' && selectedTone.value?.identifier === id) {
          // Selection identifier remains the same (id), details will update via getter
        }
      }
    } catch (err: any) {
      console.error('Error updating custom tone:', err);
      error.value = err.response?.data?.detail || err.response?.data?.name?.[0] || err.message || 'Failed to update custom tone.';
      throw err; // Re-throw for component handling
    } finally {
      isLoading.value = false;
    }
  }


  // Remove a custom tone
  async function removeTone(id: number) {
    isLoading.value = true;
    error.value = null;
    try {
      await api.delete(id);
      customTones.value = customTones.value.filter(t => t.id !== id);
      // If the removed tone was selected, reset selection
      if (selectedTone.value?.type === 'custom' && selectedTone.value?.identifier === id) {
        selectedTone.value = null; // Or select a default predefined tone
        if (predefinedTones.value.length > 0) {
           // selectTone({ type: 'predefined', identifier: predefinedTones.value[0].name });
        }
      }
    } catch (err: any) {
      console.error('Error removing custom tone:', err);
      error.value = err.response?.data?.detail || err.message || 'Failed to remove custom tone.';
      throw err; // Re-throw for component handling
    } finally {
      isLoading.value = false;
    }
  }

  // Set the selected tone
  function selectTone(toneIdentifier: SelectedToneIdentifier | null) {
    selectedTone.value = toneIdentifier;
  }

  // --- Getters ---

  // Combined list for dropdown selectors
  const allAvailableTones = computed(() => {
    return [
      ...predefinedTones.value.map(t => ({ ...t, type: 'predefined', identifier: t.name })),
      ...customTones.value.map(t => ({ ...t, type: 'custom', identifier: t.id }))
    ];
  });

  // Get full details of the currently selected tone
  const selectedToneDetails = computed(() => {
    if (!selectedTone.value) return null;

    if (selectedTone.value.type === 'predefined') {
      return predefinedTones.value.find(t => t.name === selectedTone.value?.identifier);
    } else {
      return customTones.value.find(t => t.id === selectedTone.value?.identifier);
    }
  });

  // Helper function to get tone colors by identifier
  function getToneColors(toneIdentifier: SelectedToneIdentifier | string | null | unknown) {
    const parsed = parseToneIdentifier(toneIdentifier);
    
    if (!parsed) {
      return getDefaultToneColors();
    }
    
    if (parsed.type === 'predefined') {
      const tone = predefinedTones.value.find(t => t.name === parsed.identifier);
      return tone ? { bgColor: tone.bgColor, textColor: tone.textColor } : getDefaultToneColors();
    }
    
    if (parsed.type === 'custom') {
      const tone = customTones.value.find(t => t.id === Number(parsed.identifier));
      return tone ? { bgColor: tone.bgColor, textColor: tone.textColor } : getDefaultToneColors();
    }
    
    return getDefaultToneColors();
  }

  return {
    // State
    predefinedTones,
    customTones,
    selectedTone,
    isLoading,
    error,
    // Actions
    loadPredefinedTones,
    fetchCustomTones,
    addTone,
    updateTone,
    removeTone,
    selectTone,
    // Getters
    allAvailableTones,
    selectedToneDetails,
    getToneColors,
  };
});