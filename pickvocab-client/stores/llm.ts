import { LLMModelsApi } from "~/api/llm";
import type { LLMModel, LLMProvider } from "~/utils/llm";
import { createPickvocabChat, createPickvocabSource } from "./llm/createPickvocabModel";
import {
  AnthropicChat,
  AnthropicSource,
  ArliAIChat,
  ArliAISource,
  CerebrasChat,
  CerebrasSource,
  ChutesChat,
  ChutesSource,
  GeminiChat,
  GeminiSource,
  GroqChat,
  GroqSource,
  MistralChat,
  MistralSource,
  OpenAIChat,
  OpenAISource,
  OpenRouterChat,
  OpenRouterSource,
  type ChatSource,
  type DictionarySource,
} from "pickvocab-dictionary";
// @ts-ignore
import geminiImg from '~/assets/gemini.png';
// @ts-ignore
import openaiImg from '~/assets/openai.png';
// @ts-ignore
import anthropicImg from '~/assets/anthropic.png';
// @ts-ignore
import openrouterImg from '~/assets/openrouter.jpg';
// @ts-ignore
import cerebrasImg from '~/assets/cerebras.svg';
// @ts-ignore
import arliImg from '~/assets/arli_ai.svg';
// @ts-ignore
import groqImg from '~/assets/groq.png';
// @ts-ignore
import sambaNovaImg from '~/assets/sambanova.webp';
// @ts-ignore
import mistralImg from '~/assets/mistral.png';
// @ts-ignore
import chutesImg from '~/assets/chutes.png';
import { SambaNovaSourceClient } from "./llm/sambaNovaSourceClient";
import { SambaNovaChatClient } from "./llm/sambaNovaChatClient";
// Codestral proxy clients for browser use
import { CodestralSourceClient } from "./llm/codestralSourceClient";
import { CodestralChatClient } from "./llm/codestralChatClient";

export const useLLMStore = defineStore("llm", () => {
  const config = useRuntimeConfig();

  const providerMap: Record<string, { logo: string, apiKeyUrl: string, keyFormat: string, hidden?: boolean }> = {
    Pickvocab: {
      logo: '/pickvocab.png',
      apiKeyUrl: '',
      keyFormat: '',
      hidden: true
    },
    Gemini: {
      logo: geminiImg,
      apiKeyUrl: 'https://aistudio.google.com/app/apikey',
      keyFormat: 'AIxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
    },
    OpenAI: {
      logo: openaiImg,
      apiKeyUrl: 'https://platform.openai.com/api-keys',
      keyFormat: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
    },
    Anthropic: {
      logo: anthropicImg,
      apiKeyUrl: 'https://console.anthropic.com/settings/keys',
      keyFormat: 'sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
    },
    OpenRouter: {
      logo: openrouterImg,
      apiKeyUrl: 'https://openrouter.ai/settings/keys',
      keyFormat: 'sk-or-v1-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
    },
    ArliAI: {
      logo: arliImg,
      apiKeyUrl: 'https://www.arliai.com/keys',
      keyFormat: '',
      hidden: true,
    },
    Groq: {
      logo: groqImg,
      apiKeyUrl: 'https://console.groq.com/keys',
      keyFormat: 'gsk_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
    },
    Cerebras: {
      logo: cerebrasImg,
      apiKeyUrl: 'https://cloud.cerebras.ai',
      keyFormat: 'csk_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
    },
    SambaNova: {
      logo: sambaNovaImg,
      apiKeyUrl: 'https://cloud.sambanova.ai/apis',
      keyFormat: ''
    },
    Mistral: {
      logo: mistralImg,
      apiKeyUrl: 'https://console.mistral.ai/api-keys/',
      keyFormat: ''
    },
    Codestral: {
      logo: mistralImg,
      apiKeyUrl: 'https://console.mistral.ai/codestral',
      keyFormat: ''
    },
    Chutes: {
      logo: chutesImg,
      apiKeyUrl: 'https://chutes.ai/app/api',
      keyFormat: ''
    }
  };

  const models: Ref<LLMModel[]> = ref([]);
  const providers: Ref<LLMProvider[]> = ref([]);
  const version: Ref<number | undefined> = ref(undefined);

  const api = new LLMModelsApi();

  const pickvocabModel = computed(() => {
    return models.value.find((model) => model.name === config.public.PICKVOCAB_MODEL);
  });

  const userModels = computed(() => {
    return models.value.filter((model) => model.name !== config.public.PICKVOCAB_MODEL);
  });

  const pickvocabDictionarySource = computed(() => {
    if (!pickvocabModel.value) {
      throw new Error('Pickvocab model not found');
    }
    return createPickvocabSource(getModelByName, pickvocabModel.value.name, pickvocabModel.value.id);
  });

  const pickvocabChatSource = computed(() => {
    if (!pickvocabModel.value) {
      throw new Error('Pickvocab model not found');
    }
    return createPickvocabChat(getModelByName, pickvocabModel.value.name, pickvocabModel.value.id);
  });

  /**
   * Sync models with remote
   */
  async function sync() {
    const storeVersion = Number(config.public.LLM_STORE_VERSION || 0);

    const remoteModels = await api.list();
    const remoteProviderSet: Set<string> = new Set();
    remoteModels.forEach((m) => {
      remoteProviderSet.add(m.provider);
    });

    const remoteProviders: LLMProvider[] = [...remoteProviderSet].map(
      (provider) => ({
        name: provider,
      }),
    );

    if (!version.value || version.value < storeVersion) {
      models.value = remoteModels;
      providers.value = remoteProviders;
      version.value = storeVersion;
      return;
    }

    models.value.forEach((model) => {
      const remoteModel = remoteModels.find((m) => m.id === model.id);
      if (remoteModel) remoteModel.isActive = model.isActive;
    });
    models.value = remoteModels;


    providers.value.forEach((provider) => {
      const remoteProvider = remoteProviders.find((p) => p.name === provider.name);
      if (remoteProvider) remoteProvider.apiKey = provider.apiKey;
    });
    providers.value = remoteProviders;

    version.value = storeVersion;
  }

  const validation = computed(() => {
    const results: { [key: string]: { model?: LLMModel, errors: string[] } } = {};
    let hasAnyActiveValidModel = false;

    models.value.forEach((model) => {
      if (model.isActive) {
        const provider = providers.value.find((p) => p.name === model.provider);
        if (!provider || !provider.apiKey) {
          results[model.name] = {
            model,
            errors: [`Missing API key for ${model.provider}. Please set your ${model.provider} API key in "Providers" tab!`]
          }
        } else {
          hasAnyActiveValidModel = true;
        }
      }
    });

    if (!hasAnyActiveValidModel) {
      results['global'] = {
        errors: ['Please activate at least one LLM model!']
      };
    }

    return results;
  });

  function getErrorFromValidation (modelName: string): string | undefined {
    const result = validation.value[modelName];
    if (result) {
      return result.errors.length > 0 ? result.errors[0] : undefined;
    }
    return undefined;
  }

  const activeUserModel = computed(() => {
    return userModels.value.find((model) => model.isActive);
  });

  function createDictionarySource (model: LLMModel): DictionarySource {
    switch (model.provider) {
      case 'Pickvocab':
        return pickvocabDictionarySource.value;

      case 'Gemini': {
        const apiKey = providers.value.find(p => p.name === 'Gemini')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Gemini');
        }
        return new GeminiSource({
          modelId: model.id,
          modelName: model.name,
          apiKey,
        });
      }

      case 'OpenAI': {
        const apiKey = providers.value.find(p => p.name === 'OpenAI')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for OpenAI');
        }
        return new OpenAISource({
          modelId: model.id,
          modelName: model.name,
          apiKey
        });
      }

      case 'Anthropic': {
        const apiKey = providers.value.find(p => p.name === 'Anthropic')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Anthropic');
        }
        return new AnthropicSource({
          modelId: model.id,
          modelName: model.name,
          apiKey,
        });
      }

      case 'OpenRouter': {
        const apiKey = providers.value.find(p => p.name === 'OpenRouter')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for OpenRouter');
        }
        return new OpenRouterSource({
          modelId: model.id,
          modelName: model.name,
          apiKey: providers.value.find(p => p.name === 'OpenRouter')?.apiKey
        });
      }

      case 'ArliAI': {
        const apiKey = providers.value.find(p => p.name === 'ArliAI')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for ArliAI');
        }
        return new ArliAISource({
          modelId: model.id,
          modelName: model.name,
          apiKey
        });
      }

      case 'Groq': {
        const apiKey = providers.value.find(p => p.name === 'Groq')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Groq');
        }
        return new GroqSource({
          modelId: model.id,
          modelName: model.name,
          apiKey: providers.value.find(p => p.name === 'Groq')?.apiKey
        });
      }

      case 'Cerebras': {
        const apiKey = providers.value.find(p => p.name === 'Cerebras')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Cerebras');
        }
        return new CerebrasSource({
          modelId: model.id,
          modelName: model.name,
          apiKey: providers.value.find(p => p.name === 'Cerebras')?.apiKey,
          isThinkingModel: model.isThinking
        });
      }

      case 'SambaNova': {
        const apiKey = providers.value.find(p => p.name === 'SambaNova')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for SambaNova');
        }
        return new SambaNovaSourceClient({
          modelId: model.id,
          modelName: model.name,
          apiKey: providers.value.find(p => p.name === 'SambaNova')?.apiKey
        });
      }

      case 'Mistral': {
        const apiKey = providers.value.find(p => p.name === 'Mistral')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Mistral');
        }
        return new MistralSource({
          modelId: model.id,
          modelName: model.name,
          apiKey: apiKey
        });
      }

      case 'Codestral': {
        const apiKey = providers.value.find(p => p.name === 'Codestral')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Codestral');
        }
        // Use CodestralSourceClient to proxy through backend and avoid CORS
        return new CodestralSourceClient({
          modelId: model.id,
          modelName: model.name,
          apiKey: apiKey
        });
      }

      case 'Chutes': {
        const apiKey = providers.value.find(p => p.name === 'Chutes')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Chutes');
        }
        return new ChutesSource({
          modelId: model.id,
          modelName: model.name,
          apiKey: apiKey,
          isThinkingModel: model.isThinking
        });
      }

      default:
        throw new Error(`Unknown provider: ${model.provider}`);
    }
  }

  function createChatSource (model: LLMModel): ChatSource {
    switch (model.provider) {
      case 'Pickvocab':
        return pickvocabChatSource.value;

      case 'Gemini': {
        const apiKey = providers.value.find(p => p.name === 'Gemini')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Gemini');
        }
        return new GeminiChat({
          modelName: model.name,
          modelId: model.id,
          apiKey,
        });
      }

      case 'OpenAI': {
        const apiKey = providers.value.find(p => p.name === 'OpenAI')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for OpenAI');
        }
        return new OpenAIChat({
          modelName: model.name,
          modelId: model.id,
          apiKey,
        });
      }

      case 'Anthropic': {
        const apiKey = providers.value.find(p => p.name === 'Anthropic')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Anthropic');
        }
        return new AnthropicChat({
          modelName: model.name,
          modelId: model.id,
          apiKey,
        });
      }

      case 'OpenRouter': {
        const apiKey = providers.value.find(p => p.name === 'OpenRouter')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for OpenRouter');
        }
        return new OpenRouterChat({
          modelName: model.name,
          modelId: model.id,
          apiKey,
        });
      }

      case 'ArliAI': {
        const apiKey = providers.value.find(p => p.name === 'ArliAI')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for ArliAI');
        }
        return new ArliAIChat({
          modelName: model.name,
          modelId: model.id,
          apiKey,
        });
      }

      case 'Groq': {
        const apiKey = providers.value.find(p => p.name === 'Groq')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Groq');
        }
        return new GroqChat({
          modelName: model.name,
          modelId: model.id,
          apiKey
        });
      }

      case 'Cerebras': {
        const apiKey = providers.value.find(p => p.name === 'Cerebras')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Cerebras');
        }
        return new CerebrasChat({
          modelName: model.name,
          modelId: model.id,
          apiKey,
          isThinkingModel: model.isThinking
        });
      }

      case 'SambaNova': {
        const apiKey = providers.value.find(p => p.name === 'SambaNova')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for SambaNova');
        }
        return new SambaNovaChatClient({
          modelName: model.name,
          modelId: model.id,
          apiKey,
        });
      }

      case 'Mistral': {
        const apiKey = providers.value.find(p => p.name === 'Mistral')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Mistral');
        }
        return new MistralChat({
          modelName: model.name,
          modelId: model.id,
          apiKey,
        });
      }

      case 'Codestral': {
        const apiKey = providers.value.find(p => p.name === 'Codestral')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Codestral');
        }
        // Use CodestralChatClient to proxy through backend and avoid CORS
        return new CodestralChatClient({
          modelName: model.name,
          modelId: model.id,
          apiKey,
        });
      }

      case 'Chutes': {
        const apiKey = providers.value.find(p => p.name === 'Chutes')?.apiKey;
        if (!apiKey) {
          throw new Error('Missing API key for Chutes');
        }
        return new ChutesChat({
          modelName: model.name,
          modelId: model.id,
          apiKey,
          isThinkingModel: model.isThinking
        });
      }

      default:
        throw new Error(`Unknown provider: ${model.provider}`);
    }
  }

  function getModelById (id: number) {
    return models.value.find((model) => model.id === id);
  }

  function getModelByName (name: string) {
    return models.value.find((model) => model.name === name);
  }

  const lastShowAPIKeyAlert: Ref<number | undefined> = ref(undefined);

  function shouldShowAPIKeyAlert() {
    if (activeUserModel.value) return false;

    if (!lastShowAPIKeyAlert.value) return true;

    const now = Date.now();
    // > 3 days
    return (now - lastShowAPIKeyAlert.value) / (1000 * 60 * 60 * 24) > 3;
  }

  return {
    providerMap,
    version,
    models,
    userModels,
    providers,
    pickvocabDictionarySource,
    pickvocabChatSource,
    sync,
    getErrorFromValidation,
    activeUserModel,
    createDictionarySource,
    createChatSource,
    getModelById,
    getModelByName,
    lastShowAPIKeyAlert,
    shouldShowAPIKeyAlert,
  }
}, {
  persist: {
    storage: piniaPluginPersistedstate.localStorage(),
    pick: ['version', 'models', 'providers', 'lastShowAPIKeyAlert']
  }
});
