import type { WordEntry } from "pickvocab-dictionary";
import { RemoteCardsApi } from "~/api/cards/remote";
import { RemoteDecksApi } from "~/api/decks/remote";
import { RemoteDictionaryApi } from "~/api/dictionary/remote";
import { FeatureFlagApi } from "~/api/featureFlag";
import { RemoteGenericCardsApi } from "~/api/genericCard";
import { RemoteReviewApi } from "~/api/review/remote";
import type { BaseContextCard, BaseDefinitionCard, DefinitionCard } from "~/utils/card";
import type { FeatureFlag } from "~/utils/featureFlag";

export const useAppStore = defineStore('app', () => {
  const isInitialized = ref(false);

  const isShowSearchModal = ref(false);

  function showSearchModal() {
    isShowSearchModal.value = true;
  }
  function hideSearchModal() {
    isShowSearchModal.value = false;
  }

  // Words
  const wordApi = new RemoteDictionaryApi();
  async function searchWords(text: string, abortSignal?: AbortSignal): Promise<string[]> {
    return await wordApi.search(text, abortSignal);
  }

  // Cards
  const isShowAddCardModal = ref(false);
  const currentAddWord: Ref<WordEntry | undefined> = ref(undefined);
  const currentDefIdx = ref(0);

  function showAddCardModal(word?: WordEntry, idx = 0) {
    isShowAddCardModal.value = true;
    currentAddWord.value = word;
    currentDefIdx.value = idx;
  }
  function hideAddCardModal() {
    isShowAddCardModal.value = false;
  }

  const cardApi = new RemoteCardsApi();

  async function listCards(params?: { page?: number, [key: string]: unknown }): Promise<{ result: DefinitionCard[], totalPages: number }> {
    return await cardApi.list(params);
  }
  async function getCard(id: CardId): Promise<DefinitionCard | undefined> {
    return await cardApi.get(id);
  }
  async function createCard(card: BaseDefinitionCard) {
    return await cardApi.create(card);
  }
  async function updateCardDefinition(card: DefinitionCard): Promise<DefinitionCard> {
    return await cardApi.updateDefinition(card);
  }
  async function deleteCard(id: CardId) {
    await cardApi.delete(id);
  }
  async function searchCards(text: string, abortSignal?: AbortSignal): Promise<DefinitionCard[]> {
    return await cardApi.search(text, abortSignal);
  }

  // Generic Cards
  const genericCardApi = new RemoteGenericCardsApi();

  async function listGenericCards(params?: { page?: number, [key: string]: unknown }): Promise<{
    result: Card[],
    totalPages: number,
  }> {
    return await genericCardApi.list(params);
  }
  async function getGenericCard(id: CardId): Promise<Card | undefined> {
    return await genericCardApi.get(id);
  }
  async function createDefinitionCard(card: BaseDefinitionCard) {
    return await genericCardApi.createDefinitionCard(card);
  }
  async function createContextCard(card: BaseContextCard) {
    return await genericCardApi.createContextCard(card);
  }
  async function updateDefinitionCard(card: DefinitionCard): Promise<DefinitionCard> {
    return await genericCardApi.updateDefinitionCard(card);
  }
  async function deleteGenericCard(id: CardId) {
    await genericCardApi.delete(id);
  }
  async function searchGenericCards(text: string, abortSignal?: AbortSignal): Promise<Card[]> {
    return await genericCardApi.search(text, abortSignal);
  }

  async function checkCardEmbeddingStatus(id: CardId): Promise<{
    has_embedding: boolean;
    model_provider?: string;
    model_name?: string;
  }> {
    return await genericCardApi.checkEmbeddingStatus(id);
  }

  // Decks
  const isShowAddDeckModal = ref(false);

  function showAddDeckModal() {
    isShowAddDeckModal.value = true;
  }
  function hideAddDeckModal() {
    isShowAddDeckModal.value = false;
  }

  const deckApi = new RemoteDecksApi();

  async function listDecks(params?: { page?: number, [key: string]: unknown }): Promise<Deck[]> {
    return await deckApi.list(params);
  }
  async function createDeck(deck: BaseDeck) {
    return await deckApi.create(deck);
  }
  async function getDeck(id: DeckId, page = 1): Promise<Deck | undefined> {
    return await deckApi.get(id, page);
  }
  async function searchDecks(text: string, abortSignal?: AbortSignal): Promise<Deck[]> {
    return await deckApi.search(text, abortSignal);
  }
  async function deleteDeck(id: DeckId) {
    await deckApi.delete(id);
  }
  async function addCardToDeck(deckId: DeckId, cardId: CardId) {
    await deckApi.addCard(deckId, cardId);
  }
  async function removeCardFromDeck(deckId: DeckId, cardId: CardId) {
    await deckApi.removeCard(deckId, cardId);
  }

  // Review
  const isShowReviewModal = ref(false);

  function showReviewModal() {
    isShowReviewModal.value = true;
  }
  function hideReviewModal() {
    isShowReviewModal.value = false;
  }

  const reviewApi = new RemoteReviewApi();

  async function getReview (id: ReviewId): Promise<Review | undefined> {
    return await reviewApi.get(id);
  }
  async function createReview(review: BaseReview) {
    return await reviewApi.create(review);
  }
  async function updateScoreReview(review: Review) {
    return await reviewApi.updateScore(review);
  }

  // API Key

  const isShowAPIKeyModal = ref(false);

  function showAPIKeyModal() {
    isShowAPIKeyModal.value = true;
  }
  function hideAPIKeyModal() {
    isShowAPIKeyModal.value = false;
  }

  // Compare Modal
  const isShowCompareModal = ref(false);
  const currentCompareWord = ref('');

  function showCompareModal(currentWord?: string) {
    isShowCompareModal.value = true;
    if (currentWord) {
      currentCompareWord.value = currentWord;
    }
  }
  function hideCompareModal() {
    isShowCompareModal.value = false;
  }

  // Feature flags
  const featureFlags: Ref<Map<string, FeatureFlag> | undefined> = ref(undefined);
  const featureFlagApi = new FeatureFlagApi();

  async function fetchFeatureFlags() {
    featureFlags.value = new Map();
    const featureFlagList = await featureFlagApi.list();
    featureFlagList.forEach((flag) => {
      featureFlags.value?.set(flag.name, flag);
    });
  }

  async function isFeatureFlagEnabled(name: string, userId: number) {
    if (featureFlags.value === undefined) {
      await fetchFeatureFlags();
    }
    const flag = featureFlags.value!.get(name);
    if (!flag) return true;

    if (userId === undefined) {
      return flag.isActive; // Default behavior if no user ID is provided
    }

    const isUserExcepted = flag.exceptUsers.includes(userId);

    if (flag.isActive) {
      return !isUserExcepted; // Active unless in exceptUsers
    } else {
      return isUserExcepted; // Inactive unless in exceptUsers
    }
  }

  const isShowWebExtensionToast = ref(false);
  const showWebExtensionToast = ref(false);

  const isShowWordContextDetailed = ref(true);

  return {
    isInitialized,

    isShowSearchModal,
    showSearchModal,
    hideSearchModal,

    searchWords,

    isShowAddCardModal,
    currentAddWord,
    currentDefIdx,
    showAddCardModal,
    hideAddCardModal,
    listCards,
    getCard,
    createCard,
    updateCardDefinition,
    deleteCard,
    searchCards,

    listGenericCards,
    getGenericCard,
    createDefinitionCard,
    createContextCard,
    updateDefinitionCard,
    deleteGenericCard,
    searchGenericCards,
    checkCardEmbeddingStatus,

    isShowAddDeckModal,
    showAddDeckModal,
    hideAddDeckModal,
    listDecks,
    createDeck,
    getDeck,
    searchDecks,
    deleteDeck,
    addCardToDeck,
    removeCardFromDeck,

    isShowReviewModal,
    showReviewModal,
    hideReviewModal,
    getReview,
    createReview,
    updateScoreReview,

    isShowAPIKeyModal,
    showAPIKeyModal,
    hideAPIKeyModal,

    currentCompareWord,
    isShowCompareModal,
    showCompareModal,
    hideCompareModal,

    featureFlags,
    isFeatureFlagEnabled,

    isShowWebExtensionToast,
    showWebExtensionToast,

    isShowWordContextDetailed,
  }
}, {
  persist: {
    storage: piniaPluginPersistedstate.localStorage(),
    pick: ['isShowWebExtensionToast', 'isShowWordContextDetailed']
  }
});
