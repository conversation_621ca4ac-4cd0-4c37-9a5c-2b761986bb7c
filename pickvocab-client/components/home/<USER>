<template>
  <fwb-navbar class="max-w-screen-xl p-4 flex mx-auto">
    <template #logo>
      <div class="flex items-center">
        <NuxtLink to="/" class="flex items-center space-x-3 rtl:space-x-reverse">
          <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">pickvocab</span>
        </NuxtLink>
        <!-- Desktop navigation menu (md and up) -->
        <div class="hidden md:flex items-center ml-8">
          <NavigationMenu>
            <NavigationMenuList>
              <NavigationMenuItem>
                <NavigationMenuTrigger>Features</NavigationMenuTrigger>
                <NavigationMenuContent>
                  <div class="p-4 min-w-[300px]">
                    <NuxtLink to="/features/dictionary">
                      <NavigationMenuLink>
                        <div class="flex gap-3 hover:bg-gray-100 cursor-pointer rounded px-4 py-2 transition-colors">
                          <div class="flex-shrink-0">
                            <IconBook class="w-6 h-6" :stroke-width="1.5" />
                          </div>
                          <div class="flex flex-col">
                            <span class="font-semibold">Dictionary</span>
                            <span class="text-sm text-gray-500 font-normal mt-0.5">AI-powered English dictionary</span>
                          </div>
                        </div>
                      </NavigationMenuLink>
                    </NuxtLink>
                    <NuxtLink to="/features/book-reader">
                      <NavigationMenuLink>
                        <div class="flex gap-3 hover:bg-gray-100 cursor-pointer rounded px-4 py-2 transition-colors">
                          <div class="flex-shrink-0">
                            <IconBook2 class="w-6 h-6" :stroke-width="1.5" />
                          </div>
                          <div class="flex flex-col">
                            <span class="font-semibold">Book Reader</span>
                            <span class="text-sm text-gray-500 font-normal mt-0.5">Read books with integrated AI dictionary</span>
                          </div>
                        </div>
                      </NavigationMenuLink>
                    </NuxtLink>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>
              <!-- Add more NavigationMenuItem here for future features -->
            </NavigationMenuList>
          </NavigationMenu>
        </div>
      </div>
    </template>
    <template #default="{ isShowMenu }">
      <fwb-navbar-collapse :is-show-menu="isShowMenu">
        <!-- Mobile megamenu (md:hidden) using shadcn-vue Accordion -->
        <div class="flex flex-col md:hidden gap-2 w-full mb-2">
          <Accordion type="single" collapsible>
            <AccordionItem value="features">
              <AccordionTrigger class="w-full px-4 py-3 rounded-lg bg-gray-50 font-medium text-gray-900">
                Features
              </AccordionTrigger>
              <AccordionContent class="w-full">
                <NuxtLink to="/features/dictionary" class="block w-full">
                  <div class="flex gap-3 items-center hover:bg-gray-100 cursor-pointer rounded px-4 py-2 transition-colors">
                    <IconBook class="w-6 h-6" :stroke-width="1.5" />
                    <div class="flex flex-col">
                      <span class="font-semibold">Dictionary</span>
                      <span class="text-sm text-gray-500 font-normal mt-0.5">AI-powered contextual English dictionary</span>
                    </div>
                  </div>
                </NuxtLink>
                <NuxtLink to="/features/book-reader" class="block w-full">
                  <div class="flex gap-3 items-center hover:bg-gray-100 cursor-pointer rounded px-4 py-2 transition-colors">
                    <IconBook2 class="w-6 h-6" :stroke-width="1.5" />
                    <div class="flex flex-col">
                      <span class="font-semibold">Book Reader</span>
                      <span class="text-sm text-gray-500 font-normal mt-0.5">Read books with integrated AI dictionary</span>
                    </div>
                  </div>
                </NuxtLink>
                <!-- Add more feature links here if needed -->
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
        <div class="flex flex-col mt-4 md:hidden space-y-2">
          <a :href="appUrl">
            <Button
              class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
              Get started →
            </Button>
          </a>
          <a :href="demoUrl">
            <Button
              variant="outline"
              class="w-full bg-white hover:bg-gray-100 text-gray-800 font-medium px-6 py-3 rounded-lg border border-gray-200 transition-all duration-200">
              Demo
            </Button>
          </a>
        </div>
      </fwb-navbar-collapse>
    </template>
    <template #right-side>
      <a :href="appUrl">
        <Button
          class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
          Get started →
        </Button>
      </a>
      <a :href="demoUrl">
        <Button
          variant="outline"
          class="bg-white hover:bg-gray-100 text-gray-800 font-medium px-6 py-3 rounded-lg border border-gray-200 transition-all duration-200 ml-2">
          Demo
        </Button>
      </a>
    </template>
  </fwb-navbar>
</template>

<script setup lang="ts">
import { FwbNavbar, FwbNavbarCollapse, FwbButton } from 'flowbite-vue';
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuTrigger,
  NavigationMenuContent,
  NavigationMenuLink,
} from '@/components/ui/navigation-menu';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';
// @ts-ignore
import IconBook from '@tabler/icons-vue/dist/esm/icons/IconBook.mjs';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
const appUrl = '/app';
const demoUrl = '/demo';
</script>

<style scoped></style> 