<script setup lang="ts">
import { ref, useTemplateRef, onMounted, nextTick } from 'vue';
import { IconSearch, IconCheck } from '@tabler/icons-vue';
import { slugifyText } from '~/utils/slugifyText';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import {
  Combobox,
  ComboboxAnchor,
  ComboboxInput,
  ComboboxList,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
  type AcceptableValue,
} from '@/components/ui/combobox';
import { watchDebounced, useResizeObserver } from '@vueuse/core';
import { uniq } from 'lodash-es';
import { RemoteDictionaryApi } from '@/api/dictionary/remote';
import Spinner from '@/components/app/utils/Spinner.vue';

const router = useRouter();
const authStore = useAuthStore();

const appUrl = '/app';
const typingValue = ref('');
const isComboboxOpen = ref(false);
const suggestionOptions = ref<string[]>([]);
const isLoadingSuggestions = ref(false);
let currentApiController: AbortController | undefined = undefined;
const remoteApi = new RemoteDictionaryApi();

const searchInput = useTemplateRef<any>('search-input');
const comboboxAnchor = useTemplateRef<any>('combobox-anchor');
const comboboxListStyle = ref<{ width?: string }>({});

onMounted(() => {
  let targetForResizeObserverFn = () => comboboxAnchor.value?.$el;

  if (searchInput.value?.$el) {
    targetForResizeObserverFn = () => searchInput.value?.$el;
  }

  if (searchInput.value) {
    const elToFocus = searchInput.value.$el || searchInput.value;
    if (typeof elToFocus.focus === 'function') {
      elToFocus.focus();
    }
  }

  useResizeObserver(targetForResizeObserverFn, (entries) => {
    const [entry] = entries;
    if (entry && entry.target) {
      const width = (entry.target as HTMLElement).getBoundingClientRect().width;
      if (width > 0) {
        comboboxListStyle.value = { width: `${width}px` };
      }
    }
  });

  nextTick(() => {
    const elForWidthCalc = searchInput.value?.$el || comboboxAnchor.value?.$el;
    if (elForWidthCalc && typeof elForWidthCalc.getBoundingClientRect === 'function') {
      const rect = elForWidthCalc.getBoundingClientRect();
      const width = rect.width;
      if (width > 0 && comboboxListStyle.value.width !== `${width}px`) {
        if (!comboboxListStyle.value.width || parseFloat(comboboxListStyle.value.width) === 0) {
            comboboxListStyle.value = { width: `${width}px` };
        }
      }
    }
  });
});

watchDebounced(typingValue, async (newText) => {
  if (newText.trim() === '') {
    suggestionOptions.value = [];
    isComboboxOpen.value = false;
    isLoadingSuggestions.value = false;
    return;
  }

  if (currentApiController) {
    currentApiController.abort();
  }
  currentApiController = new AbortController();

  suggestionOptions.value = [newText];
  isComboboxOpen.value = true;
  isLoadingSuggestions.value = true;
  try {
    const fetchedSuggestions = await remoteApi.search(newText, currentApiController.signal);
    suggestionOptions.value = uniq([
      newText,
      ...fetchedSuggestions
    ].filter(s => s && s.trim() !== ''));
  } finally {
    isLoadingSuggestions.value = false;
  }
}, { debounce: 300 });


function trackLookupWordEvent (text: string) {
  const { gtag } = useGtag(); 
   gtag('event', 'landing_page_lookup_word', {
     'username': authStore.user?.username,
     'email': authStore.user?.email,
     'text': text,
   });
}

function submitLookup() {
  const termToSearch = typingValue.value.trim();

  if (termToSearch === '') {
    return;
  }

  trackLookupWordEvent(termToSearch);
  isComboboxOpen.value = false;
  router.push({ name: 'app-dictionary', query: { word: termToSearch } });
}

function handleComboboxEnter(event: KeyboardEvent) {
  // If the Combobox component handled the Enter key (e.g., to select a highlighted item),
  // it likely called event.preventDefault(). In that case, selectOption will be triggered
  // by the component, which will then call submitLookup. So, we do nothing here.
  if (event.defaultPrevented) {
    return;
  }

  // If event.defaultPrevented is false, the Enter key is for submitting the current input text.
  // submitLookup() will also handle closing the combobox (isComboboxOpen.value = false).
  if (typingValue.value.trim() !== '') {
    submitLookup();
  }
}

function selectOption(value: AcceptableValue) {
  if (typeof value === 'string') {
    typingValue.value = value;
    isComboboxOpen.value = false;
    submitLookup();
  } else {
    console.warn('Combobox emitted a non-string value:', value);
    isComboboxOpen.value = false;
  }
}

</script>

<template>
  <div class="flex items-center max-w-xl w-full relative">
    <Combobox
      v-model="typingValue"
      v-model:open="isComboboxOpen"
      :ignore-filter="true"
      class="relative w-full"
      @update:model-value="selectOption"
    >
      <ComboboxAnchor ref="combobox-anchor" class="relative w-full">
        <ComboboxInput
          v-model="typingValue"
          type="text"
          id="home-search-input"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
          placeholder="Enter any word, idiom or complex expression..."
          ref="search-input"
          autocomplete="off"
          @keydown.enter="handleComboboxEnter($event)"
        />
      </ComboboxAnchor>

      <ComboboxList
        v-show="isComboboxOpen && suggestionOptions.length > 0"
        class="absolute z-10 mt-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg"
        :style="comboboxListStyle"
        position="popper"
        align="start"
        :side-offset="4"
      >
        <div class="max-h-60 overflow-auto p-1">
          <ComboboxGroup>
            <ComboboxItem
              v-for="option in suggestionOptions"
              :key="option"
              :value="option"
              class="cursor-pointer select-none relative py-2 pl-8 pr-2 text-gray-900 dark:text-white data-[highlighted]:bg-blue-100 dark:data-[highlighted]:bg-blue-600 data-[highlighted]:text-blue-700 dark:data-[highlighted]:text-white rounded-md flex items-center text-sm"
            >
              <ComboboxItemIndicator class="absolute left-2 flex items-center justify-center">
                <IconCheck class="h-4 w-4" />
              </ComboboxItemIndicator>
              <span class="block truncate">{{ option }}</span>
            </ComboboxItem>
            <ComboboxItem
              v-if="isLoadingSuggestions"
              value="__loading__"
              class="cursor-default select-none relative py-2 pl-8 pr-2 text-gray-400 dark:text-gray-400 rounded-md flex items-center text-sm"
              disabled
            >
              <span class="flex items-center justify-center w-full">
                <Spinner :show-message="false" size="6" />
                <span class="ml-2 text-gray-400">Loading...</span>
              </span>
            </ComboboxItem>
          </ComboboxGroup>
        </div>
      </ComboboxList>
    </Combobox>

    <div
      @click="submitLookup"
      class="p-2.5 ms-2 text-sm cursor-pointer font-medium text-white bg-blue-700 rounded-lg border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 flex-shrink-0"
    >
      <IconSearch stroke-width="2" class="w-5 h-5" />
      <span class="sr-only">Search</span>
    </div>
  </div>
</template>

<style scoped></style>
