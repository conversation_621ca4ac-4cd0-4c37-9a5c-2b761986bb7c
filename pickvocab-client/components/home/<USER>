<script setup lang="ts">
import { FwbJumbotron, FwbBadge } from "flowbite-vue";
import HomeSearchBar from "./HomeSearchBar.vue";
</script>

<template>
  <div>
    <fwb-jumbotron header-text="AI-powered English Dictionary"
      sub-text="Finally, a dictionary that can look up anything">
      <div class="flex flex-col space-y-4 sm:flex-row sm:justify-center sm:space-y-0">
        <HomeSearchBar></HomeSearchBar>
      </div>
    </fwb-jumbotron>
    <div class="flex items-center justify-center">
      <fwb-badge type="green" size="sm">Word</fwb-badge>
      <fwb-badge type="yellow" size="sm">Collocation</fwb-badge>
      <fwb-badge type="purple" size="sm">Idiom</fwb-badge>
      <fwb-badge type="pink" size="sm">Expression</fwb-badge>
    </div>
  </div>
</template>

<style scoped></style>
