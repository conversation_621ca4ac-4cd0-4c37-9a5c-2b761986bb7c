<script setup lang="ts">
import { watchDebounced } from '@vueuse/core';
import type { Modal } from 'flowbite';
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
// @ts-ignore
import IconPointer from '@tabler/icons-vue/dist/esm/icons/IconPointer.mjs';
// @ts-ignore
import IconSparkles from '@tabler/icons-vue/dist/esm/icons/IconSparkles.mjs';
// @ts-ignore
import IconScale from '@tabler/icons-vue/dist/esm/icons/IconScale.mjs';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';
// @ts-ignore
import IconBooks from '@tabler/icons-vue/dist/esm/icons/IconBooks.mjs';
import { CardType } from '~/utils/card';

const store = useAppStore();
const authStore = useAuthStore();
const { isShowSearchModal } = storeToRefs(store);

const router = useRouter();
const searchText = ref('');
const searchInput = ref(null);

const suggestedCards: Ref<Card[]> = ref([]);
const suggestedDecks: Ref<Deck[]> = ref([]);
const suggestedDictionaryWords: Ref<string[]> = ref([]);
const suggestedWordsComparison: Ref<string[]> = ref([]);
const selectedItemIdx = ref(0);
const wordRefs = ref([]);
const compareRefs = ref([]);
const cardRefs = ref([]);
const deckRefs = ref([]);

let modal: Modal | undefined;

onMounted(() => {
  useFlowbite(({ Modal }) => {
    const $targetEl = document.getElementById('search-modal');
    modal = new Modal($targetEl);

    modal!.updateOnShow(async () => {
      window.addEventListener('keydown', handleKeyPress, true);
    });

    modal!.updateOnHide(() => {
      store.hideSearchModal();
      window.removeEventListener('keydown', handleKeyPress, true);
    });
  });
});

function trackLookupWordEvent(text: string) {
  const { gtag } = useGtag();

  gtag('event', 'app_lookup_word', {
    'username': authStore.user?.username,
    'email': authStore.user?.email,
    'text': text,
  });
}

watch(isShowSearchModal, async (value) => {
  if (value === true) {
    searchText.value = '';
    selectedItemIdx.value = 0;
    nextTick(() => (searchInput.value as any).focus());
    modal!.show();
  } else {
    modal!.hide();
  }
});

let currentController: AbortController | undefined = undefined;

watchDebounced(searchText, async () => {
  if (searchText.value === '') {
    suggestedDictionaryWords.value = [];
    suggestedWordsComparison.value = [];
    suggestedCards.value = [];
    suggestedDecks.value = [];
  } else {
    if (currentController) {
      currentController.abort();
    }
    currentController = new AbortController();

    suggestedDictionaryWords.value = [`Lookup "${searchText.value}"`];
    const suggestedWords = await store.searchWords(searchText.value, currentController.signal);
    suggestedDictionaryWords.value = suggestedDictionaryWords.value.concat(suggestedWords.slice(0, 5));
    suggestedWordsComparison.value = [`Compare "${searchText.value}" with...`];
    suggestedCards.value = await store.searchGenericCards(searchText.value, currentController.signal);
    suggestedDecks.value = await store.searchDecks(searchText.value, currentController.signal);
    if (selectedItemIdx.value >=
      suggestedDictionaryWords.value.length +
      suggestedWordsComparison.value.length +
      suggestedCards.value.length +
      suggestedDecks.value.length
    ) {
      selectedItemIdx.value = 0;
    }
  }
}, {
  debounce: 300
});

watch(selectedItemIdx, () => {
  if (selectedItemIdx.value < suggestedDictionaryWords.value.length) {
    (wordRefs.value[selectedItemIdx.value] as any).scrollIntoView(false);
  } else if (selectedItemIdx.value < suggestedDictionaryWords.value.length + suggestedWordsComparison.value.length) {
    (compareRefs.value[selectedItemIdx.value - suggestedDictionaryWords.value.length] as any).scrollIntoView(false);
  } else if (selectedItemIdx.value <
    suggestedDictionaryWords.value.length + suggestedWordsComparison.value.length + suggestedCards.value.length) {
    (cardRefs.value[
      selectedItemIdx.value
      - suggestedDictionaryWords.value.length
      - suggestedWordsComparison.value.length
    ] as any).scrollIntoView(false);
  } else {
    (deckRefs.value[
      selectedItemIdx.value
      - suggestedDictionaryWords.value.length
      - suggestedWordsComparison.value.length
      - suggestedCards.value.length
    ] as any).scrollIntoView(false);
  }
});

function handleKeyPress(e: KeyboardEvent) {
  if (e.key === 'ArrowUp') {
    e.stopPropagation();
    e.preventDefault();
    selectedItemIdx.value = Math.max(0, selectedItemIdx.value - 1);
  } else if (e.key === 'ArrowDown') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedDictionaryWords.value.length + suggestedWordsComparison.value.length
      + suggestedCards.value.length + suggestedDecks.value.length > 0) {
      selectedItemIdx.value = Math.min(
        suggestedDictionaryWords.value.length
        + suggestedWordsComparison.value.length
        + suggestedCards.value.length
        + suggestedDecks.value.length
        - 1,
        selectedItemIdx.value + 1
      );
    }
  } else if (e.key === 'Enter') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedDictionaryWords.value.length > 0
      || suggestedWordsComparison.value.length > 0
      || suggestedCards.value.length > 0
      || suggestedDecks.value.length > 0) {
      onEnter();
    }
  }
}

function onEnter() {
  if (selectedItemIdx.value < suggestedDictionaryWords.value.length) {
    onEnterDictionary(selectedItemIdx.value);
  } else if (selectedItemIdx.value < suggestedDictionaryWords.value.length + suggestedWordsComparison.value.length) {
    onEnterComparison();
  } else if (selectedItemIdx.value <
    suggestedDictionaryWords.value.length + suggestedWordsComparison.value.length + suggestedCards.value.length) {
    onEnterCard((suggestedCards.value[
      selectedItemIdx.value - suggestedDictionaryWords.value.length - suggestedWordsComparison.value.length
    ]));
  } else {
    onEnterCardList(suggestedDecks.value[
      selectedItemIdx.value - suggestedDictionaryWords.value.length
      - suggestedWordsComparison.value.length - suggestedCards.value.length
    ]);
  }
}

function onEnterDictionary(idx: number) {
  store.hideSearchModal();
  if (idx === 0) {
    trackLookupWordEvent(searchText.value);
    router.push({ name: 'app-dictionary', query: { word: searchText.value } });
  } else {
    trackLookupWordEvent(suggestedDictionaryWords.value[idx]);
    router.push({ name: 'app-dictionary', query: { word: suggestedDictionaryWords.value[idx] } });
  }
}

function onEnterComparison() {
  store.hideSearchModal();
  store.showCompareModal(searchText.value);
}

function onEnterCard(card: Card) {
  store.hideSearchModal();
  router.push({ name: 'app-cards-slug-id', params: { id: (card as any).id } });
}

function onEnterCardList(cardList: Deck) {
  store.hideSearchModal();
  router.push({ name: 'app-notebooks-slug-id', params: { id: (cardList as any).id } });
}

function goToContextLookup() {
  store.hideSearchModal();
  router.push({ name: 'app-contextual-meaning-id', params: { id: 2 } });
}

function goToReaderFeature() {
  store.hideSearchModal();
  router.push({ name: 'app-reader' });
}

</script>

<template>
  <!-- Main modal -->
  <div id="search-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <!-- Modal header -->
        <div class="flex items-center md:px-5 px-3 py-2 border-b rounded-t dark:border-gray-600">
          <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
              d="m21 21-3.5-3.5M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z" />
          </svg>
          <input v-model="searchText" type="text"
            class="border-none outline-none focus:outline-none focus:ring-0 text-gray-700 w-full rounded-lg"
            placeholder="Search" ref="searchInput" />
        </div>

        <div class="pb-4">
          <div class="mt-4 h-[520px] overflow-y-auto">
            <div class="px-4 md:px-5" v-if="searchText.length === 0">
              <div @click="goToContextLookup" class="rounded-lg border border-gray-200 p-4 shadow cursor-pointer hover:bg-gray-200">
                <div class="flex items-center font-semibold text-gray-800">
                  <icon-pointer class="inline-block w-4 h-4 mr-2"></icon-pointer>
                  <span>Contextual Lookup</span>
                </div>
                <div class="text-xs text-gray-500 mt-1">Get exact definition of a word or phrase in a passage</div>
              </div>

              <div @click="goToReaderFeature" class="mt-4 rounded-lg border border-gray-200 p-4 shadow cursor-pointer hover:bg-gray-200">
                <div class="flex items-center font-semibold text-gray-800">
                  <icon-books class="inline-block w-4 h-4 mr-2"></icon-books>
                  <span>Read Books</span>
                  <span
                    class="ml-2 bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-green-400 border border-green-400">
                    New
                  </span>
                </div>
                <div class="text-xs text-gray-500 mt-1">Read English books with our integrated dictionary for instant word lookups.</div>
              </div>
            </div>
            <div class="mt-4" v-if="searchText.length > 0">
              <div class="px-4 md:px-5" v-if="suggestedDictionaryWords.length > 0">
                <div class="flex items-center text-gray-600">
                  <icon-sparkles class="inline-block w-4 h-4 mr-2"></icon-sparkles>
                  <p class="text-sm font-medium dark:text-gray-400">AI Dictionary</p>
                </div>
                <ul class="my-4 space-y-3">
                  <li v-for="(word, idx) in suggestedDictionaryWords" ref="wordRefs"
                    class="cursor-pointer p-3 text-base font-medium text-gray-600 rounded-lg hover:bg-gray-200 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
                    :class="{ 'bg-gray-200': idx === selectedItemIdx, 'bg-gray-50': idx !== selectedItemIdx }"
                    @click="onEnterDictionary(idx)">
                    <div class="flex">
                      <div class="flex-1">
                        <div class="flex items-center ms-3">
                          <span class="">{{ word }}</span>
                        </div>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
              <div class="px-4 md:px-5" v-if="suggestedWordsComparison.length > 0">
                <div class="flex items-center text-gray-600">
                  <icon-scale class="inline-block w-4 h-4 mr-2"></icon-scale>
                  <p class="text-sm font-medium dark:text-gray-400">Words comparison</p>
                </div>
                <ul class="my-4 space-y-3">
                  <li v-for="(label, idx) in suggestedWordsComparison" ref="compareRefs"
                    class="cursor-pointer p-3 text-base font-medium text-gray-600 rounded-lg hover:bg-gray-200 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
                    :class="{ 'bg-gray-200': idx + suggestedDictionaryWords.length === selectedItemIdx, 'bg-gray-50': idx + suggestedDictionaryWords.length !== selectedItemIdx }"
                    @click="onEnterComparison()">
                    <div class="flex">
                      <div class="flex-1">
                        <div class="flex items-center ms-3">
                          <span class="">{{ label }}</span>
                        </div>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
              <div class="px-4 md:px-5" v-if="suggestedCards.length > 0">
                <div class="flex items-center text-gray-600">
                  <icon-cards class="inline-block w-4 h-4 mr-2"></icon-cards>
                  <p class="text-sm font-medium dark:text-gray-400">Your cards</p>
                </div>
                <ul class="my-4 space-y-3">
                  <li v-for="(card, idx) in suggestedCards" ref="cardRefs"
                    class="cursor-pointer p-3 text-base font-bold text-gray-600 rounded-lg hover:bg-gray-200 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
                    :class="{
                      'bg-gray-200': idx + suggestedDictionaryWords.length + suggestedWordsComparison.length === selectedItemIdx,
                      'bg-gray-50': idx + suggestedDictionaryWords.length + suggestedWordsComparison.length !== selectedItemIdx
                    }" @click="onEnterCard(card)">
                    <div class="flex">
                      <div class="flex-1">
                        <div v-if="card.cardType === CardType.DefinitionCard">
                      <div class="flex items-center ms-3">
                        <span>
                          <span>{{ card.word }}</span>
                          <span class="ml-1 inline-block text-xs border rounded-xl border-blue-400 text-blue-400 px-2 align-text-top"
                            v-if="card.definition.partOfSpeech"
                            :class="stylesForPartOfSpeech(card.definition.partOfSpeech)">{{ card.definition.partOfSpeech }}</span>
                        </span>
                      </div>
                      <p class="ms-3 font-normal text-gray-500" v-if="card.definition.definition">{{ card.definition.definition }}</p>
                    </div>
                    <div v-else>
                      <div class="flex items-center ms-3">
                        <span>
                          <span>{{ card.wordInContext.word }}</span>
                          <span class="ml-1 inline-block text-xs border rounded-xl border-blue-400 text-blue-400 px-2 align-text-top"
                            v-if="card.wordInContext.definition?.partOfSpeech"
                            :class="stylesForPartOfSpeech(card.wordInContext.definition.partOfSpeech)">{{ card.wordInContext.definition.partOfSpeech }}</span>
                        </span>
                      </div>
                      <p class="ms-3 font-normal text-gray-500" v-if="card.wordInContext.definition?.definition">{{ card.wordInContext.definition.definition }}</p>
                    </div>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
              <div class="px-4 md:px-5" v-if="suggestedDecks.length > 0">
                <div class="flex items-center text-gray-600">
                  <icon-book2 class="inline-block w-4 h-4 mr-2"></icon-book2>
                  <p class="text-sm font-medium dark:text-gray-400">Your notebooks</p>
                </div>
                <ul class="my-4 space-y-3">
                  <li v-for="(cardList, idx) in suggestedDecks" ref="deckRefs"
                    class="cursor-pointer p-3 text-base font-bold text-gray-600 rounded-lg hover:bg-gray-200 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
                    :class="{
                      'bg-gray-200': idx + suggestedDictionaryWords.length +
                        suggestedWordsComparison.length + suggestedCards.length === selectedItemIdx,
                      'bg-gray-50': idx + suggestedDictionaryWords.length +
                        suggestedWordsComparison.length + suggestedCards.length !== selectedItemIdx
                    }" @click="onEnterCardList(cardList)">
                    <div class="flex">
                      <div class="flex-1 ms-3">
                        <span class="">{{ cardList.name }}</span>
                        <p class="font-normal text-gray-500" v-if="cardList.description">{{
                          cardList.description }}</p>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <!-- <div v-else class="px-4 md:px-5">
              <div class="text-xs text-gray-500">
                <p>You can use the dictionary in two ways:</p>
              </div>
              <div class="mt-4 rounded-lg border border-gray-200 p-4 shadow-sm">
                <div>
                  <div class="flex items-center font-semibold text-gray-800">
                    <icon-search class="inline-block w-4 h-4 mr-2"></icon-search>
                    <span>Single Word/Phrase Lookup</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">Search definition of a specific word or phrase</div>
                </div>
                <div class="mt-6">
                  <div class="flex items-center text-gray-500">
                    <div class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 mr-2 text-primary text-sm font-medium">1</div>
                    <span class="text-xs">Type any word or phrase directly into the search box above to get its definition</span>
                  </div>
                </div>
              </div>
              <div class="mt-4 rounded-lg border border-gray-200 p-4 shadow-sm">
                <div>
                  <div class="flex items-center font-semibold text-gray-800">
                    <icon-pointer class="inline-block w-4 h-4 mr-2"></icon-pointer>
                    <span>Contextual Lookup</span>
                    <span class="ml-2 bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-green-400 border border-green-400">
                      Beta
                    </span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">Get exact definition of a word or phrase in a passage</div>
                </div>
                <div class="mt-6">
                  <div class="flex items-center text-gray-500">
                    <div class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 mr-2 text-primary text-sm font-medium">1</div>
                    <span class="text-xs">Paste or type your passage into the search box above</span>
                  </div>
                  <div class="flex items-center text-gray-500 mt-3">
                    <div class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 mr-2 text-primary text-sm font-medium">2</div>
                    <span class="text-xs">Highlight any word or phrase to see its exact meaning in the passage</span>
                  </div>
                </div>
                <div class="mt-6 text-xs text-gray-500">
                  <span>or click
                    <span class="text-blue-700 underline cursor-pointer">this</span>
                    to see the example
                  </span>
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
