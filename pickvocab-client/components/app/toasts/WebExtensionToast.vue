<script setup lang="ts">
import { ToastAction, ToastDescription, ToastProvider, ToastRoot, ToastTitle, ToastViewport, ToastClose } from 'radix-vue';

const open = defineModel<boolean>('open', { default: false });
</script>

<template>
<ToastProvider :duration="100000">
  <ToastRoot
    v-model:open="open"
    class="bg-white rounded-md border border-gray-200 shadow-lg px-6 py-6 grid [grid-template-areas:_'title_action'_'description_action'] grid-cols-[auto_max-content] gap-x-[15px] items-center data-[state=open]:animate-in data-[state=open]:fade-in data-[state=open]:slide-in-from-bottom data-[state=closed]:animate-out data-[state=closed]:fade-out data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=cancel]:translate-x-0 data-[swipe=cancel]:transition-[transform_200ms_ease-out] data-[swipe=end]:animate-swipeOut relative"
  >
    <div class="flex flex-col">
      <div class="flex items-center">
        <img src="~/assets/chrome.webp" alt="chrome" class="w-8 h-8">
        <div class="ml-4">
          <ToastTitle class="[grid-area:_title] mb-[5px] font-semibold text-slate12 text-base text-gray-800">
            Get definitions in just one-click
          </ToastTitle>
          <ToastDescription as-child>
            <p class="font-light text-sm text-gray-600">
              Easily understand words while you browse websites with our web extension
            </p>
          </ToastDescription>
        </div>
        <ToastAction
          class="[grid-area:_action] ml-4"
          as-child
          alt-text="Install web extension"
        >
          <a
            href="https://chromewebstore.google.com/detail/pickvocab/nfhhjfaahjkjdjbkpacapdblonogknag?hl=en"
            target="_blank"
            class="cursor-pointer inline-flex items-center justify-center rounded font-medium text-xs px-[10px] leading-[25px] h-[25px] bg-green2 text-green11 shadow-[inset_0_0_0_1px] shadow-green7 hover:shadow-[inset_0_0_0_1px] hover:shadow-green8 focus:shadow-[0_0_0_2px] focus:shadow-green8">
            Install
          </a>
        </ToastAction>
      </div>
      <img src="~/assets/extension-lookup.webp" alt="extension-lookup" class="mt-4 w-full rounded">
    </div>

    <ToastClose aria-label="Close" class="absolute top-1 right-3">
      <span aria-hidden class="text-gray-500">×</span>
    </ToastClose>
  </ToastRoot>
  <ToastViewport class="[--viewport-padding:_25px] fixed bottom-0 right-0 flex flex-col p-[var(--viewport-padding)] gap-[10px] w-[500px] max-w-[100vw] m-0 list-none z-[**********] outline-none" />
</ToastProvider>
</template>

<style scoped></style>
