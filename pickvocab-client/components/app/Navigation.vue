<script setup lang="ts">
// import {
//   IconPlus, IconCards, IconHelp, IconMessageChatbot, IconBook2, IconLogin2, IconLogout2
// } from '@tabler/icons-vue';
// @ts-ignore
import IconPlus from '@tabler/icons-vue/dist/esm/icons/IconPlus.mjs';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
// @ts-ignore
import IconHelp from '@tabler/icons-vue/dist/esm/icons/IconHelp.mjs';
// @ts-ignore
import IconMessageChatbot from '@tabler/icons-vue/dist/esm/icons/IconMessageChatbot.mjs';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';
// @ts-ignore
import IconLogin2 from '@tabler/icons-vue/dist/esm/icons/IconLogin2.mjs';
// @ts-ignore
import IconLogout2 from '@tabler/icons-vue/dist/esm/icons/IconLogout2.mjs';

import Dropdown from './utils/Dropdown.vue';

const props = defineProps<{
  isDemoPage?: boolean
}>();

const store = useAppStore();
const authStore = useAuthStore();
let { user } = storeToRefs(authStore);
</script>

<template>
  <nav class="fixed top-0 z-40 w-full bg-white border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700">
    <div class="px-3 py-3 lg:px-5 lg:pl-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center justify-start rtl:justify-end">
          <button data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar"
            type="button"
            class="inline-flex items-center p-2 text-sm text-gray-500 rounded-lg sm:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600">
            <span class="sr-only">Open sidebar</span>
            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path clip-rule="evenodd" fill-rule="evenodd"
                d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z">
              </path>
            </svg>
          </button>
          <a v-if="isDemoPage" href="/demo" class="flex ms-2 md:me-24">
            <span
              class="self-center text-xl text-gray-700 font-semibold sm:text-2xl whitespace-nowrap dark:text-white">pickvocab</span>
            <span
              class="sm:hidden flex ml-2 bg-blue-100 text-blue-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded border border-blue-400 items-center">Demo</span>
            <span
              class="hidden sm:flex ml-2 bg-blue-100 text-blue-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded border border-blue-400 items-center">Demo
              Project</span>
          </a>
          <NuxtLink v-else to="/app" class="flex ms-2 md:me-24">
            <!-- <svg class="h-8 me-3 w-7 h-7 text-gray-700 dark:text-white" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10.779 17.779 4.36 19.918 6.5 13.5m4.279 4.279 8.364-8.643a3.027 3.027 0 0 0-2.14-5.165 3.03 3.03 0 0 0-2.14.886L6.5 13.5m4.279 4.279L6.499 13.5m2.14 2.14 6.213-6.504M12.75 7.04 17 11.28" />
            </svg> -->
            <span
              class="self-center text-xl text-gray-700 font-semibold sm:text-2xl whitespace-nowrap dark:text-white">pickvocab</span>
            <template v-if="isDemoPage">
              <span
                class="sm:hidden flex ml-2 bg-blue-100 text-blue-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded border border-blue-400 items-center">Demo</span>
              <span
                class="hidden sm:flex ml-2 bg-blue-100 text-blue-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded border border-blue-400 items-center">Demo
                Project</span>
            </template>
          </NuxtLink>
        </div>
        <div class="flex items-center ml-auto">
          <div class="hidden md:flex ms-3">
            <div class="relative hidden md:block min-w-[400px]">
              <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                </svg>
                <span class="sr-only">Search icon</span>
              </div>
              <input type="text" id="search-navbar" @click="store.showSearchModal()"
                class="cursor-pointer block w-full p-2 ps-10 text-xs text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 hover:border-blue-500 hover:border-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="Lookup any word, idiom or phrase... (Ctrl+K)">
            </div>
          </div>
          <div class="flex md:hidden items-center ms-3">
            <button @click="store.showSearchModal()">
              <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                  d="m21 21-3.5-3.5M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z" />
              </svg>
            </button>
          </div>
          <div class="flex items-center ms-3">
            <div>
              <button type="button" class="flex" aria-expanded="false" data-dropdown-toggle="add-dropdown">
                <icon-plus class="w-5 h-5 text-gray-800 dark:text-white"></icon-plus>
              </button>
              <div id="add-dropdown"
                class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700">
                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                  <li>
                    <div @click="store.showAddCardModal()"
                      class="cursor-pointer block pl-4 pr-8 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                      <icon-cards class="inline-block w-4 h-4 me-2"></icon-cards>
                      <span>Create card</span>
                    </div>
                  </li>
                  <li>
                    <div @click="store.showAddDeckModal()"
                      class="cursor-pointer block pl-4 pr-8 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                      <icon-book-2 class="inline-block w-4 h-4 me-2"></icon-book-2>
                      <span>Create notebook</span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <!-- <div>
              <button type="button" class="flex" aria-expanded="false" data-dropdown-toggle="dropdown-add">
                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                  fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5 12h14m-7 7V5" />
                </svg>
              </button>
            </div>
            <div
              class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600"
              id="dropdown-add">
              <ul class="py-1" role="none">
                <li>
                  <a href="#"
                    class="block px-6 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                    role="menuitem">Add word</a>
                </li>
                <li>
                  <a href="#"
                    class="block px-6 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                    role="menuitem">Add group</a>
                </li>
              </ul>
            </div> -->
          </div>

          <div class="flex items-center ms-3">
            <div>
              <button type="button" class="flex" aria-expanded="false" data-dropdown-toggle="help-dropdown">
                <icon-help class="w-5 h-5 text-gray-800 dark:text-white"></icon-help>
              </button>
              <div id="help-dropdown"
                class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700">
                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                  <!-- <li>
                    <div @click="store.showAddCardModal()"
                      class="cursor-pointer block pl-4 pr-8 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                      <icon-book class="inline-block w-4 h-4 me-2"></icon-book>
                      <span>Documentation</span>
                    </div>
                  </li>
                  <li>
                    <div @click="store.showAddCardModal()"
                      class="cursor-pointer block pl-4 pr-8 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                      <icon-binoculars class="inline-block w-4 h-4 me-2"></icon-binoculars>
                      <span>Roadmap</span>
                    </div>
                  </li> -->
                  <li>
                    <a href="https://app.youform.com/forms/oy6dfb8g" target="_blank">
                      <div
                        class="cursor-pointer block pl-4 pr-8 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                        <icon-message-chatbot class="inline-block w-4 h-4 me-2"></icon-message-chatbot>
                        <span>Send Feedback</span>
                      </div>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <button type="button" v-if="!authStore.isAuthenticated" @click="authStore.showLoginModal()"
          class="flex items-center ml-3">
          <icon-login-2 class="inline-block w-5 h-5"></icon-login-2>
          <p class="ml-2 text-xs">Sign in</p>
        </button>

        <div v-else class="flex items-center">
          <div class="flex items-center ms-3">
            <Dropdown>
              <template #trigger>
                <button type="button"
                  class="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
                  aria-expanded="false">
                  <span class="sr-only">Open user menu</span>
                  <img class="w-6 h-6 rounded-full" :src="getProfilePicture(user!)" alt="user photo">
                </button>
              </template>
              <template #body>
                <div
                  class="z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600">
                  <div class="px-4 py-3" role="none">
                    <p class="text-sm text-gray-900 dark:text-white" role="none">
                      {{ getProfileName(user!) }}
                    </p>
                    <p class="text-sm font-medium text-gray-900 truncate dark:text-gray-300" role="none">
                      {{ user?.email }}
                    </p>
                  </div>
                  <ul class="py-1" role="none">
                    <!-- <li>
                      <a href="#"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                        role="menuitem">Dashboard</a>
                    </li>
                    <li>
                      <a href="#"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                        role="menuitem">Settings</a>
                    </li>
                    <li>
                      <a href="#"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                        role="menuitem">Earnings</a>
                    </li> -->
                    <li>
                      <div @click="authStore.signOut()"
                        class="flex items-center cursor-pointer block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                        role="menuitem">
                        <icon-logout-2 class="inline-block w-5 h-5"></icon-logout-2>
                        <span class="ml-2">Sign out</span>
                      </div>
                    </li>
                  </ul>
                </div>
              </template>
            </Dropdown>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<style scoped></style>