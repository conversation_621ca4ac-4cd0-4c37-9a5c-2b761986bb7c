<script setup lang="ts">
const emit = defineEmits(["primary-btn-click", "secondary-btn-click"]);
const props = defineProps<{
  label: string;
  message?: string;
  primaryBtnLabel?: string;
  secondaryBtnLabel?: string;
}>();
</script>

<template>
  <div
    id="alert-additional-content-2"
    class="p-4 mb-4 text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800"
    role="alert"
  >
    <div class="flex items-center">
      <svg
        class="flex-shrink-0 w-4 h-4 me-2"
        aria-hidden="true"
        xmlns="http://www.w3.org/2000/svg"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path
          d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"
        />
      </svg>
      <h3 class="text-lg font-medium">{{ label }}</h3>
    </div>
    <div class="mt-2 text-sm" v-if="message">
      <p>{{ message }}</p>
    </div>
    <div class="flex mt-4">
      <button
        type="button"
        v-if="primaryBtnLabel"
        @click="$emit('primary-btn-click')"
        class="text-white bg-red-800 hover:bg-red-900 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-xs px-3 py-1.5 me-2 text-center inline-flex items-center dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800"
      >
        {{ primaryBtnLabel }}
      </button>
      <button
        type="button"
        v-if="secondaryBtnLabel"
        @click="$emit('secondary-btn-click')"
        class="text-red-800 bg-transparent border border-red-800 hover:bg-red-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:hover:bg-red-600 dark:border-red-600 dark:text-red-500 dark:hover:text-white dark:focus:ring-red-800"
      >
        {{ secondaryBtnLabel }}
      </button>
    </div>
  </div>
</template>

<style scoped></style>
