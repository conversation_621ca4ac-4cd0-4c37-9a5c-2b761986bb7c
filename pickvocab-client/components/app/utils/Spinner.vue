<script setup lang="ts">
import { FwbSpinner } from 'flowbite-vue';
import type { SpinnerSize } from 'flowbite-vue/components/FwbSpinner/types.js';
import { getRandomLoadingMessage } from '~/utils/loadingMessages';

const props = withDefaults(defineProps<{ showMessage?: boolean, size?: SpinnerSize | undefined, message?: string }>(), {
  showMessage: true,
  size: '8'
});

const loadingMessage = await useAsyncData('loadingMessage', () => new Promise((resolve) => {
  resolve(getRandomLoadingMessage());
})).data;
</script>

<template>
  <div role="status" class="flex flex-col items-center justify-center" v-if="props.showMessage">
    <FwbSpinner :size="size"></FwbSpinner>
    <span class="mt-4 text-gray-400" v-if="props.message !== undefined">{{ props.message }}</span>
    <span class="mt-4 text-gray-400" v-else>{{ loadingMessage }}</span>
  </div>
  <div v-else>
    <FwbSpinner :size="size"></FwbSpinner>
  </div>
</template>

<style scoped></style>
