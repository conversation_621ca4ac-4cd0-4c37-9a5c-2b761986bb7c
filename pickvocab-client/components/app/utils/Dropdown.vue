<script setup lang="ts">
import { type DropdownOptions } from 'flowbite';

const props = withDefaults(defineProps<{ offsetSkidding?: number, offsetDistance?: number, hide?: boolean }>(), {
  offsetDistance: 0,
  offsetSkidding: 0
});

const trigger = useTemplateRef('trigger');
const body = useTemplateRef('body');
const dropdown = ref();

onMounted(() => {
  // set the dropdown menu element
  const $targetEl = body.value;

  // set the element that trigger the dropdown menu on click
  const $triggerEl = trigger.value;


  // options with default values
  const options: DropdownOptions = {
    placement: 'bottom',
    offsetSkidding: props.offsetSkidding,
    offsetDistance: props.offsetDistance,
    // onHide: () => {
    //   console.log('dropdown has been hidden');
    // },
    // onShow: () => {
    //   console.log('dropdown has been shown');
    // }
  };

  if ($targetEl) {
    /*
    * targetEl: required
    * triggerEl: required
    * options: optional
    */

    useFlowbite(({ Dropdown }) => {
      // @ts-ignore
      dropdown.value = new Dropdown($targetEl, $triggerEl, options);
    });

    // show the dropdown
  }
})

watch(() => props.hide, () => {
  if (dropdown.value) dropdown.value.hide();
});
</script>

<template>
  <div>
    <div ref="trigger">
      <slot ref="trigger" name="trigger"></slot>
    </div>
    <div ref="body" class="hidden z-50">
      <slot ref="body" name="body"></slot>
    </div>
  </div>
</template>
