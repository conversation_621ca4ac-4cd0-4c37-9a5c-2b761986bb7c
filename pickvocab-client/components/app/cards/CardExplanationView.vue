<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { CardType } from '~/utils/card';
import type { ContextCard } from '~/utils/card'; // Assuming ContextCard type exists
import { useAuthStore } from '~/stores/auth'; // Assuming path
import { useLLMStore } from '~/stores/llm'; // Assuming path
import { useAppStore } from '~/stores/app'; // Add app store import
import ExplanationHeader from '~/components/app/contextualMeaning/ExplanationHeader.vue';
import CardWordInfo from '~/components/app/cards/explanation/CardWordInfo.vue';
import CardWordSetting from '~/components/app/cards/explanation/CardWordSetting.vue';
import ExplanationSimple from '~/components/app/contextualMeaning/ExplanationSimple.vue';
import ExplanationFull from '~/components/app/contextualMeaning/ExplanationFull.vue';
// import type { LLMModel } from '~/api/llm'; // Remove or comment out if LLMModel is not directly used or correctly exported

// Define Props
const props = defineProps<{
  card: ContextCard;
}>();

// Define Emits
const emit = defineEmits<{
  (e: 'delete'): void;
}>();

// Stores
const authStore = useAuthStore();
const llmStore = useLLMStore();
const store = useAppStore();

// Local State
const isDetailed = ref(false);
const selectedSimpleViewLanguage = ref('English');
const embeddingStatus = ref<{ loading: boolean; indexed: boolean | null }>({ loading: true, indexed: null });

// Initialize isDetailed based on card data when the component mounts or card changes
watch(() => props.card, async (newCard) => {
  if (newCard) {
    isDetailed.value = !!newCard.wordInContext.definition;
    
    // Fetch embedding status
    embeddingStatus.value = { loading: true, indexed: null };
    try {
      const statusResult = await store.checkCardEmbeddingStatus(newCard.id);
      embeddingStatus.value.indexed = statusResult.has_embedding;
    } catch (error) {
      console.error('Failed to fetch embedding status:', error);
      embeddingStatus.value.indexed = null;
    } finally {
      embeddingStatus.value.loading = false;
    }
  }
}, { immediate: true });

// Computed Properties
const canToggleDetailedView = computed(() => {
  // Assumes card prop is always a ContextCard here
  return (
    !!props.card.wordInContext.definition && // Check if full definition exists
    !!props.card.wordInContext.definitionShort // Check if short definition exists
  );
});

const simpleExplanationAvailableLanguages = computed(() => {
  // Check if definitionShort exists first
  if (props.card.wordInContext.definitionShort) {
    const languagesObj = props.card.wordInContext.definitionShort.languages;
    // Check if languages object exists and has keys
    if (languagesObj && Object.keys(languagesObj).length > 0) {
      const languages = Object.keys(languagesObj);
      if (!languages.includes('English')) {
        languages.unshift('English'); // Ensure 'English' is always an option if other languages exist
      }
      return languages;
    } else {
      // If definitionShort exists but languages is missing or empty, default to English
      return ['English'];
    }
  }
  // If definitionShort itself doesn't exist, return undefined
  return undefined;
});

const llmModel = computed(() => {
  // LLM model logic specific to ContextCard
  return props.card.wordInContext.llm_model
    ? llmStore.getModelById(props.card.wordInContext.llm_model)
    : undefined;
});

// Methods
function handleDelete() {
  emit('delete');
}

</script>

<template>
  <div> <!-- Root element for the component -->
    <ExplanationHeader :word="card.wordInContext.word">
      <template #wordInfo>
        <CardWordInfo
          :llm-model-name="llmModel?.name"
          :llm-provider-logo="llmModel ? llmStore.providerMap[llmModel.provider].logo : undefined"
          :updated-at="card.updatedAt"
        />
      </template>
      <template #settings>
         <CardWordSetting
           :is-owner="card.owner === authStore.currentUser?.id"
           @delete="handleDelete"
           :show-detailed-toggle="canToggleDetailedView"
           v-model:isDetailed="isDetailed"
           :embedding-status="embeddingStatus"
         />
      </template>
    </ExplanationHeader>

    <ExplanationSimple
      v-if="!isDetailed && card.wordInContext.definitionShort"
      :word-entry="card.wordInContext"
      v-model:selected-language="selectedSimpleViewLanguage"
      :available-languages="simpleExplanationAvailableLanguages"
      class="mt-4"
      :show-context="true"
    />
    <ExplanationFull
      v-else-if="isDetailed && card.wordInContext.definition"
      :word-entry="card.wordInContext"
      class="mt-4"
      :show-context="true"
    />
    <!-- Optional: Add a placeholder or message if neither simple nor full definition is available -->
    <div v-else class="mt-4 text-gray-500 italic">
       Explanation details not available.
    </div>
  </div>
</template>

<style scoped></style> 