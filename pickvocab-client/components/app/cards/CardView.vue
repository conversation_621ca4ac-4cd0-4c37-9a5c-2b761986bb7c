<script setup lang="ts">
// import { IconDotsVertical, IconPencilPlus, IconReload, IconTrash } from '@tabler/icons-vue';
// @ts-ignore
import IconDotsVertical from '@tabler/icons-vue/dist/esm/icons/IconDotsVertical.mjs';
// @ts-ignore
import IconPencilPlus from '@tabler/icons-vue/dist/esm/icons/IconPencilPlus.mjs';
// @ts-ignore
import IconReload from '@tabler/icons-vue/dist/esm/icons/IconReload.mjs';
// @ts-ignore
import IconTrash from '@tabler/icons-vue/dist/esm/icons/IconTrash.mjs';
// @ts-ignore
import IconInfoCircleFilled from '@tabler/icons-vue/dist/esm/icons/IconInfoCircleFilled.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';
// @ts-ignore
import IconLoader2 from '@tabler/icons-vue/dist/esm/icons/IconLoader2.mjs';
import Dropdown from '~/components/app/utils/Dropdown.vue';
import FunctionalCardView from '~/components/app/cards/FunctionalCardView.vue';
import CardViewAddExampleModal from '~/components/app/cards/CardViewAddExampleModal.vue';
import { CardType } from '~/utils/card';
import DangerAlert from '~/components/app/utils/DangerAlert.vue';
import CardExplanationView from './CardExplanationView.vue';

const store = useAppStore();
const authStore = useAuthStore();
const llmStore = useLLMStore();

const route = useRoute();
const router = useRouter();
const card: Ref<Card | undefined> = ref();
const showAddExampleModal = ref(false);
const isLoading = ref(false);
const embeddingStatus = ref<{ loading: boolean; indexed: boolean | null }>({ loading: true, indexed: null });

const cardWord = computed(() => {
  if (card.value?.cardType === CardType.DefinitionCard) {
    return card.value.word;
  }
  return card.value?.wordInContext.word;
});

useSeoMeta({
  title: computed(() => cardWord.value ? `${cardWord.value} - Cards | Pickvocab` : 'Cards | Pickvocab'),
});

const llmModel = computed(() => {
  if (card.value?.cardType === CardType.DefinitionCard) {
    return card.value?.referenceWord ? llmStore.getModelById(card.value.referenceWord?.llm_model) : undefined;
  }
  return card.value?.wordInContext.llm_model
    ? llmStore.getModelById(card.value?.wordInContext.llm_model) : undefined;
});

watch(() => route.params.id, async (value) => {
  if (Array.isArray(value)) throw new Error('Unexpected');
  isLoading.value = true;
  embeddingStatus.value = { loading: true, indexed: null };
  try {
    card.value = await store.getGenericCard(Number(value));
    if (cardWord.value) {
      const slug = slugifyText(cardWord.value);
      const newRoute = router.resolve({
        params: { slug, ...route.params },
        query: route.query
      });
      window.history.replaceState('', '', newRoute.fullPath);
    }
    
    // Fetch embedding status after card is loaded
    try {
      const statusResult = await store.checkCardEmbeddingStatus(Number(value));
      embeddingStatus.value.indexed = statusResult.has_embedding;
    } catch (error) {
      console.error('Failed to fetch embedding status:', error);
      embeddingStatus.value.indexed = null;
    }
  } finally {
    isLoading.value = false;
    embeddingStatus.value.loading = false;
  }
}, { immediate: true });

async function deleteCard() {
  await store.deleteGenericCard(card.value!.id);
  router.push({ name: 'app' })
  alert('Card deleted succesfully!');
}

</script>

<template>
  <div class="flex-grow">
    <div class="sm:ml-64 pt-24 pb-10 pl-10 pr-10 xl:pr-48 h-full flex flex-col">
      <template v-if="card?.cardType === CardType.DefinitionCard">
        <FunctionalCardView :card="card" :llm-model="llmModel"
          :llm-icon="llmModel ? llmStore.providerMap[llmModel.provider].logo : undefined" class="flex-grow">
          <template #cardSettings>
            <Dropdown :offsetDistance="-10" :offsetSkidding="-60">
              <template #trigger>
                <div class="p-2 hover:rounded hover:bg-gray-100 cursor-pointer">
                  <icon-dots-vertical class="w-5 h-5"></icon-dots-vertical>
                </div>
              </template>
              <template #body>
                <div
                  class="z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600">
                  <div class="px-4 py-3 sm:hidden" role="none">
                    <div v-if="llmModel" class="flex items-center">
                      <img :src="llmStore.providerMap[llmModel.provider].logo" alt="llm-model-img" class="w-4 h-4">
                      <p class="ml-1 text-sm font-medium text-gray-900 dark:text-white" role="none">
                        {{ llmModel.name }}
                      </p>
                    </div>
                    <p v-if="card && card.createdAt" class="mt-1 text-sm text-gray-600 truncate dark:text-gray-300"
                      role="none">
                      {{ new Date(card.createdAt).toLocaleString() }}
                    </p>
                  </div>
                  <div class="px-4 py-2 border-b border-gray-100 dark:border-gray-600" role="none">
                    <div class="flex items-center">
                      <span v-if="embeddingStatus.loading" class="flex items-center">
                        <icon-loader2 class="w-4 h-4 text-blue-500 animate-spin" />
                        <p class="ml-2 text-sm text-gray-600 dark:text-gray-300" role="none">
                          Checking indexing...
                        </p>
                      </span>
                      <span v-else-if="embeddingStatus.indexed === true" class="flex items-center">
                        <icon-check class="w-4 h-4 text-green-500" />
                        <p class="ml-2 text-sm text-gray-600 dark:text-gray-300" role="none">
                          Indexed
                        </p>
                      </span>
                      <span v-else class="flex items-center">
                        <icon-x class="w-4 h-4 text-red-500" />
                        <p class="ml-2 text-sm text-gray-600 dark:text-gray-300" role="none">
                          Not Indexed
                        </p>
                      </span>
                    </div>
                  </div>
                  <ul class="py-1" role="none">
                    <li>
                      <div v-if="card.owner === authStore.currentUser?.id" @click="showAddExampleModal = true"
                        class="flex items-center px-5 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                        role="menuitem">
                        <icon-pencil-plus class="inline-block w-4 h-4"></icon-pencil-plus>
                        <span class="ml-2">Add examples</span>
                      </div>
                    </li>
                    <li>
                      <div @click="router.push({ name: 'app-dictionary', query: { fresh: 'true', word: card.word } })"
                        class="flex items-center px-5 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                        role="menuitem">
                        <icon-reload class="inline-block w-4 h-4"></icon-reload>
                        <span class="ml-2">Re-lookup</span>
                      </div>
                    </li>
                    <li>
                      <div v-if="card.owner === authStore.currentUser?.id" @click="deleteCard()"
                        class="flex items-center px-5 py-2 text-sm hover:bg-gray-100 text-red-500 cursor-pointer"
                        role="menuitem">
                        <icon-trash class="inline-block w-4 h-4"></icon-trash>
                        <span class="ml-2">Delete</span>
                      </div>
                    </li>
                  </ul>
                </div>
              </template>
            </Dropdown>
          </template>
        </FunctionalCardView>
        <CardViewAddExampleModal :card="card" v-model:show="showAddExampleModal">
        </CardViewAddExampleModal>
      </template>
      <template v-else-if="card?.cardType === CardType.ContextCard">
         <CardExplanationView
           :card="card" 
           @delete="deleteCard"
         />
      </template>
      <DangerAlert
        v-else-if="!isLoading"
        :label="!authStore.user ? 'Sign in to see this page' : 'Card not found'"
        :primary-btn-label="!authStore.user ? 'Sign in' : undefined"
        @primary-btn-click="!authStore.user ? authStore.showLoginModal() : undefined"
      />
    </div>
  </div>
</template>

<style scoped></style>
