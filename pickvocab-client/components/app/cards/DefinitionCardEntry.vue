<script setup lang="ts">
const props = defineProps<{
  card: DefinitionCard
}>();
</script>

<template>
  <div
    class="cursor-pointer block p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700">

    <h5 class="mb-2 text-lg font-semibold tracking-tight text-gray-700 dark:text-white">
      <span>{{ card.word }}</span>
      <p class="inline-block ml-2 text-sm border rounded-xl border-blue-400 text-blue-400 px-2"
        :class="stylesForPartOfSpeech(card.definition.partOfSpeech)" v-if="card.definition.partOfSpeech !== undefined">
        {{
        card.definition.partOfSpeech }}</p>
    </h5>
    <p class="font-normal text-gray-600 dark:text-gray-400" v-if="card.definition.definition !== undefined">{{
        card.definition.definition }}</p>
    <p class="mt-2 italic font-normal text-sm text-gray-600 dark:text-gray-400"
      v-if="card.definition.examples?.length !== 0">"{{
        card.definition.examples![0] }}"</p>
  </div>
</template>

<style scoped></style>
