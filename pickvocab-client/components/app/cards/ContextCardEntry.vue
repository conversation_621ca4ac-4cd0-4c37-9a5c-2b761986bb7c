<script setup lang="ts">
import type { ContextCard } from '~/utils/card';
import TiptapEditor from '~/components/editor/TiptapEditor.vue';

const props = defineProps<{
  card: ContextCard
}>();

const reducedContext: ComputedRef<{
  text: string,
  selectedText: string ,
  offset: number
}> = computed(() => {
  return reduceContext(
    props.card.wordInContext.context,
    props.card.wordInContext.word,
    props.card.wordInContext.offset,
  );
});

// Because the selected text is enclosed in double quotes,
// the offset needs to be increased by one for correct highlighting
const context = computed(() => `"${reducedContext.value.text}"`)

</script>

<template>
  <div
    class="cursor-pointer block p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700">

    <h5 class="mb-2 text-lg font-semibold tracking-tight text-gray-700 dark:text-white">
      <span>{{ card.wordInContext.word }}</span>
      <p v-if="card.wordInContext.definition"
        class="inline-block ml-2 text-sm border rounded-xl border-blue-400 text-blue-400 px-2"
        :class="stylesForPartOfSpeech(card.wordInContext.definition.partOfSpeech)">
        {{ card.wordInContext.definition.partOfSpeech }}</p>
    </h5>
    <p v-if="card.wordInContext.definition"
      class="font-normal text-gray-600 dark:text-gray-400">{{
        card.wordInContext.definition.definition }}</p>
    <!-- <p class="mt-2 italic font-normal text-sm text-gray-600 dark:text-gray-400">"{{
        card.wordInContext.context }}"</p> -->
    <blockquote class="mt-2 italic font-normal text-sm dark:text-gray-400">
        <TiptapEditor
          :text="context"
          :selected-text="reducedContext.selectedText"
          :offset="reducedContext.offset + 1"
          :css-classes="'tiptap prose prose-sm sm:prose-base lg:prose-lg xl:prose-2xl focus:outline-none w-full !text-gray-600 !bg-transparent rounded-lg'"
          :show-options="false"
          :show-bubble-menu="false"
        />
      </blockquote>
  </div>
</template>

<style scoped></style>
