<script setup lang="ts">
import Dropdown from '~/components/app/utils/Dropdown.vue';
// @ts-ignore
import IconDotsVertical from '@tabler/icons-vue/dist/esm/icons/IconDotsVertical.mjs';
// @ts-ignore
import IconTrash from '@tabler/icons-vue/dist/esm/icons/IconTrash.mjs';
// @ts-ignore
import IconInfoCircleFilled from '@tabler/icons-vue/dist/esm/icons/IconInfoCircleFilled.mjs';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';
// @ts-ignore
import IconLoader2 from '@tabler/icons-vue/dist/esm/icons/IconLoader2.mjs';
import { FwbToggle } from 'flowbite-vue';

// Define props
defineProps<{
  isOwner: boolean;
  showDetailedToggle?: boolean; // Added prop to control toggle visibility
  embeddingStatus?: { loading: boolean; indexed: boolean | null };
}>();

// Define emits
const emit = defineEmits<{
  (e: 'delete'): void;
}>();

// Define model for two-way binding with parent
const isDetailed = defineModel<boolean>('isDetailed', { default: false });

</script>

<template>
  <div class="flex items-center space-x-2"> <!-- Added flex container and spacing -->
    <FwbToggle
      v-if="showDetailedToggle"
      label="Detailed"
      v-model="isDetailed"
      class="mr-2"
    />
    <Dropdown :offsetDistance="-10" :offsetSkidding="-40">
      <template #trigger>
        <div class="p-2 hover:rounded hover:bg-gray-100 cursor-pointer">
          <icon-dots-vertical class="w-5 h-5"></icon-dots-vertical>
        </div>
      </template>
      <template #body>
        <div
          class="z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600">
          
          <!-- Card status section (visible on all screens) -->
          <div v-if="embeddingStatus" class="px-4 py-2 border-b border-gray-100 dark:border-gray-600" role="none">
            <div class="flex items-center">
              <span v-if="embeddingStatus.loading" class="flex items-center">
                <icon-loader2 class="w-4 h-4 text-blue-500 animate-spin" />
                <p class="ml-2 text-sm text-gray-600 dark:text-gray-300" role="none">
                  Checking indexing...
                </p>
              </span>
              <span v-else-if="embeddingStatus.indexed === true" class="flex items-center">
                <icon-check class="w-4 h-4 text-green-500" />
                <p class="ml-2 text-sm text-gray-600 dark:text-gray-300" role="none">
                  Indexed
                </p>
              </span>
              <span v-else class="flex items-center">
                <icon-x class="w-4 h-4 text-red-500" />
                <p class="ml-2 text-sm text-gray-600 dark:text-gray-300" role="none">
                  Not Indexed
                </p>
              </span>
            </div>
          </div>
          
          <ul class="py-1" role="none">
            <li>
              <div v-if="isOwner" @click="emit('delete')"
                class="flex items-center px-5 py-2 text-sm hover:bg-gray-100 text-red-500 cursor-pointer"
                role="menuitem">
                <icon-trash class="inline-block w-4 h-4"></icon-trash>
                <span class="ml-2">Delete</span>
              </div>
              <!-- Add a placeholder if not owner or no actions available? Or just render nothing. -->
               <div v-else class="px-5 py-2 text-sm text-gray-400">
                 No actions available
               </div>
            </li>
            <!-- Other potential settings items could go here -->
          </ul>
        </div>
      </template>
    </Dropdown>
  </div>
</template>

<style scoped>
/* Add specific styles if needed */
</style> 