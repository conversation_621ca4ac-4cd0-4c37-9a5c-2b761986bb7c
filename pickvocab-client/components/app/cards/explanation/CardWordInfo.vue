<script setup lang="ts">
import { FwbTooltip } from 'flowbite-vue';
// @ts-ignore
import IconInfoCircleFilled from '@tabler/icons-vue/dist/esm/icons/IconInfoCircleFilled.mjs';

// Define props required by the tooltip content
defineProps<{
  llmModelName?: string;
  llmProviderLogo?: string;
  updatedAt?: string | Date;
}>();

// Helper to check if any info is available to display
function hasInfo(props: { llmModelName?: string; llmProviderLogo?: string; updatedAt?: string | Date }) {
  return props.llmModelName || props.llmProviderLogo || props.updatedAt;
}
</script>

<template>
  <FwbTooltip v-if="hasInfo($props)" class="hidden sm:block" theme="dark">
    <template #trigger>
      <icon-info-circle-filled
        class="inline-block text-gray-400 w-5 h-5 cursor-pointer hover:text-gray-600"></icon-info-circle-filled>
    </template>
    <template #content>
      <div class="flex items-center px-3 py-2 border font-medium text-sm rounded-lg shadow-sm bg-white text-gray-900 dark:text-white">
        <span v-if="llmProviderLogo && llmModelName" class="flex items-center">
          <img :src="llmProviderLogo" alt="llm-model-img" class="w-4 h-4">
          <span class="ml-1">{{ llmModelName }}&nbsp;</span>
        </span>
        <span v-if="updatedAt">{{ new Date(updatedAt).toLocaleString() }}</span>
      </div>
    </template>
  </FwbTooltip>
</template>

<style scoped>
/* Add specific styles if needed */
</style> 