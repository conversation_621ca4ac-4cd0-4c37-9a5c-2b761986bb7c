<script setup lang="ts">
// import { IconReload, IconCards, IconDotsVertical, IconScale, IconBookmark } from '@tabler/icons-vue';
// @ts-ignore
import IconReload from '@tabler/icons-vue/dist/esm/icons/IconReload.mjs';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
// @ts-ignore
import IconDotsVertical from '@tabler/icons-vue/dist/esm/icons/IconDotsVertical.mjs';
// @ts-ignore
import IconScale from '@tabler/icons-vue/dist/esm/icons/IconScale.mjs';
// @ts-ignore
import IconBookmark from '@tabler/icons-vue/dist/esm/icons/IconBookmark.mjs';
import { FwbTooltip, FwbSelect } from 'flowbite-vue';
import * as locale from 'locale-codes';
import { Dictionary, type DefinitionDetails, type DictionarySource, type WordEntry } from 'pickvocab-dictionary';
import { RemoteDictionaryApi } from '~/api/dictionary/remote';
import Dropdown from '~/components/app/utils/Dropdown.vue';
import Spinner from '~/components/app/utils/Spinner.vue';
import DictionaryWordViewInfoAlert from '~/components/app/dictionary/DictionaryWordViewInfoAlert.vue';
import DictionaryWordViewErrorAlert from '~/components/app/dictionary/DictionaryWordViewErrorAlert.vue';
import { stylesForPartOfSpeech } from '~/utils/utils';
import { languages } from '~/utils/languages';

const store = useAppStore();
const llmStore = useLLMStore();

const route = useRoute();
const router = useRouter();
const wordEntry: Ref<WordEntry | undefined> = ref();
const isDefinitionForLanguageLoading = ref(false);
const isMoreExamplesLoading = ref(false);
const isMoreSynonymsLoading = ref(false);
const errorMessage = ref('');
const selectedLanguage = ref<string | undefined>('English');
const fromCache = ref(true);

const isSlug = computed(() => route.query.word === undefined && route.params.word !== undefined);

const word = computed(() => {
  if (route.query.word !== undefined) {
    return route.query.word as string;
  }

  const value = Array.isArray(route.params.word) ? undefined : route.params.word as string | undefined;
  return value?.split('-').join(' ');
});

const language = computed(() => {
  let text: string;
  if (route.query.language) {
    text = route.query.language as string;
  } else {
    text = route.params.language as string;
  }

  if (text) {
    const name = locale.getByTag(text)?.name;
    if (name) return name;
  }
  return 'English';
});

if (word.value !== undefined) {
  const api = new RemoteDictionaryApi();
  const response = await useAsyncData(
    word.value,
    async () => {
      if (route.query.id) {
        const result = await api.get(Number(route.query.id));
        return result !== undefined ? [result] : [];
      }

      return isSlug.value ? api.list({ slug: normalizeSlug(route.params.word as string) }) : api.list({ word: word.value });
    }
  );
  if (response.data.value && response.data.value.length > 0) {
    wordEntry.value = response.data.value[0];
  }
}

const showInfoMessage = computed(() => {
  return !fromCache.value && llmStore.shouldShowAPIKeyAlert();
});

onMounted(() => {
  watch([word, language, () => route.query.id], (newValues, oldValues) => {
  // newValues and oldValues will be arrays in the order of your dependencies
  if (newValues.some((val, index) => val !== (oldValues as any)[index])) {
      wordEntry.value = undefined; // Consider if this is truly necessary
      setupUrlAndLookup();
  }
}, { immediate: true }); // Only if you truly need the immediate first call

  watch(selectedLanguage, async (value) => {
    const tag = value ? locale.getByName(value)?.tag : undefined;
    if (tag) {
      router.push({
        params: route.params,
        query: {
          ...route.query,
          language: tag,
        }
      });
    }
  });
});

const llmModel = computed(() => {
  return wordEntry.value ? llmStore.getModelById(wordEntry.value?.llm_model) : undefined;
});

const dictionary = computed(() => {
  let sources: DictionarySource[] = [llmStore.pickvocabDictionarySource];
  if (llmStore.activeUserModel) {
    sources = [llmStore.createDictionarySource(llmStore.activeUserModel), ...sources];
  }
  const dictionary = new Dictionary(sources);
  return dictionary;
});

/**
 * To handle outdated slug in the old URLs. For example, '/dictionary/zonked-(out)' and
 * '/dictionary/zonked-out' are the same.
 */
function normalizeSlug(slug: string) {
  return slugifyText(slug.split('-').join(' '));
}

async function listAllMeanings(word: string, fresh = false): Promise<{ word: WordEntry, changed: boolean }> {
  const api = new RemoteDictionaryApi();
  if (!fresh) {
    const response = await useAsyncData(
      word,
      async () => {
        if (route.query.id) {
          const result = await api.get(Number(route.query.id));
          return result !== undefined ? [result] : [];
        }

        return isSlug.value ? api.list({ slug: normalizeSlug(route.params.word as string) }) : api.list({ word });
      }
    );
    if (response.data.value && response.data.value.length > 0) return { word: response.data.value[0], changed: false };
  }
  fromCache.value = false;
  const wordResult = await dictionary.value.listAllMeanings(word);
  const created = await api.create(wordResult.word);
  return { word: created, changed: wordResult.changed };
}

async function lookupWordForLanguage(language?: string, fresh = false) {
  if (!language || language === 'English') return;
  if (!wordEntry.value) throw new Error('Word entry is undefined');

  try {
    if (fresh) {
      wordEntry.value.definitions.forEach((definition) => {
        delete definition.languages?.[language];
      });
    }

    isDefinitionForLanguageLoading.value = true;
    const { word: entryWithDefinitionForLanguage, changed } = await dictionary.value.listAllMeaningsForLanguage(toRaw(wordEntry.value), language);
    wordEntry.value = { ...toRaw(wordEntry.value!), ...entryWithDefinitionForLanguage };

    if (changed) {
      const api = new RemoteDictionaryApi();
      api.put(wordEntry.value);
    }
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
  } finally {
    isDefinitionForLanguageLoading.value = false;
  }
}

async function getMoreExamples() {
  if (!wordEntry.value) throw new Error('Word entry is undefined');

  try {
    isMoreExamplesLoading.value = true;
    const { word: entryWithExamples, changed } = await dictionary.value.getMoreExamples(toRaw(wordEntry.value!));
    isMoreExamplesLoading.value = false;
    wordEntry.value = { ...toRaw(wordEntry.value!), ...entryWithExamples };

    if (changed) {
      const api = new RemoteDictionaryApi();
      api.put(wordEntry.value);
    }
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
  } finally {
    isMoreExamplesLoading.value = false;
  }
}

async function getMoreSynonyms() {
  if (!wordEntry.value) throw new Error('Word entry is undefined');

  try {
    isMoreSynonymsLoading.value = true;
    const { word: entryWithSynonyms, changed } = await dictionary.value.getMoreSynonyms(toRaw(wordEntry.value))
    isMoreSynonymsLoading.value = false;
    wordEntry.value = { ...toRaw(wordEntry.value!), ...entryWithSynonyms };

    if (changed) {
      const api = new RemoteDictionaryApi();
      api.put(wordEntry.value);
    }
  } catch (err) {
    console.log(err);
    errorMessage.value = `${err}`;
  } finally {
    isMoreSynonymsLoading.value = false;
  }
}

async function lookupWord(word: string, fresh = false, language?: string) {
  errorMessage.value = '';

  try {
    wordEntry.value = (await listAllMeanings(word, fresh)).word;

    lookupWordForLanguage(language);
    getMoreExamples();
    getMoreSynonyms();
  } catch (err) {
    errorMessage.value = `${err}`;
  }
}

const title = computed(() => {
  const wordText =  wordEntry.value ? wordEntry.value.word : word.value;

  switch (language.value) {
    case 'Vietnamese':
      return wordText ? `Nghĩa của ${wordText} | Từ điển Anh - Việt` : 'Từ điển Anh - Việt | Pickvocab';

    default:
      return wordText ? `${wordText} - Definition, Meaning & Synonyms | Pickvocab` : 'Dictionary | Pickvocab';
  }
});

const description = computed(() => {
  if (language.value !== 'English' && wordEntry.value !== undefined) {
    let description = wordEntry.value.definitions[0].definition;
    if (wordEntry.value.definitions[0].languages?.[language.value]?.definition) {
      description = renderDefinitionForLanguage(wordEntry.value.definitions[0], language.value);
    }
    if (wordEntry.value.definitions[0].examples && wordEntry.value.definitions[0].examples.length > 0) {
      description += `: ${wordEntry.value.definitions[0].examples[0]}`;
    }
    return `${description}. Learn more.`;
  }
  if (wordEntry.value !== undefined) {
    let description = wordEntry.value.definitions[0].definition;
    if (wordEntry.value.definitions[0].examples && wordEntry.value.definitions[0].examples.length > 0) {
      description += `: ${wordEntry.value.definitions[0].examples[0]}`;
    }
    return `${description}. Learn more.`;
  }
  return 'Learn more.'
});

if (isSlug.value) {
  const url = useRequestURL();
  useHead({
    link: [
      { rel: 'canonical', href: `${url.origin}${url.pathname}` },
      ...generateAllHrefLangLinks(route.params.word as string, url.origin).map((ele) => {
        return {
          rel: 'alternate',
          href: ele.href,
          hreflang: ele.hreflang,
        };
      }),
    ],
    htmlAttrs: {
      lang: locale.getByName(language.value).tag ?? 'en',
    },
  });
}


useSeoMeta({
  title,
  description,
  ogTitle: title,
  ogDescription: description,
  twitterTitle: title,
  twitterDescription: description,
});

async function setupUrlAndLookup() {
  const fresh = Array.isArray(route.query.fresh) ? undefined : Boolean(route.query.fresh);

  if (languages.some(l => l.value === language.value)) {
    selectedLanguage.value = language.value;
  } else {
    selectedLanguage.value = undefined;
  }

  if (route.query.language && route.params.language) {
    router.push({
      name: 'app-dictionary',
      query: {
        ...route.query,
        word: word.value,
      }
    });
  }

  const newRoute = router.resolve({
    query: {
      ...route.query,
      fresh: undefined,
      language: route.query.language,
    }
  });
  window.history.replaceState('', '', newRoute.fullPath);

  await lookupWord(word.value as string, fresh, language.value);
}

async function refresh() {
  if (word.value && !Array.isArray(word)) {
    if (language.value !== 'English') {
      await lookupWordForLanguage(language.value, true);
    } else {
      wordEntry.value = undefined;
      await lookupWord(word.value, true, language.value);
      router.push({
        params: route.params,
        query: {
            ...route.query,
            id: wordEntry.value!.id.toString()
        }
      });
    }
  }
}

function setupApiKey() {
  store.showAPIKeyModal();
}

async function hideApiKeyAlert() {
  llmStore.lastShowAPIKeyAlert = Date.now();
}

function renderDefinitionForLanguage(definition: DefinitionDetails, language: string) {
  return definition.languages![language].word ?
    `${definition.languages![language].word} - ${definition.languages![language].definition}`
    : definition.languages![language].definition;
}

function compareWords(wordEntry: WordEntry, idx: number) {
  router.push({
    name: 'app-ask',
    query: {
      compare: [wordEntry.word]
        .concat(wordEntry.definitions[idx].synonyms?.map(s => s.synonym) ?? [])
        .map((w) => slugifyText(w)).join(',')
    }
  })
}

</script>

<template>
  <div class="sm:ml-64 mt-14 pt-10 pl-10 pr-10 xl:pr-48 flex flex-col h-full">
    <DictionaryWordViewInfoAlert v-if="showInfoMessage" class="mb-12" @setup="setupApiKey()"
      @dismiss="hideApiKeyAlert()">
    </DictionaryWordViewInfoAlert>

    <div v-if="errorMessage">
      <DictionaryWordViewErrorAlert @retry="refresh()" @setup="setupApiKey()" :message="errorMessage"
        :is-active-user-model="llmStore.activeUserModel ? true : false"></DictionaryWordViewErrorAlert>
    </div>

    <div class="flex items-center py-2 border-b">
      <p class="text-3xl text-gray-700 font-semibold">{{ wordEntry ? wordEntry.word : word }}</p>

      <FwbTooltip class="ml-2 hidden sm:block" theme="dark">
        <template #trigger>
          <icon-reload @click="refresh()" stroke-width="2.5"
            class="inline-block text-gray-400 w-4 h-4 cursor-pointer hover:text-blue-500"></icon-reload>
        </template>
        <template #content>
          <div v-if="wordEntry"
            class="flex items-center px-3 py-2 border font-medium text-sm rounded-lg shadow-sm bg-white">
            <span v-if="llmModel" class="flex items-center">
              <img :src="llmStore.providerMap[llmModel.provider].logo" alt="llm-model-img" class="w-4 h-4">
              <span class="ml-1">{{ llmModel.name }}&nbsp;</span>
            </span>
            <span v-if="wordEntry.createdAt">{{ new Date(wordEntry.createdAt).toLocaleString() }}</span>
          </div>
        </template>
      </FwbTooltip>

      <Dropdown :offsetDistance="-10" :offsetSkidding="-70" class="ml-auto sm:hidden" :hide="!wordEntry">
        <template #trigger>
          <div class="p-2 hover:rounded hover:bg-gray-100 cursor-pointer">
            <icon-dots-vertical class="w-5 h-5"></icon-dots-vertical>
          </div>
        </template>
        <template #body>
          <div v-if="wordEntry"
            class="z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600">
            <div class="px-4 py-3" role="none">
              <div v-if="llmModel" class="flex items-center">
                <img :src="llmStore.providerMap[llmModel.provider].logo" alt="llm-model-img" class="w-4 h-4">
                <p class="ml-1 text-sm font-medium text-gray-900 dark:text-white" role="none">
                  {{ llmModel.name }}
                </p>
              </div>
              <p v-if="wordEntry && wordEntry.createdAt" class="mt-1 text-sm text-gray-600 truncate dark:text-gray-300"
                role="none">
                {{ new Date(wordEntry.createdAt).toLocaleString() }}
              </p>
            </div>
            <ul class="py-1" role="none">
              <li>
                <div @click="store.showAddCardModal(wordEntry)"
                  class="flex items-center px-5 py-2 text-sm text-gray-600 hover:bg-gray-100 cursor-pointer"
                  role="menuitem">
                  <icon-cards class="inline-block w-4 h-4"></icon-cards>
                  <span class="ml-2">Save</span>
                </div>
              </li>
              <li>
                <div @click="refresh()"
                  class="flex items-center px-5 py-2 text-sm text-gray-600 hover:bg-gray-100 cursor-pointer"
                  role="menuitem">
                  <icon-reload class="inline-block w-4 h-4"></icon-reload>
                  <span class="ml-2">Re-lookup</span>
                </div>
              </li>
            </ul>
          </div>
        </template>
      </Dropdown>

      <button type="button"
        class="hidden sm:block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 ml-auto"
        @click="store.showAddCardModal(wordEntry)">Save</button>
    </div>

    <div class="mt-8">
      <fwb-select class="sm:w-48" v-model="selectedLanguage" :options="languages" placeholder="Select Language" />
    </div>

    <div v-if="wordEntry !== undefined" class="pb-10">
      <div v-for="(definition, index) in wordEntry.definitions" :class="{ 'mt-16': index > 0, 'border-t': index > 0 }">
        <div class="flex mt-8 items-center">
          <p class="text-base text-gray-600">{{ index + 1 }}.
            <span class="inline-block text-sm border rounded-xl px-2 align-middle ml-1"
              :class="stylesForPartOfSpeech(definition.partOfSpeech)">
              {{ definition.partOfSpeech }}
            </span>
            <span class="ml-1" v-if="!language || language === 'English'">{{ definition.definition }}</span>
            <span class="ml-1"
              v-else-if="definition.languages && definition.languages[language] && definition.languages[language].definition">{{
                renderDefinitionForLanguage(definition, language) }}</span>
            <Spinner :show-message="false" :size="'5'" class="ml-1 inline-block align-middle" v-else></Spinner>
          </p>
          <div @click="store.showAddCardModal(wordEntry, index)"
            class="ml-1 text-gray-700">
            <div class="p-1 hover:rounded hover:bg-gray-100">
              <icon-bookmark class="w-4 h-4 cursor-pointer flex-shrink-0"></icon-bookmark>
            </div>
          </div>
        </div>

        <!-- <div class="mt-8">
          <Spinner :show-message="false" :size="'5'" v-if="isDefinitionForLanguageLoading"></Spinner>
          <ul v-if="definition.languages" class="mt-8 text-base text-gray-500 list-disc list-inside">
            <li v-for="language in Object.entries(definition.languages)">
              <span class="font-semibold">{{ language[0] }}: </span>
              <span>{{ language[1].definition }}</span>
            </li>
          </ul>
        </div> -->

        <div class="mt-8" v-if="definition.context">
          <p class="text-xl text-gray-700 font-semibold">Usage context</p>
          <p class="text-base text-gray-600 mt-2" v-if="!language || language === 'English'">{{
            definition.context }}</p>
          <p class="text-base text-gray-600 mt-2"
            v-else-if="definition.languages && definition.languages[language] && definition.languages[language].definition">
            {{ definition.languages[language].context }}</p>
          <Spinner :show-message="false" :size="'5'" class="mt-2" v-else></Spinner>
        </div>

        <div class="mt-8">
          <p class="text-xl text-gray-700 font-semibold">Examples</p>
          <ol class="list-decimal list-inside text-base text-gray-600">
            <li v-for="example in definition.examples" class="p-1 mt-2">
              <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">{{ example }}</blockquote>
            </li>
          </ol>
          <Spinner :show-message="false" :size="'5'" class="mt-2" v-if="isMoreExamplesLoading"></Spinner>
        </div>
        <div class="mt-8">
          <div class="flex items-center">
            <p class="text-xl text-gray-700 font-semibold">Synonyms</p>
            <Dropdown :offsetDistance="-10" :offsetSkidding="-20">
              <template #trigger>
                <div class="p-2 hover:rounded hover:bg-gray-100 cursor-pointer">
                  <icon-dots-vertical class="w-4 h-4"></icon-dots-vertical>
                </div>
              </template>
              <template #body>
                <div
                  class="z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600">
                  <ul class="py-1" role="none">
                    <li>
                      <div @click="compareWords(wordEntry, index)"
                        class="flex items-center px-5 py-2 text-sm text-gray-600 hover:bg-gray-100 cursor-pointer"
                        role="menuitem">
                        <icon-scale class="inline-block w-4 h-4"></icon-scale>
                        <span class="ml-2">Compare words</span>
                      </div>
                    </li>
                  </ul>
                </div>
              </template>
            </Dropdown>
          </div>
          <ol class="text-base text-gray-600 mt-4">
            <li v-for="synonym in definition.synonyms" class="mt-4">
              <NuxtLink :to="{ name: 'app-dictionary', query: { word: synonym.synonym } }"
                rel="nofollow"
                class="font-semibold underline text-blue-800">{{ synonym.synonym }}</NuxtLink>
              <blockquote class="mt-2 px-4 border-l-4 border-gray-200 border-solid italic">{{ synonym.example }}</blockquote>
            </li>
          </ol>
          <Spinner :show-message="false" :size="'5'" class="mt-3" v-if="isMoreSynonymsLoading"></Spinner>
        </div>
      </div>
    </div>
    <div v-else-if="!errorMessage" class="flex items-center justify-center h-full">
      <Spinner></Spinner>
    </div>
  </div>
</template>

<style scoped></style>
