<script setup lang="ts">

const emit = defineEmits(['setup', 'retry']);
const props = defineProps<{
  message: string,
  isActiveUserModel?: boolean
}>();

</script>

<template>
  <div id="alert-additional-content-2"
    class="p-4 mb-4 text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800"
    role="alert">
    <div class="flex items-center">
      <svg class="flex-shrink-0 w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
        viewBox="0 0 20 20">
        <path
          d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
      </svg>
      <span class="sr-only">Error</span>
      <h3 class="text-lg font-medium">Something went wrong</h3>
    </div>
    <div class="mt-2 mb-4 text-sm">
      <p>{{ props.message }}</p>
      <div v-if="!isActiveUserModel">
        <br>
        <p>
          We've noticed that you haven't set your own AI Model API key. This may be causing the error you're experiencing. 
          While we provide a default key, it's shared by
          all users and may slow down during busy times. By adding your personal API key in the settings, you'll get
          quicker answers and can use the app as much as you want.
        </p>
        <br>
        <p>Please try again later or set up your own AI Model API key.</p>
      </div>
      <div v-else>
        <br>
        <p>Please try again later.</p>
      </div>
    </div>
    <div v-if="!isActiveUserModel">
      <div class="flex">
        <button type="button" @click="$emit('setup')"
          class="text-white bg-red-800 hover:bg-red-900 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-xs px-3 py-1.5 me-2 text-center inline-flex items-center dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800">
          Setup API Key
        </button>
        <button type="button" @click="$emit('retry')"
          class="text-red-800 bg-transparent border border-red-800 hover:bg-red-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:hover:bg-red-600 dark:border-red-600 dark:text-red-500 dark:hover:text-white dark:focus:ring-red-800"
          data-dismiss-target="#alert-additional-content-2" aria-label="Close">
          Retry
        </button>
      </div>
    </div>
    <div v-else>
      <button type="button" @click="$emit('retry')"
        class="text-red-800 bg-transparent border border-red-800 hover:bg-red-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:hover:bg-red-600 dark:border-red-600 dark:text-red-500 dark:hover:text-white dark:focus:ring-red-800"
        data-dismiss-target="#alert-additional-content-2" aria-label="Close">
        Retry
      </button>
    </div>
  </div>
</template>

<style scoped></style>