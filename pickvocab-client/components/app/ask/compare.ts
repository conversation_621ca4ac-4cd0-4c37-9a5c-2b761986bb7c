export function getComparePrompt(words: string[]): string {
  if (words.length === 0) throw new Error('Empty list of compare words');
  return `
Generate a markdown table comparing the given words or phrases. For each word or phrase, consider including relevant information from the following suggestions:

- Define it concisely
- Provide its primary context of use
- List common synonyms
- Identify subtle connotations or nuances
- Give an example sentence showcasing proper usage
- Note any register (formal, informal, etc.) or field-specific usage
- Highlight key differences from similar words/phrases

Input: ${words.join(', ')}
`
}

export function getComparePrompt2(words: string[]): string {
  if (words.length === 0) throw new Error('Empty list of compare words');
  return `
As an expert in English linguistics and vocabulary, your task is to deliver a focused comparison and analysis of the provided words or phrases.
Consider all possible meanings and nuances for each term, and if the input lacks specificity, analyze and compare each meaning.
Your response should also include clear guidelines on when one word or phrase is preferable over the others, based on context and usage.

Ensure your analysis is comprehensive, detailed, and considers subtle differences in meaning, connotation, and appropriateness for various contexts.

Input: ${words.join(', ')}
`
}

export function getComparePrompt3(words: string[]): string {
  if (words.length === 0) throw new Error('Empty list of compare words');
  return `
As an expert in English linguistics, lexicography, and semantics, your task is to provide a thorough and nuanced analysis of the given words or phrases. Your response should encompass the following elements:

- Key Differences (TLDR): Begin with a concise summary highlighting the most crucial distinctions between the terms.
- Denotative meanings: List and explain all possible definitions for each word or phrase, including less common usages.
- Connotative analysis: Explore the emotional, cultural, and social associations of each term.
- Register and formality: Discuss the level of formality and appropriate contexts for each word or phrase.
- Collocations and idiomatic usage: Provide common word combinations and idiomatic expressions associated with each term.
- Pragmatic considerations: Analyze how the terms might be interpreted in different social situations or cultures.
- Syntactic behavior: Explain any unique grammatical patterns or constructions associated with each word or phrase.
- Stylistic implications: Discuss the effect of choosing one term over another in various types of writing or speech.
- Disambiguation: If the input is ambiguous, analyze each possible interpretation separately.
- Potential pitfalls: Highlight common mistakes or misunderstandings related to the usage of these words or phrases.
- Usage guidelines: Provide clear, context-specific recommendations for when to use each term, supported by examples.

Your analysis should be comprehensive yet concise, balancing depth with clarity. Use examples to illustrate key points and consider both academic and practical perspectives.
If relevant, include insights from corpus linguistics or recent linguistic research to support your analysis.

Input: ${words.join(', ')}
`;
}
