// Re-export prompts
export { getRevisePrompt, getRevisePromptWithSimilaritySearch } from './prompts';

// Re-export handler
export { handleReviseCommand, type ReviseCommandContext } from './handler';

// Re-export API functions
export { performSimilaritySearch, cleanup, cancelSearch } from './api';

// Re-export polling functions (in case they're needed elsewhere)
export { pollForResults, cancelPolling, cleanupPolling, type PollState, activePolls } from './polling'; 