import { RemoteGenericCardsApi } from '~/api/genericCard';
import type { Card } from '~/utils/card';
import { CardType } from '~/utils/card';
import { pollForResults, cancelPolling, cleanupPolling, type PollState, activePolls } from './polling';

/**
 * Format a card for use in prompt
 */
function formatCardForPrompt(card: Card): string {
  if (card.cardType === CardType.DefinitionCard) {
    return `word: ${card.word}\ndefinition: ${card.definition.definition || ''}`;
  } else {
    // Context card
    const wordInContext = card.wordInContext;
    const definition = wordInContext.definition?.definition || wordInContext.definitionShort?.explanation || '';
    return `word: ${wordInContext.word}\ndefinition: ${definition}`;
  }
}

// Store a reference to the API for reuse
const genericCardApi = new RemoteGenericCardsApi();

/**
 * Performs a similarity search with non-blocking polling
 * 
 * @param text The text to search for similar words
 * @param limit Maximum number of results to return (default: 20)
 * @returns A promise that resolves to an array of formatted word strings
 */
export async function performSimilaritySearch(text: string, limit = 20): Promise<string[]> {
  try {
    const taskResult = await genericCardApi.startSimilaritySearch(text, { limit });
    
    if (!taskResult.taskId) {
      console.error("No task ID returned from similarity search");
      return [];
    }
    
    // Create a promise that will be resolved when polling completes
    return new Promise<string[]>((resolve, reject) => {
      // Set up polling state and store it in the map
      const taskId = taskResult.taskId;
      
      // Start non-blocking polling using the API method directly
      const getSimilaritySearchResults = (id: string) => genericCardApi.getSimilaritySearchResults(id);
      
      // Add the poll state to the shared map
      activePolls.set(taskId, {
        taskId,
        attempts: 0,
        limit: 20, // Max attempts
        resolve,
        reject
      });
      
      pollForResults(taskId, getSimilaritySearchResults, result => formatCardForPrompt(result.card));
    });
  } catch (error) {
    console.error("Error in similarity search:", error);
    return [];
  }
}

/**
 * Clean up any active polling tasks
 */
export function cleanup(): void {
  cleanupPolling();
}

/**
 * Cancel a specific similarity search task
 * 
 * @param taskId The ID of the task to cancel
 */
export function cancelSearch(taskId: string): void {
  cancelPolling(taskId);
} 