import type { ChatResponse, ChatSource } from 'pickvocab-dictionary';
import { parseReviseResponse } from '../response';
import { getRevisePromptWithSimilaritySearch } from './prompts';
import { performSimilaritySearch, cleanup } from './api';

/**
 * Helper interface for handleReviseCommand
 */
export interface ReviseCommandContext {
  chatSource: ChatSource;
  historyList: { role: 'user' | 'model', parts: string }[];
  scrollToBottom: () => void;
  userText: string;
  prompt: string;
  authStore: {
    currentUser: any;
  };
  store: {
    isFeatureFlagEnabled: (flag: string, userId: number) => Promise<boolean>;
  };
  llmStore: {
    activeUserModel: any;
    pickvocabChatSource: ChatSource;
    createChatSource: (model: any) => ChatSource;
  };
}

/**
 * Gets a fresh chat source instance for a separate LLM request
 */
function getFreshChatSource(context: ReviseCommandContext): ChatSource {
  if (context.llmStore.activeUserModel) {
    return context.llmStore.createChatSource(context.llmStore.activeUserModel);
  }
  return context.llmStore.pickvocabChatSource;
}

/**
 * Processes the standard revision request
 */
async function processStandardRevision(
  context: ReviseCommandContext
): Promise<ChatResponse | undefined> {
  try {
    const result = await context.chatSource.sendMessage(context.prompt);
    const messages = parseReviseResponse(result.message);
    messages.forEach((message) => {
      context.historyList.push({ role: 'model', parts: message });
    });
    context.scrollToBottom();
    return result;
  } catch (err) {
    console.error("Error in standard revision:", err);
    return undefined;
  }
}

/**
 * Processes the similarity search based revision
 */
async function processSimilaritySearchRevision(
  context: ReviseCommandContext,
  userText: string
): Promise<ChatResponse | undefined> {
  try {
    // Use performSimilaritySearch directly
    const wordsList = await performSimilaritySearch(userText);
    
    if (!wordsList.length) {
      return undefined;
    }
    
    const similaritySearchPrompt = getRevisePromptWithSimilaritySearch(userText, wordsList);
    const freshChatSource = getFreshChatSource(context);
    const result = await freshChatSource.sendMessage(similaritySearchPrompt);
    
    if (result) {
      context.historyList.push({ 
        role: 'model', 
        parts: result.message 
      });
      context.scrollToBottom();
    }
    
    return result;
  } catch (err) {
    console.error("Error in similarity search revision:", err);
    return undefined;
  }
}

/**
 * Handle the revise command
 */
export async function handleReviseCommand(
  context: ReviseCommandContext
): Promise<{ error?: Error }> {
  try {
    // Check if similarity search is enabled
    let shouldDoSimilaritySearch = false;
    if (context.authStore.currentUser) {
      shouldDoSimilaritySearch = await context.store.isFeatureFlagEnabled(
        'revise_with_embedding', 
        context.authStore.currentUser.id
      );
    }
    
    // Start standard revision request
    const standardPromise = processStandardRevision(context);
    
    // Start similarity search if enabled
    let similaritySearchPromise: Promise<ChatResponse | undefined> = Promise.resolve(undefined);
    if (shouldDoSimilaritySearch) {
      const userText = context.userText.slice(8).trim();
      similaritySearchPromise = processSimilaritySearchRevision(context, userText);
    }
    
    // Wait for both to complete (but results will be displayed earlier)
    await Promise.allSettled([standardPromise, similaritySearchPromise]);
    return {};
  } catch (error) {
    return { error: error instanceof Error ? error : new Error(String(error)) };
  } finally {
    // Clean up any ongoing tasks
    cleanup();
  }
} 