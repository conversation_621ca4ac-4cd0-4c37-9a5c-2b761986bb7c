/**
 * Interface for polling task state
 */
export interface PollState {
  taskId: string;
  attempts: number;
  limit: number;
  resolve: (result: string[]) => void;
  reject: (error: Error) => void;
}

// Store active polling operations
export const activePolls = new Map<string, PollState>();

/**
 * Non-blocking polling function
 */
export function pollForResults(taskId: string, 
  getSimilaritySearchResults: (id: string) => Promise<any>,
  formatResult: (result: any) => string
): void {
  const pollState = activePolls.get(taskId);
  if (!pollState) return; // Poll was cancelled
  
  // Poll once
  getSimilaritySearchResults(taskId)
    .then((response: any) => {
      // Log the full response structure for debugging
      console.log(`Poll response for ${taskId}:`, JSON.stringify(response));
      
      // Handle success results
      if (response.status === 'SUCCESS' && response.results) {
        const formattedResults = response.results.map(formatResult);
        activePolls.delete(taskId);
        pollState.resolve(formattedResults);
        return;
      }
      
      // Handle errors
      if (response.status === 'FAILED') {
        activePolls.delete(taskId);
        pollState.reject(new Error(response.error || response.message || 'Search failed'));
        return;
      }
      
      // Add more debug info to help trace what's happening
      console.log(`Polling ${taskId}: status=${response.status}, hasResults=${!!response.results}, attempt=${pollState.attempts}`);
      
      // Check max attempts
      if (pollState.attempts >= pollState.limit) {
        activePolls.delete(taskId);
        pollState.reject(new Error(`Timeout while waiting for search results. Last status: ${response.status}`));
        return;
      }
      
      // Continue polling with setTimeout (non-blocking)
      pollState.attempts++;
      // Use exponential backoff for polling (min 500ms, max 5000ms)
      const backoffTime = 500 * Math.pow(1.5, Math.min(pollState.attempts, 5));
      setTimeout(() => pollForResults(taskId, getSimilaritySearchResults, formatResult), Math.min(backoffTime, 5000));
    })
    .catch((error: any) => {
      console.error(`Polling error for ${taskId}:`, error);
      activePolls.delete(taskId);
      pollState.reject(error);
    });
}

/**
 * Utility to cancel specific polling operation
 */
export function cancelPolling(taskId: string): void {
  if (activePolls.has(taskId)) {
    activePolls.delete(taskId);
  }
}

/**
 * Utility to clean up all polling operations
 */
export function cleanupPolling(): void {
  activePolls.clear();
} 