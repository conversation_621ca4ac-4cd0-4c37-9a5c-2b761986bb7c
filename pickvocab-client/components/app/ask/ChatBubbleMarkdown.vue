<script setup lang="ts">
import { computed } from 'vue';
import { marked } from 'marked';

const props = defineProps<{
  source: string;
}>();

const markdownHtml = computed(() => {
  return marked(props.source, { breaks: true });
});

</script>

<template>
  <div>
    <div class='chat-markdown-body' v-html="markdownHtml"></div>
  </div>
</template>

<style>
.chat-markdown-body {
  --base-size-4: 0.25rem;
  --base-size-8: 0.5rem;
  --base-size-16: 1rem;
  --base-text-weight-normal: 400;
  --base-text-weight-medium: 500;
  --base-text-weight-semibold: 600;
  --fontStack-monospace: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
}

.chat-markdown-body,
[data-theme="light"] {
  /*light*/
  color-scheme: light;
  --focus-outlineColor: #0969da;
  --fgColor-default: #1f2328;
  --fgColor-muted: #636c76;
  --fgColor-accent: #0969da;
  --fgColor-success: #1a7f37;
  --fgColor-attention: #9a6700;
  --fgColor-danger: #d1242f;
  --fgColor-done: #8250df;
  --bgColor-default: #ffffff;
  --bgColor-muted: #f6f8fa;
  --bgColor-neutral-muted: #afb8c133;
  --bgColor-attention-muted: #fff8c5;
  --borderColor-default: #d0d7de;
  --borderColor-muted: #d0d7deb3;
  --borderColor-neutral-muted: #afb8c133;
  --borderColor-accent-emphasis: #0969da;
  --borderColor-success-emphasis: #1a7f37;
  --borderColor-attention-emphasis: #bf8700;
  --borderColor-danger-emphasis: #cf222e;
  --borderColor-done-emphasis: #8250df;
  --color-prettylights-syntax-comment: #57606a;
  --color-prettylights-syntax-constant: #0550ae;
  --color-prettylights-syntax-constant-other-reference-link: #0a3069;
  --color-prettylights-syntax-entity: #6639ba;
  --color-prettylights-syntax-storage-modifier-import: #24292f;
  --color-prettylights-syntax-entity-tag: #0550ae;
  --color-prettylights-syntax-keyword: #cf222e;
  --color-prettylights-syntax-string: #0a3069;
  --color-prettylights-syntax-variable: #953800;
  --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
  --color-prettylights-syntax-brackethighlighter-angle: #57606a;
  --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
  --color-prettylights-syntax-invalid-illegal-bg: #82071e;
  --color-prettylights-syntax-carriage-return-text: #f6f8fa;
  --color-prettylights-syntax-carriage-return-bg: #cf222e;
  --color-prettylights-syntax-string-regexp: #116329;
  --color-prettylights-syntax-markup-list: #3b2300;
  --color-prettylights-syntax-markup-heading: #0550ae;
  --color-prettylights-syntax-markup-italic: #24292f;
  --color-prettylights-syntax-markup-bold: #24292f;
  --color-prettylights-syntax-markup-deleted-text: #82071e;
  --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
  --color-prettylights-syntax-markup-inserted-text: #116329;
  --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
  --color-prettylights-syntax-markup-changed-text: #953800;
  --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
  --color-prettylights-syntax-markup-ignored-text: #eaeef2;
  --color-prettylights-syntax-markup-ignored-bg: #0550ae;
  --color-prettylights-syntax-meta-diff-range: #8250df;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
}

.chat-markdown-body {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  margin: 0;
  /* color: var(--fgColor-default); */
  /* background-color: var(--bgColor-default); */
  /* font-family: -apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"; */
  font-size: 16px;
  line-height: 1.5;
  word-wrap: break-word;
  scroll-behavior: auto;
}

.chat-markdown-body .octicon {
  display: inline-block;
  fill: currentColor;
  vertical-align: text-bottom;
}

.chat-markdown-body h1:hover .anchor .octicon-link:before,
.chat-markdown-body h2:hover .anchor .octicon-link:before,
.chat-markdown-body h3:hover .anchor .octicon-link:before,
.chat-markdown-body h4:hover .anchor .octicon-link:before,
.chat-markdown-body h5:hover .anchor .octicon-link:before,
.chat-markdown-body h6:hover .anchor .octicon-link:before {
  width: 16px;
  height: 16px;
  content: ' ';
  display: inline-block;
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
  mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
}

.chat-markdown-body details,
.chat-markdown-body figcaption,
.chat-markdown-body figure {
  display: block;
}

.chat-markdown-body summary {
  display: list-item;
}

.chat-markdown-body [hidden] {
  display: none !important;
}

.chat-markdown-body a {
  background-color: transparent;
  color: var(--fgColor-accent);
  text-decoration: none;
}

.chat-markdown-body abbr[title] {
  border-bottom: none;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

.chat-markdown-body b,
.chat-markdown-body strong {
  font-weight: var(--base-text-weight-semibold, 600);
}

.chat-markdown-body dfn {
  font-style: italic;
}

.chat-markdown-body h1 {
  margin: .67em 0;
  font-weight: var(--base-text-weight-semibold, 600);
  padding-bottom: .3em;
  font-size: 2em;
  /* border-bottom: 1px solid var(--borderColor-muted); */
}

.chat-markdown-body mark {
  background-color: var(--bgColor-attention-muted);
  color: var(--fgColor-default);
}

.chat-markdown-body small {
  font-size: 90%;
}

.chat-markdown-body sub,
.chat-markdown-body sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.chat-markdown-body sub {
  bottom: -0.25em;
}

.chat-markdown-body sup {
  top: -0.5em;
}

.chat-markdown-body img {
  border-style: none;
  max-width: 100%;
  box-sizing: content-box;
  background-color: var(--bgColor-default);
}

.chat-markdown-body code,
.chat-markdown-body kbd,
.chat-markdown-body pre,
.chat-markdown-body samp {
  font-family: monospace;
  font-size: 1em;
}

.chat-markdown-body figure {
  margin: 1em 40px;
}

.chat-markdown-body hr {
  box-sizing: content-box;
  overflow: hidden;
  background: transparent;
  border-bottom: 1px solid var(--borderColor-muted);
  height: .25em;
  padding: 0;
  margin: 24px 0;
  background-color: var(--borderColor-default);
  border: 0;
}

.chat-markdown-body input {
  font: inherit;
  margin: 0;
  overflow: visible;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.chat-markdown-body [type=button],
.chat-markdown-body [type=reset],
.chat-markdown-body [type=submit] {
  -webkit-appearance: button;
  appearance: button;
}

.chat-markdown-body [type=checkbox],
.chat-markdown-body [type=radio] {
  box-sizing: border-box;
  padding: 0;
}

.chat-markdown-body [type=number]::-webkit-inner-spin-button,
.chat-markdown-body [type=number]::-webkit-outer-spin-button {
  height: auto;
}

.chat-markdown-body [type=search]::-webkit-search-cancel-button,
.chat-markdown-body [type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
  appearance: none;
}

.chat-markdown-body ::-webkit-input-placeholder {
  color: inherit;
  opacity: .54;
}

.chat-markdown-body ::-webkit-file-upload-button {
  -webkit-appearance: button;
  appearance: button;
  font: inherit;
}

.chat-markdown-body a:hover {
  text-decoration: underline;
}

.chat-markdown-body ::placeholder {
  color: var(--fgColor-muted);
  opacity: 1;
}

.chat-markdown-body hr::before {
  display: table;
  content: "";
}

.chat-markdown-body hr::after {
  display: table;
  clear: both;
  content: "";
}

.chat-markdown-body table {
  border-spacing: 0;
  border-collapse: collapse;
  display: block;
  width: max-content;
  max-width: 100%;
  overflow: auto;
}

.chat-markdown-body td,
.chat-markdown-body th {
  padding: 0;
}

.chat-markdown-body details summary {
  cursor: pointer;
}

.chat-markdown-body details:not([open])>*:not(summary) {
  display: none;
}

.chat-markdown-body a:focus,
.chat-markdown-body [role=button]:focus,
.chat-markdown-body input[type=radio]:focus,
.chat-markdown-body input[type=checkbox]:focus {
  outline: 2px solid var(--focus-outlineColor);
  outline-offset: -2px;
  box-shadow: none;
}

.chat-markdown-body a:focus:not(:focus-visible),
.chat-markdown-body [role=button]:focus:not(:focus-visible),
.chat-markdown-body input[type=radio]:focus:not(:focus-visible),
.chat-markdown-body input[type=checkbox]:focus:not(:focus-visible) {
  outline: solid 1px transparent;
}

.chat-markdown-body a:focus-visible,
.chat-markdown-body [role=button]:focus-visible,
.chat-markdown-body input[type=radio]:focus-visible,
.chat-markdown-body input[type=checkbox]:focus-visible {
  outline: 2px solid var(--focus-outlineColor);
  outline-offset: -2px;
  box-shadow: none;
}

.chat-markdown-body a:not([class]):focus,
.chat-markdown-body a:not([class]):focus-visible,
.chat-markdown-body input[type=radio]:focus,
.chat-markdown-body input[type=radio]:focus-visible,
.chat-markdown-body input[type=checkbox]:focus,
.chat-markdown-body input[type=checkbox]:focus-visible {
  outline-offset: 0;
}

.chat-markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font: 11px var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);
  line-height: 10px;
  color: var(--fgColor-default);
  vertical-align: middle;
  background-color: var(--bgColor-muted);
  border: solid 1px var(--borderColor-neutral-muted);
  border-bottom-color: var(--borderColor-neutral-muted);
  border-radius: 6px;
  box-shadow: inset 0 -1px 0 var(--borderColor-neutral-muted);
}

.chat-markdown-body h1,
.chat-markdown-body h2,
.chat-markdown-body h3,
.chat-markdown-body h4,
.chat-markdown-body h5,
.chat-markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: var(--base-text-weight-semibold, 600);
  line-height: 1.25;
}

.chat-markdown-body h2 {
  font-weight: var(--base-text-weight-semibold, 600);
  padding-bottom: .3em;
  font-size: 1.5em;
  border-bottom: 1px solid var(--borderColor-muted);
}

.chat-markdown-body h3 {
  font-weight: var(--base-text-weight-semibold, 600);
  font-size: 1.25em;
}

.chat-markdown-body h4 {
  font-weight: var(--base-text-weight-semibold, 600);
  font-size: 1em;
}

.chat-markdown-body h5 {
  font-weight: var(--base-text-weight-semibold, 600);
  font-size: .875em;
}

.chat-markdown-body h6 {
  font-weight: var(--base-text-weight-semibold, 600);
  font-size: .85em;
  color: var(--fgColor-muted);
}

.chat-markdown-body p {
  margin-top: 0;
  margin-bottom: 10px;
}

.chat-markdown-body blockquote {
  margin: 0;
  padding: 0 1em;
  color: var(--fgColor-muted);
  border-left: .25em solid var(--borderColor-default);
}

.chat-markdown-body ul,
.chat-markdown-body ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 2em;
}

.chat-markdown-body ul {
  list-style-type: disc;
}

.chat-markdown-body ul ul {
  list-style-type: circle;
}

.chat-markdown-body ul ul ul {
  list-style-type: square;
}

.chat-markdown-body ol {
  list-style-type: decimal;
}

.chat-markdown-body ol ol {
  list-style-type: lower-alpha;
}

.chat-markdown-body ol ol ol {
  list-style-type: lower-roman;
}

.chat-markdown-body ol ol,
.chat-markdown-body ul ol {
  list-style-type: lower-roman;
}

.chat-markdown-body ul ul ol,
.chat-markdown-body ul ol ol,
.chat-markdown-body ol ul ol,
.chat-markdown-body ol ol ol {
  list-style-type: lower-alpha;
}

.chat-markdown-body dd {
  margin-left: 0;
}

.chat-markdown-body tt,
.chat-markdown-body code,
.chat-markdown-body samp {
  font-family: var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);
  font-size: 12px;
}

.chat-markdown-body pre {
  margin-top: 0;
  margin-bottom: 0;
  font-family: var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);
  font-size: 12px;
  word-wrap: normal;
}

.chat-markdown-body .octicon {
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.chat-markdown-body input::-webkit-outer-spin-button,
.chat-markdown-body input::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
  appearance: none;
}

.chat-markdown-body .mr-2 {
  margin-right: var(--base-size-8, 8px) !important;
}

.chat-markdown-body::before {
  display: table;
  content: "";
}

.chat-markdown-body::after {
  display: table;
  clear: both;
  content: "";
}

.chat-markdown-body>*:first-child {
  margin-top: 0 !important;
}

.chat-markdown-body>*:last-child {
  margin-bottom: 0 !important;
}

.chat-markdown-body a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.chat-markdown-body .absent {
  color: var(--fgColor-danger);
}

.chat-markdown-body .anchor {
  float: left;
  padding-right: 4px;
  margin-left: -20px;
  line-height: 1;
}

.chat-markdown-body .anchor:focus {
  outline: none;
}

.chat-markdown-body p,
.chat-markdown-body blockquote,
.chat-markdown-body ul,
.chat-markdown-body ol,
.chat-markdown-body dl,
.chat-markdown-body table,
.chat-markdown-body pre,
.chat-markdown-body details {
  margin-top: 0;
  margin-bottom: 16px;
}

.chat-markdown-body blockquote>:first-child {
  margin-top: 0;
}

.chat-markdown-body blockquote>:last-child {
  margin-bottom: 0;
}

.chat-markdown-body h1 .octicon-link,
.chat-markdown-body h2 .octicon-link,
.chat-markdown-body h3 .octicon-link,
.chat-markdown-body h4 .octicon-link,
.chat-markdown-body h5 .octicon-link,
.chat-markdown-body h6 .octicon-link {
  color: var(--fgColor-default);
  vertical-align: middle;
  visibility: hidden;
}

.chat-markdown-body h1:hover .anchor,
.chat-markdown-body h2:hover .anchor,
.chat-markdown-body h3:hover .anchor,
.chat-markdown-body h4:hover .anchor,
.chat-markdown-body h5:hover .anchor,
.chat-markdown-body h6:hover .anchor {
  text-decoration: none;
}

.chat-markdown-body h1:hover .anchor .octicon-link,
.chat-markdown-body h2:hover .anchor .octicon-link,
.chat-markdown-body h3:hover .anchor .octicon-link,
.chat-markdown-body h4:hover .anchor .octicon-link,
.chat-markdown-body h5:hover .anchor .octicon-link,
.chat-markdown-body h6:hover .anchor .octicon-link {
  visibility: visible;
}

.chat-markdown-body h1 tt,
.chat-markdown-body h1 code,
.chat-markdown-body h2 tt,
.chat-markdown-body h2 code,
.chat-markdown-body h3 tt,
.chat-markdown-body h3 code,
.chat-markdown-body h4 tt,
.chat-markdown-body h4 code,
.chat-markdown-body h5 tt,
.chat-markdown-body h5 code,
.chat-markdown-body h6 tt,
.chat-markdown-body h6 code {
  padding: 0 .2em;
  font-size: inherit;
}

.chat-markdown-body summary h1,
.chat-markdown-body summary h2,
.chat-markdown-body summary h3,
.chat-markdown-body summary h4,
.chat-markdown-body summary h5,
.chat-markdown-body summary h6 {
  display: inline-block;
}

.chat-markdown-body summary h1 .anchor,
.chat-markdown-body summary h2 .anchor,
.chat-markdown-body summary h3 .anchor,
.chat-markdown-body summary h4 .anchor,
.chat-markdown-body summary h5 .anchor,
.chat-markdown-body summary h6 .anchor {
  margin-left: -40px;
}

.chat-markdown-body summary h1,
.chat-markdown-body summary h2 {
  padding-bottom: 0;
  border-bottom: 0;
}

.chat-markdown-body ul.no-list,
.chat-markdown-body ol.no-list {
  padding: 0;
  list-style-type: none;
}

.chat-markdown-body ol[type="a s"] {
  list-style-type: lower-alpha;
}

.chat-markdown-body ol[type="A s"] {
  list-style-type: upper-alpha;
}

.chat-markdown-body ol[type="i s"] {
  list-style-type: lower-roman;
}

.chat-markdown-body ol[type="I s"] {
  list-style-type: upper-roman;
}

.chat-markdown-body ol[type="1"] {
  list-style-type: decimal;
}

.chat-markdown-body div>ol:not([type]) {
  list-style-type: decimal;
}

.chat-markdown-body ul ul,
.chat-markdown-body ul ol,
.chat-markdown-body ol ol,
.chat-markdown-body ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.chat-markdown-body li>p {
  margin-top: 16px;
}

.chat-markdown-body li+li {
  margin-top: .25em;
}

.chat-markdown-body dl {
  padding: 0;
}

.chat-markdown-body dl dt {
  padding: 0;
  margin-top: 16px;
  font-size: 1em;
  font-style: italic;
  font-weight: var(--base-text-weight-semibold, 600);
}

.chat-markdown-body dl dd {
  padding: 0 16px;
  margin-bottom: 16px;
}

.chat-markdown-body table th {
  font-weight: var(--base-text-weight-semibold, 600);
}

.chat-markdown-body table th,
.chat-markdown-body table td {
  padding: 6px 13px;
  border: 1px solid var(--borderColor-default);
}

.chat-markdown-body table td>:last-child {
  margin-bottom: 0;
}

.chat-markdown-body table tr {
  background-color: var(--bgColor-default);
  border-top: 1px solid var(--borderColor-muted);
}

.chat-markdown-body table tr:nth-child(2n) {
  background-color: var(--bgColor-muted);
}

.chat-markdown-body table img {
  background-color: transparent;
}

.chat-markdown-body img[align=right] {
  padding-left: 20px;
}

.chat-markdown-body img[align=left] {
  padding-right: 20px;
}

.chat-markdown-body .emoji {
  max-width: none;
  vertical-align: text-top;
  background-color: transparent;
}

.chat-markdown-body span.frame {
  display: block;
  overflow: hidden;
}

.chat-markdown-body span.frame>span {
  display: block;
  float: left;
  width: auto;
  padding: 7px;
  margin: 13px 0 0;
  overflow: hidden;
  border: 1px solid var(--borderColor-default);
}

.chat-markdown-body span.frame span img {
  display: block;
  float: left;
}

.chat-markdown-body span.frame span span {
  display: block;
  padding: 5px 0 0;
  clear: both;
  color: var(--fgColor-default);
}

.chat-markdown-body span.align-center {
  display: block;
  overflow: hidden;
  clear: both;
}

.chat-markdown-body span.align-center>span {
  display: block;
  margin: 13px auto 0;
  overflow: hidden;
  text-align: center;
}

.chat-markdown-body span.align-center span img {
  margin: 0 auto;
  text-align: center;
}

.chat-markdown-body span.align-right {
  display: block;
  overflow: hidden;
  clear: both;
}

.chat-markdown-body span.align-right>span {
  display: block;
  margin: 13px 0 0;
  overflow: hidden;
  text-align: right;
}

.chat-markdown-body span.align-right span img {
  margin: 0;
  text-align: right;
}

.chat-markdown-body span.float-left {
  display: block;
  float: left;
  margin-right: 13px;
  overflow: hidden;
}

.chat-markdown-body span.float-left span {
  margin: 13px 0 0;
}

.chat-markdown-body span.float-right {
  display: block;
  float: right;
  margin-left: 13px;
  overflow: hidden;
}

.chat-markdown-body span.float-right>span {
  display: block;
  margin: 13px auto 0;
  overflow: hidden;
  text-align: right;
}

.chat-markdown-body code,
.chat-markdown-body tt {
  padding: .2em .4em;
  margin: 0;
  font-size: 85%;
  white-space: break-spaces;
  background-color: var(--bgColor-neutral-muted);
  border-radius: 6px;
}

.chat-markdown-body code br,
.chat-markdown-body tt br {
  display: none;
}

.chat-markdown-body del code {
  text-decoration: inherit;
}

.chat-markdown-body samp {
  font-size: 85%;
}

.chat-markdown-body pre code {
  font-size: 100%;
}

.chat-markdown-body pre>code {
  padding: 0;
  margin: 0;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.chat-markdown-body .highlight {
  margin-bottom: 16px;
}

.chat-markdown-body .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.chat-markdown-body .highlight pre,
.chat-markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  color: var(--fgColor-default);
  background-color: var(--bgColor-muted);
  border-radius: 6px;
}

.chat-markdown-body pre code,
.chat-markdown-body pre tt {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

.chat-markdown-body .csv-data td,
.chat-markdown-body .csv-data th {
  padding: 5px;
  overflow: hidden;
  font-size: 12px;
  line-height: 1;
  text-align: left;
  white-space: nowrap;
}

.chat-markdown-body .csv-data .blob-num {
  padding: 10px 8px 9px;
  text-align: right;
  background: var(--bgColor-default);
  border: 0;
}

.chat-markdown-body .csv-data tr {
  border-top: 0;
}

.chat-markdown-body .csv-data th {
  font-weight: var(--base-text-weight-semibold, 600);
  background: var(--bgColor-muted);
  border-top: 0;
}

.chat-markdown-body [data-footnote-ref]::before {
  content: "[";
}

.chat-markdown-body [data-footnote-ref]::after {
  content: "]";
}

.chat-markdown-body .footnotes {
  font-size: 12px;
  color: var(--fgColor-muted);
  border-top: 1px solid var(--borderColor-default);
}

.chat-markdown-body .footnotes ol {
  padding-left: 16px;
}

.chat-markdown-body .footnotes ol ul {
  display: inline-block;
  padding-left: 16px;
  margin-top: 16px;
}

.chat-markdown-body .footnotes li {
  position: relative;
}

.chat-markdown-body .footnotes li:target::before {
  position: absolute;
  top: -8px;
  right: -8px;
  bottom: -8px;
  left: -24px;
  pointer-events: none;
  content: "";
  border: 2px solid var(--borderColor-accent-emphasis);
  border-radius: 6px;
}

.chat-markdown-body .footnotes li:target {
  color: var(--fgColor-default);
}

.chat-markdown-body .footnotes .data-footnote-backref g-emoji {
  font-family: monospace;
}

.chat-markdown-body .pl-c {
  color: var(--color-prettylights-syntax-comment);
}

.chat-markdown-body .pl-c1,
.chat-markdown-body .pl-s .pl-v {
  color: var(--color-prettylights-syntax-constant);
}

.chat-markdown-body .pl-e,
.chat-markdown-body .pl-en {
  color: var(--color-prettylights-syntax-entity);
}

.chat-markdown-body .pl-smi,
.chat-markdown-body .pl-s .pl-s1 {
  color: var(--color-prettylights-syntax-storage-modifier-import);
}

.chat-markdown-body .pl-ent {
  color: var(--color-prettylights-syntax-entity-tag);
}

.chat-markdown-body .pl-k {
  color: var(--color-prettylights-syntax-keyword);
}

.chat-markdown-body .pl-s,
.chat-markdown-body .pl-pds,
.chat-markdown-body .pl-s .pl-pse .pl-s1,
.chat-markdown-body .pl-sr,
.chat-markdown-body .pl-sr .pl-cce,
.chat-markdown-body .pl-sr .pl-sre,
.chat-markdown-body .pl-sr .pl-sra {
  color: var(--color-prettylights-syntax-string);
}

.chat-markdown-body .pl-v,
.chat-markdown-body .pl-smw {
  color: var(--color-prettylights-syntax-variable);
}

.chat-markdown-body .pl-bu {
  color: var(--color-prettylights-syntax-brackethighlighter-unmatched);
}

.chat-markdown-body .pl-ii {
  color: var(--color-prettylights-syntax-invalid-illegal-text);
  background-color: var(--color-prettylights-syntax-invalid-illegal-bg);
}

.chat-markdown-body .pl-c2 {
  color: var(--color-prettylights-syntax-carriage-return-text);
  background-color: var(--color-prettylights-syntax-carriage-return-bg);
}

.chat-markdown-body .pl-sr .pl-cce {
  font-weight: bold;
  color: var(--color-prettylights-syntax-string-regexp);
}

.chat-markdown-body .pl-ml {
  color: var(--color-prettylights-syntax-markup-list);
}

.chat-markdown-body .pl-mh,
.chat-markdown-body .pl-mh .pl-en,
.chat-markdown-body .pl-ms {
  font-weight: bold;
  color: var(--color-prettylights-syntax-markup-heading);
}

.chat-markdown-body .pl-mi {
  font-style: italic;
  color: var(--color-prettylights-syntax-markup-italic);
}

.chat-markdown-body .pl-mb {
  font-weight: bold;
  color: var(--color-prettylights-syntax-markup-bold);
}

.chat-markdown-body .pl-md {
  color: var(--color-prettylights-syntax-markup-deleted-text);
  background-color: var(--color-prettylights-syntax-markup-deleted-bg);
}

.chat-markdown-body .pl-mi1 {
  color: var(--color-prettylights-syntax-markup-inserted-text);
  background-color: var(--color-prettylights-syntax-markup-inserted-bg);
}

.chat-markdown-body .pl-mc {
  color: var(--color-prettylights-syntax-markup-changed-text);
  background-color: var(--color-prettylights-syntax-markup-changed-bg);
}

.chat-markdown-body .pl-mi2 {
  color: var(--color-prettylights-syntax-markup-ignored-text);
  background-color: var(--color-prettylights-syntax-markup-ignored-bg);
}

.chat-markdown-body .pl-mdr {
  font-weight: bold;
  color: var(--color-prettylights-syntax-meta-diff-range);
}

.chat-markdown-body .pl-ba {
  color: var(--color-prettylights-syntax-brackethighlighter-angle);
}

.chat-markdown-body .pl-sg {
  color: var(--color-prettylights-syntax-sublimelinter-gutter-mark);
}

.chat-markdown-body .pl-corl {
  text-decoration: underline;
  color: var(--color-prettylights-syntax-constant-other-reference-link);
}

.chat-markdown-body [role=button]:focus:not(:focus-visible),
.chat-markdown-body [role=tabpanel][tabindex="0"]:focus:not(:focus-visible),
.chat-markdown-body button:focus:not(:focus-visible),
.chat-markdown-body summary:focus:not(:focus-visible),
.chat-markdown-body a:focus:not(:focus-visible) {
  outline: none;
  box-shadow: none;
}

.chat-markdown-body [tabindex="0"]:focus:not(:focus-visible),
.chat-markdown-body details-dialog:focus:not(:focus-visible) {
  outline: none;
}

.chat-markdown-body g-emoji {
  display: inline-block;
  min-width: 1ch;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1em;
  font-style: normal !important;
  font-weight: var(--base-text-weight-normal, 400);
  line-height: 1;
  vertical-align: -0.075em;
}

.chat-markdown-body g-emoji img {
  width: 1em;
  height: 1em;
}

.chat-markdown-body .task-list-item {
  list-style-type: none;
}

.chat-markdown-body .task-list-item label {
  font-weight: var(--base-text-weight-normal, 400);
}

.chat-markdown-body .task-list-item.enabled label {
  cursor: pointer;
}

.chat-markdown-body .task-list-item+.task-list-item {
  margin-top: var(--base-size-4);
}

.chat-markdown-body .task-list-item .handle {
  display: none;
}

.chat-markdown-body .task-list-item-checkbox {
  margin: 0 .2em .25em -1.4em;
  vertical-align: middle;
}

.chat-markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {
  margin: 0 -1.6em .25em .2em;
}

.chat-markdown-body .contains-task-list {
  position: relative;
}

.chat-markdown-body .contains-task-list:hover .task-list-item-convert-container,
.chat-markdown-body .contains-task-list:focus-within .task-list-item-convert-container {
  display: block;
  width: auto;
  height: 24px;
  overflow: visible;
  clip: auto;
}

.chat-markdown-body ::-webkit-calendar-picker-indicator {
  filter: invert(50%);
}

.chat-markdown-body .markdown-alert {
  padding: var(--base-size-8) var(--base-size-16);
  margin-bottom: var(--base-size-16);
  color: inherit;
  border-left: .25em solid var(--borderColor-default);
}

.chat-markdown-body .markdown-alert>:first-child {
  margin-top: 0;
}

.chat-markdown-body .markdown-alert>:last-child {
  margin-bottom: 0;
}

.chat-markdown-body .markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: var(--base-text-weight-medium, 500);
  align-items: center;
  line-height: 1;
}

.chat-markdown-body .markdown-alert.markdown-alert-note {
  border-left-color: var(--borderColor-accent-emphasis);
}

.chat-markdown-body .markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--fgColor-accent);
}

.chat-markdown-body .markdown-alert.markdown-alert-important {
  border-left-color: var(--borderColor-done-emphasis);
}

.chat-markdown-body .markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--fgColor-done);
}

.chat-markdown-body .markdown-alert.markdown-alert-warning {
  border-left-color: var(--borderColor-attention-emphasis);
}

.chat-markdown-body .markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--fgColor-attention);
}

.chat-markdown-body .markdown-alert.markdown-alert-tip {
  border-left-color: var(--borderColor-success-emphasis);
}

.chat-markdown-body .markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--fgColor-success);
}

.chat-markdown-body .markdown-alert.markdown-alert-caution {
  border-left-color: var(--borderColor-danger-emphasis);
}

.chat-markdown-body .markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--fgColor-danger);
}

.chat-markdown-body>*:first-child>.heading-element:first-child {
  margin-top: 0 !important;
}
</style>