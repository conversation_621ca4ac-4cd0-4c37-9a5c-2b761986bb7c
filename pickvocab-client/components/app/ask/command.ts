import { getCheckPrompt } from "./check";
import { getComparePrompt, getComparePrompt2, getComparePrompt3 } from "./compare";
import { getRevisePrompt } from "./revise/index";

export enum CommandKind {
  Revise,
  Check,
  Compare,
  Ask,
  Help
}

// export interface Command {
//   kind: CommandKind;
//   args: string[];
// }

const delimeter = [' ', '\n', '\t', '\r'];
const stringQuotes = ['"', "'"];

function parse(text: string): string[] {
  let mode: 'normal' | 'string' = 'normal';

  let current = '';
  const parts = [];

  for (let i = 0; i < text.length; i++) {
    if (mode === 'normal') {
      if (delimeter.includes(text[i])) {
        if (current !== '') {
          parts.push(current);
          current = '';
        }
      } else if (stringQuotes.includes(text[i])) {
        mode = 'string';
      } else {
        current += text[i];
      }
    } else {
      if (stringQuotes.includes(text[i])) {
        mode = 'normal';
      } else {
        current += text[i];
      }
    }
  }
  if (current !== '') {
    parts.push(current);
  }
  return parts;
}

export function getPrompt(text: string): { prompt: string, command: CommandKind, args?: string[] } {
  if (text.startsWith('/revise')) {
    return { prompt: getRevisePrompt(text.slice(8).trim()), command: CommandKind.Revise };
  } else if (text.startsWith('/check')) {
    return { prompt: getCheckPrompt(text.slice(6).trim()), command: CommandKind.Check };
  } else if (text.startsWith('/compare')) {
    const words = parse(text.slice(8).trim());
    return { prompt: getComparePrompt3(words), command: CommandKind.Compare, args: words };
  } else if (text.startsWith('/help')) {
    return { prompt: '', command: CommandKind.Help };
  }

  return { prompt: text, command: CommandKind.Ask };
}