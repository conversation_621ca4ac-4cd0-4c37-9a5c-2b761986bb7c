import YAML from 'yaml'

function extractFromCodeBlock(codeBlock: string): string {
  const match = codeBlock.match(/\`\`\`(?:.*)\n((?:.|\s)*)\`\`\`/);
  if (match === null) return codeBlock;
  return match[1];
}

function removeQuotes(str: string): string {
  if (str.startsWith('\\"') && str.endsWith('\\"')) {
    return str.slice(2, -2);
  }
  if (str.startsWith("\\'") && str.endsWith("\\'")) {
    return str.slice(2, -2);
  }
  return str;
}

export function parseReviseResponse(response: string): string[] {
  console.log(response);
  response = response.replace(/"/g, '\\"');
  response = response.replace(/'/g, "\\'");

  const yamlResponse = extractFromCodeBlock(response);
  const result = YAML.parse(yamlResponse);

  const casualResponse = `
## 😊 Casual
${result.casual.revision}

### Notable phrases
${result.casual.notable_phrases.map((phrase: { phrase: string, explanation: string }) => {
  const phraseWithoutQuotes = removeQuotes(phrase.phrase);
  return `- [${phraseWithoutQuotes}](/dictionary/${encodeURIComponent(phraseWithoutQuotes)})\n  - ${phrase.explanation}`;
}).join('\n')}

### Vocabularies
${result.casual.words.map((word: { word: string, definition: string }) => {
  const wordWithoutQuotes = removeQuotes(word.word);
  return `- [${wordWithoutQuotes}](/dictionary/${encodeURIComponent(wordWithoutQuotes)})\n  - ${word.definition}`;
}).join('\n')}
  `;

  const neutralResponse = `
## 📝 Neutral
${result.neutral.revision}

### Notable phrases
${result.neutral.notable_phrases.map((phrase: { phrase: string, explanation: string }) => {
  const phraseWithoutQuotes = removeQuotes(phrase.phrase);
  return `- [${phraseWithoutQuotes}](/dictionary/${encodeURIComponent(phraseWithoutQuotes)})\n  - ${phrase.explanation}`;
}).join('\n')}

### Vocabularies
${result.neutral.words.map((word: { word: string, definition: string }) => {
  const wordWithoutQuotes = removeQuotes(word.word);
  return `- [${wordWithoutQuotes}](/dictionary/${encodeURIComponent(wordWithoutQuotes)})\n  - ${word.definition}`;
}).join('\n')}
  `;

  const formalResponse = `
## 👔 Formal
${result.formal.revision}

### Notable phrases
${result.formal.notable_phrases.map((phrase: { phrase: string, explanation: string }) => {
  const phraseWithoutQuotes = removeQuotes(phrase.phrase);
  return `- [${phraseWithoutQuotes}](/dictionary/${encodeURIComponent(phraseWithoutQuotes)})\n  - ${phrase.explanation}`
}).join('\n')}

### Vocabularies
${result.formal.words.map((word: { word: string, definition: string }) => {
  const wordWithoutQuotes = removeQuotes(word.word);
  return `- [${wordWithoutQuotes}](/dictionary/${encodeURIComponent(wordWithoutQuotes)})\n  - ${word.definition}`;
}).join('\n')}
  `;
  return [casualResponse, neutralResponse, formalResponse];
}