import type { ChatSource } from 'pickvocab-dictionary';
import { CommandKind } from './command';
import { WordComparisonApi } from '~/api/wordComparison';
import { handleReviseCommand as handleReviseCommandImpl, type ReviseCommandContext } from './revise/index';
import { slugifyText } from '~/utils/slugifyText';

// Types for handlers
export interface CommandHandlerContext {
  chatSource: ChatSource;
  historyList: { role: 'user' | 'model', parts: string }[];
  scrollToBottom: () => void;
  userText: string;
  message: {
    prompt: string;
    command: CommandKind;
    args?: string[];
  };
  authStore: {
    currentUser: any;
  };
  store: {
    isFeatureFlagEnabled: (flag: string, userId: number) => Promise<boolean>;
    showAPIKeyModal: () => void;
  };
  llmStore: {
    activeUserModel: any;
    pickvocabChatSource: ChatSource;
    createChatSource: (model: any) => ChatSource;
  };
  botWelcomeMessage: string;
}

export interface CommandHandlerResult {
  error?: Error;
}

// Help command handler
export async function handleHelpCommand(
  context: CommandHandlerContext
): Promise<CommandHandlerResult> {
  context.historyList.push({ role: 'model', parts: context.botWelcomeMessage });
  context.scrollToBottom();
  return {};
}

// Compare command handler
export async function handleCompareCommand(
  context: CommandHandlerContext
): Promise<CommandHandlerResult> {
  try {
    if (!context.message.args) {
      throw new Error('No args provided');
    }

    const wordComparisonApi = new WordComparisonApi();
    
    try {
      // Try to get from cache first
      const cacheResults = await wordComparisonApi.list({ 
        words: context.message.args.map(slugifyText).join(',') 
      });
      
      if (!cacheResults.length) {
        throw new Error('No word comparison found');
      }
      
      const response = cacheResults[0].content;
      context.historyList.push({ role: 'model', parts: response });
    } catch (err) {
      // Generate new comparison if not in cache
      const result = await context.chatSource.sendMessage(context.message.prompt);
      const response = result.message;
      context.historyList.push({ role: 'model', parts: response });

      // Cache the result
      await wordComparisonApi.create({
        content: response,
        words: context.message.args.map(slugifyText).join(','),
        llm_model: result.modelId
      });
    }
    
    return {};
  } catch (error) {
    return { error: error instanceof Error ? error : new Error(String(error)) };
  }
}

// Revise command handler - using the implementation from the revise module
export async function handleReviseCommand(
  context: CommandHandlerContext
): Promise<CommandHandlerResult> {
  // Create ReviseCommandContext from CommandHandlerContext
  const reviseContext: ReviseCommandContext = {
    chatSource: context.chatSource,
    historyList: context.historyList,
    scrollToBottom: context.scrollToBottom,
    userText: context.userText,
    prompt: context.message.prompt,
    authStore: context.authStore,
    store: context.store,
    llmStore: context.llmStore
  };
  
  // Call the implementation
  return handleReviseCommandImpl(reviseContext);
}

// Check command handler
export async function handleCheckCommand(
  context: CommandHandlerContext
): Promise<CommandHandlerResult> {
  try {
    const result = await context.chatSource.sendMessage(context.message.prompt);
    context.historyList.push({ role: 'model', parts: result.message });
    context.scrollToBottom();
    return {};
  } catch (error) {
    return { error: error instanceof Error ? error : new Error(String(error)) };
  }
}

// Default (Ask) command handler
export async function handleDefaultCommand(
  context: CommandHandlerContext
): Promise<CommandHandlerResult> {
  try {
    const result = await context.chatSource.sendMessage(context.message.prompt);
    context.historyList.push({ role: 'model', parts: result.message });
    context.scrollToBottom();
    return {};
  } catch (error) {
    return { error: error instanceof Error ? error : new Error(String(error)) };
  }
} 