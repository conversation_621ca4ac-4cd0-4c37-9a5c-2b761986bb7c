<script setup lang="ts">
import { onMounted, computed } from 'vue';
import Tribute, { type TributeCollection } from 'tributejs';
import { isMobile } from 'is-mobile';

const props = withDefaults(defineProps<{ collection: TributeCollection<{ [key: string]: any; }>[] | undefined }>(), {
  collection: undefined
});

const emit = defineEmits(['submit'])

const tribute = props.collection ? new Tribute({
  collection: props.collection
}) : undefined;

// --- Device Detection ---
const isDeviceMobile = isMobile();
const isMac = !isDeviceMobile && /Mac|iPhone|iPad|iPod/i.test(navigator.userAgent); // Check Mac only if not mobile

// --- Computed Placeholder ---
const placeholderText = computed(() => {
  if (isDeviceMobile) {
    return "Message here...";
  } else {
    return isMac
      ? "Message here... (⌘+Enter to send)"
      : "Message here... (Ctrl+Enter to send)";
  }
});

onMounted(() => {
  tribute?.attach(document.getElementById("chat")!);

  const chatBox = document.getElementById("chat") as HTMLInputElement;
  chatBox.addEventListener('keydown', handleKeyPress);
});

function submit() {
  const chatBox = document.getElementById("chat") as HTMLInputElement;
  const text = chatBox.value;
  if (text !== '') {
    chatBox.value = '';
    emit('submit', text);
  }
}

function handleKeyPress(e: KeyboardEvent) {
  // Use pre-calculated isMac for consistency
  const modifierKeyPressed = isMac ? e.metaKey : e.ctrlKey;

  // Only apply shortcut logic on non-mobile devices
  if (!isDeviceMobile && modifierKeyPressed && e.key === 'Enter') {
    submit();
    e.preventDefault(); // Prevent default Enter behavior (newline) only when submitting via shortcut
  }
}
</script>

<template>
  <form>
    <label for="chat" class="sr-only">Your message</label>
    <div class="flex items-center px-3 py-2 rounded-lg bg-gray-50 dark:bg-gray-700">
      <!-- <button type="button"
        class="inline-flex justify-center p-2 text-gray-500 rounded-lg cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-600">
        <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 18">
          <path fill="currentColor"
            d="M13 5.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0ZM7.565 7.423 4.5 14h11.518l-2.516-3.71L11 13 7.565 7.423Z" />
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M18 1H2a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1Z" />
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M13 5.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0ZM7.565 7.423 4.5 14h11.518l-2.516-3.71L11 13 7.565 7.423Z" />
        </svg>
        <span class="sr-only">Upload image</span>
      </button>
      <button type="button"
        class="p-2 text-gray-500 rounded-lg cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-600">
        <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M13.408 7.5h.01m-6.876 0h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0ZM4.6 11a5.5 5.5 0 0 0 10.81 0H4.6Z" />
        </svg>
        <span class="sr-only">Add emoji</span>
      </button> -->
      <textarea id="chat" rows="1"
        class="min-h-[50px] max-h-[300px] block mx-4 p-2.5 w-full text-sm text-gray-900 bg-white rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
        :placeholder="placeholderText"></textarea>
      <div
        @click="submit()"
        class="inline-flex justify-center p-2 text-blue-600 rounded-full cursor-pointer hover:bg-blue-100 dark:text-blue-500 dark:hover:bg-gray-600">
        <svg class="w-5 h-5 rotate-90 rtl:-rotate-90" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
          fill="currentColor" viewBox="0 0 18 20">
          <path
            d="m17.914 18.594-8-18a1 1 0 0 0-1.828 0l-8 18a1 1 0 0 0 1.157 1.376L8 18.281V9a1 1 0 0 1 2 0v9.281l6.758 1.689a1 1 0 0 0 1.156-1.376Z" />
        </svg>
        <span class="sr-only">Send message</span>
      </div>
    </div>
  </form>
</template>

<style scoped>
#chat {
  field-sizing: content;
}
</style>
