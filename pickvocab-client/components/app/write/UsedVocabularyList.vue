<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'; // Removed PropType import
import type { Card } from '~/utils/card'; // Import Card type
import VocabularyCard from './VocabularyCard.vue';
import Spinner from '~/components/app/utils/Spinner.vue'; // Import Spinner

// Removed local VocabularyCardData interface

const props = defineProps<{
  usedVocabularyCards: Card[]; // Use simpler generic syntax
  isLoadingVocabularyCards?: boolean; // Add loading prop
}>();

const emits = defineEmits([]); // Removed 'viewVocabularyDetails'

// Removed handleViewDetails function
</script>

<template>
  <!-- Show spinner when loading vocabulary cards -->
  <div v-if="isLoadingVocabularyCards" class="flex justify-center items-center p-4">
    <Spinner :size="'6'" />
  </div>
  <!-- Show content when not loading -->
  <div v-else class="space-y-3">
    <VocabularyCard 
      v-for="(card, index) in usedVocabularyCards" 
      :key="card.id"
      :card="card"
    />
    <!-- Optional: Add a message if the list is empty -->
    <p v-if="!usedVocabularyCards || usedVocabularyCards.length === 0" class="text-sm text-gray-500 px-4 py-2">
      No specific vocabulary words were used in this revision.
    </p>
  </div>
</template>

<style scoped>
/* Component-specific styles if needed */
</style>