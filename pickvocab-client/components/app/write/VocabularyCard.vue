<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { Card, DefinitionCard, ContextCard } from '~/utils/card'; // Import Card types
import { CardType } from '~/utils/card'; // Import the CardType enum
import DefinitionCardEntry from '~/components/app/cards/DefinitionCardEntry.vue'; // Import entry component
import ContextCardEntry from '~/components/app/cards/ContextCardEntry.vue'; // Import entry component
// Removed unused imports: computed, IconChevronRight, stylesForPartOfSpeech

// No longer need the local VocabularyCardData interface

const props = defineProps<{
  card: Card; // Use the imported Card type
}>();

const emits = defineEmits([]); // Removed 'viewDetails'

// Removed computed properties as rendering is delegated
</script>

<template>
  <NuxtLink
    :to="'/app/cards/' + card.id"
    target="_blank"
    rel="noopener noreferrer"
    class="p-4 border border-gray-200 rounded-lg bg-white hover:border-gray-300 hover:bg-gray-50 active:bg-gray-100 transition-all duration-150 cursor-pointer shadow-sm block"
  > <!-- Moved comment outside the tag -->
    <!-- Conditionally render the appropriate entry component -->
    <DefinitionCardEntry
      v-if="card.cardType === CardType.DefinitionCard"
      :card="card as DefinitionCard"
      class="!p-0 !border-none !shadow-none !bg-transparent hover:!bg-transparent"
    />
    <ContextCardEntry
      v-else-if="card.cardType === CardType.ContextCard"
      :card="card as ContextCard"
      class="!p-0 !border-none !shadow-none !bg-transparent hover:!bg-transparent"
    />
    <!-- Removed direct display elements -->
  </NuxtLink> <!-- Closing tag for the root NuxtLink -->
</template>

<style scoped>
/* No custom styles needed */
</style>