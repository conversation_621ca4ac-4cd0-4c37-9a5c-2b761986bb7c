<script setup lang="ts">
import { ref, type PropType, watch } from 'vue';
import type { Card } from '~/utils/card'; // Import the base Card type
import OutputSectionHeader from './OutputSectionHeader.vue';
import RevisedTextViewer from './RevisedTextViewer.vue';
import UsedVocabularyList from './UsedVocabularyList.vue';
import LLMFeedbackDisplay from './LLMFeedbackDisplay.vue';
import CollapsibleSection from '~/components/ui/CollapsibleSection.vue';
import Spinner from '~/components/app/utils/Spinner.vue'; // Import Spinner
// @ts-ignore
import IconMessageCircle from '@tabler/icons-vue/dist/esm/icons/IconMessageCircle.mjs';
// @ts-ignore
import IconChevronLeft from '@tabler/icons-vue/dist/esm/icons/IconChevronLeft.mjs';
// @ts-ignore
import IconChevronRight from '@tabler/icons-vue/dist/esm/icons/IconChevronRight.mjs';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';

const props = defineProps({
  isLoading: { type: Boolean, required: true },
  isLoadingVocabularyCards: { type: Boolean, required: true }, // Add loading prop for vocabulary cards
  revisedText: { type: String, required: true },
  // Removed feedbackMessage prop
  llmFeedbackText: { type: String, required: true },
  learningFocus: { type: Array as PropType<string[]>, required: false, default: () => [] },
  usedVocabularyCards: {
    type: Array as PropType<Card[]>, // Use the base Card type
    required: true
  },
  currentRevisionIndex: { type: Number, required: true },
  totalRevisions: { type: Number, required: true },
  vocabularyWasUsed: { type: Boolean, required: true } // Add new prop
});

const showLLMFeedback = defineModel<boolean>('showLLMFeedback', { default: false });
const showUsedVocabulary = defineModel<boolean>('showUsedVocabulary', { default: false });

const emits = defineEmits([
  'copyRevisedText',
  'refreshRevision',
  'toggleLLMFeedback',
  'toggleUsedVocabulary',
  'update:showLLMFeedback',
  'update:showUsedVocabulary',
  'navigateRevision',
]);


function navigate(direction: 'prev' | 'next') {
  emits('navigateRevision', direction);
}
</script>

<template>
  <!-- Show spinner when loading -->
  <div v-if="props.isLoading" class="flex justify-center items-center p-10">
    <Spinner :size="'8'" />
  </div>
  <!-- Show content when not loading -->
  <div v-else class="bg-white border border-gray-200 rounded-lg shadow-sm sm:p-5 px-2 py-5 space-y-4">
    <OutputSectionHeader
      @refresh-revision="emits('refreshRevision')"
      @copy-revised-text="emits('copyRevisedText')"
    />

    <RevisedTextViewer :revised-text="props.revisedText" />

    <!-- Revision Navigation Controls -->
    <div v-if="props.totalRevisions > 1" class="flex items-center justify-center space-x-4 mt-2 mb-2">
      <button
        @click="navigate('prev')"
        :disabled="props.currentRevisionIndex === 0"
        class="p-2 rounded-md text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Previous revision"
      >
        <IconChevronLeft class="w-5 h-5" />
      </button>
      <span class="text-sm font-medium text-gray-700">
        Revision {{ props.currentRevisionIndex + 1 }} of {{ props.totalRevisions }}
      </span>
      <button
        @click="navigate('next')"
        :disabled="props.currentRevisionIndex >= props.totalRevisions - 1"
        class="p-2 rounded-md text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Next revision"
      >
        <IconChevronRight class="w-5 h-5" />
      </button>
    </div>


    <CollapsibleSection
      v-model="showLLMFeedback"
      :title="'Feedback'"
      :icon-component="IconMessageCircle"
    >
      <LLMFeedbackDisplay
        :llm-feedback-text="props.llmFeedbackText"
        :learning-focus="props.learningFocus"
      />
    </CollapsibleSection>

    <!-- Conditionally render Used Vocabulary section -->
    <CollapsibleSection
      v-if="props.vocabularyWasUsed"
      v-model="showUsedVocabulary"
      :title="'Used Vocabulary'"
      :icon-component="IconBook2"
      :item-count="props.usedVocabularyCards.length"
    >
      <UsedVocabularyList
        :used-vocabulary-cards="props.usedVocabularyCards"
        :is-loading-vocabulary-cards="props.isLoadingVocabularyCards"
      />
    </CollapsibleSection>
  </div>
</template>

<style scoped>
/* No custom styles needed */
</style>