<script setup lang="ts">
import {
  Fwb<PERSON><PERSON><PERSON>,
  Fwb<PERSON>avbar<PERSON>oll<PERSON><PERSON>,
  FwbButton,
} from "flowbite-vue";
import HomeFooter from "~/components/home/<USER>";

useSeoMeta({
  title: 'Elevate Your Writing with Pickvocab\'s AI Assistant',
  description: 'Improve writing fluency and use your vocabulary in real writing with our AI-powered assistant - perfect for language learners.',
  ogTitle: 'Pickvocab Writing Assistant - Enhanced Writing with Your Vocabulary',
  ogDescription: 'Improve writing clarity, fix grammar issues, and put your vocabulary knowledge into active use.'
});

const appUrl = '/app';
const demoUrl = '/demo';
const writeAppUrl = '/app/write';
</script>

<template>
  <div class="font-sans antialiased text-gray-900">
    <!-- Navigation -->
    <fwb-navbar class="max-w-screen-xl p-4 flex mx-auto">
      <template #logo>
        <a href="/" class="flex items-center space-x-3 rtl:space-x-reverse">
          <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">pickvocab</span>
        </a>
      </template>
      <template #default="{ isShowMenu }">
        <fwb-navbar-collapse :is-show-menu="isShowMenu">
          <div class="flex flex-col mt-4 md:hidden space-y-2">
            <a :href="appUrl">
              <fwb-button class="w-full">Get started</fwb-button>
            </a>
            <a :href="demoUrl">
              <fwb-button class="w-full bg-gray-200 hover:bg-gray-300 text-gray-700">Demo</fwb-button>
            </a>
          </div>
        </fwb-navbar-collapse>
      </template>
      <template #right-side>
        <a :href="appUrl">
          <fwb-button>Get started</fwb-button>
        </a>
        <a :href="demoUrl">
          <fwb-button class="bg-gray-200 hover:bg-gray-300 ml-2 text-gray-700">Demo</fwb-button>
        </a>
      </template>
    </fwb-navbar>

    <!-- Combined Hero & Demo Section -->
    <div class="bg-white py-20 md:py-24 relative overflow-hidden">
      <div class="absolute top-0 right-0 w-96 h-96 bg-gray-100 rounded-full opacity-20 transform translate-x-1/3 -translate-y-1/3"></div>
      <div class="absolute bottom-0 left-0 w-64 h-64 bg-gray-100 rounded-full opacity-20 transform -translate-x-1/3 translate-y-1/3"></div>
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center max-w-3xl mx-auto mb-14">
          <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-blue-600">Writing Assistant</h1>
          <p class="lg:text-lg text-gray-700 mb-8 leading-relaxed">Polish your writing while actively using the vocabulary you've learned.</p>
          <div class="flex justify-center mb-10">
            <a :href="writeAppUrl">
              <fwb-button class="text-sm px-6 py-2.5 shadow hover:shadow-md transition-all duration-300 font-semibold">Try it now →</fwb-button>
            </a>
          </div>
        </div>
        
        <div class="max-w-5xl mx-auto">
          <div class="relative w-full rounded-xl overflow-hidden shadow-xl border border-gray-200 mt-6">
            <div class="w-full aspect-video bg-gradient-to-r from-blue-50 to-indigo-50 flex items-center justify-center">
              <div class="text-center p-6">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-blue-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p class="text-blue-600 font-medium">Demo Video Placeholder</p>
                <p class="text-gray-500 text-sm mt-2">Your demo video will appear here</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Feature Section 1: Writing Enhancement -->
    <div class="py-20 md:py-28 bg-gray-50 border-t border-gray-100">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-2 gap-12 lg:gap-16 items-center">
          <div class="order-2 md:order-1">
            <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-800">Improve your writing fluency and clarity</h2>
            <p class="text-gray-600 text-base mb-8 leading-relaxed">
              Fix awkward phrasing and grammar errors automatically. Our assistant helps you write more naturally and professionally, enhancing clarity and ensuring consistent tone.
            </p>
          </div>
          <div class="order-1 md:order-2">
            <div class="bg-gradient-to-tr from-blue-100 to-blue-50 rounded-xl w-full aspect-video flex items-center justify-center shadow-lg overflow-hidden border border-blue-200">
              <div class="relative w-full h-full flex items-center justify-center">
                <div class="absolute inset-0 bg-blue-500 opacity-5 pattern-dots"></div>
                <span class="text-blue-600 font-medium text-lg">Screenshot showing before/after text improvement</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Feature Section 2: Vocabulary Application -->
    <div class="py-20 md:py-28 bg-white border-t border-gray-100">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-2 gap-12 lg:gap-16 items-center">
          <div>
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl w-full aspect-video flex items-center justify-center shadow-lg overflow-hidden border border-blue-200">
              <div class="relative w-full h-full flex items-center justify-center">
                <div class="absolute inset-0 bg-indigo-500 opacity-5 pattern-dots"></div>
                <span class="text-indigo-600 font-medium text-lg">Screenshot showing vocabulary integration</span>
              </div>
            </div>
          </div>
          <div>
            <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-800">Use the words you've learned</h2>
            <p class="text-gray-600 text-base mb-6 leading-relaxed">
              Our assistant seamlessly integrates words from your notebooks into your writing.
            </p>
            <ul class="space-y-4">
              <li class="flex items-start">
                <span class="flex-shrink-0 inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-600">
                  <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 11.917 9.724 16.5 19 7.5" />
                  </svg>
                </span>
                <span class="ml-3 text-gray-700">Automatically finds relevant vocabulary from your notebooks</span>
              </li>
              <li class="flex items-start">
                <span class="flex-shrink-0 inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-600">
                  <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 11.917 9.724 16.5 19 7.5" />
                  </svg>
                </span>
                <span class="ml-3 text-gray-700">Intelligently incorporates your learned words into natural, fluent text</span>
              </li>
              <li class="flex items-start">
                <span class="flex-shrink-0 inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-600">
                  <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 11.917 9.724 16.5 19 7.5" />
                  </svg>
                </span>
                <span class="ml-3 text-gray-700">Make vocabulary stick by using it in practice.</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Feature Section 3: Revision History -->
    <div class="py-20 md:py-28 bg-gray-50 border-t border-gray-100">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-2 gap-12 lg:gap-16 items-center">
          <div class="order-2 md:order-1">
            <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-800">Track your progress over time</h2>
            <p class="text-gray-600 text-base mb-8 leading-relaxed">
              See your writing evolve. Access your revision history, compare versions, and track which vocabulary you've successfully put into practice.
            </p>
          </div>
          <div class="order-1 md:order-2">
            <div class="bg-gradient-to-tr from-teal-50 to-blue-50 rounded-xl w-full aspect-video flex items-center justify-center shadow-lg overflow-hidden border border-teal-200">
              <div class="relative w-full h-full flex items-center justify-center">
                <div class="absolute inset-0 bg-teal-500 opacity-5 pattern-dots"></div>
                <span class="text-teal-600 font-medium text-lg">Screenshot of revision history interface</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Feature Section 4: Learning Feedback -->
    <div class="py-20 md:py-28 bg-white border-t border-gray-100">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-2 gap-12 lg:gap-16 items-center">
          <div>
            <div class="bg-gradient-to-bl from-purple-50 to-blue-50 rounded-xl w-full aspect-video flex items-center justify-center shadow-lg overflow-hidden border border-purple-200">
              <div class="relative w-full h-full flex items-center justify-center">
                <div class="absolute inset-0 bg-purple-500 opacity-5 pattern-dots"></div>
                <span class="text-purple-600 font-medium text-lg">Screenshot of feedback interface</span>
              </div>
            </div>
          </div>
          <div>
            <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-800">Learn from detailed feedback</h2>
            <p class="text-gray-600 text-base mb-6 leading-relaxed">
              Get personalized feedback with every revision. Understand grammar patterns, see how vocabulary was used, and get suggestions for incorporating more words from your notebooks.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Final CTA Section -->
    <div class="py-20 md:py-28 bg-blue-50 border-t border-gray-100">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-3xl text-center">
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-800">Start improving your writing today</h2>
        <p class="lg:text-lg text-gray-700 mb-10 leading-relaxed">Take the next step in your language learning journey by refining your writing and using the words you learn.</p>
        <a :href="writeAppUrl">
          <fwb-button class="text-sm px-7 py-2.5 shadow hover:shadow-md transition-all duration-300 font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 border-0">✨ Try the Writing Assistant</fwb-button>
        </a>
      </div>
    </div>

    <!-- Footer -->
    <HomeFooter />
  </div>
</template>

<style scoped>
.pattern-dots {
  background-image: radial-gradient(currentColor 1px, transparent 1px);
  background-size: 20px 20px;
}
</style> 