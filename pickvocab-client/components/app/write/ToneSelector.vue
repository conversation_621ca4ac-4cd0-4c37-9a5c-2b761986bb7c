<script setup lang="ts">
import { computed } from 'vue'; // Import computed
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectSeparator
} from '~/components/ui/select'
// @ts-ignore
import IconSettings from '@tabler/icons-vue/dist/esm/icons/IconSettings.mjs'
import { useToneStore, type SelectedToneIdentifier } from '~/stores/toneStore'; // Import store and type

const toneStore = useToneStore();

// Use computed property for v-model with object comparison
// Needed because v-model might not correctly compare objects by reference when updating
const selectedToneModel = computed({
  get: () => toneStore.selectedTone,
  set: (value: SelectedToneIdentifier | null) => {
    toneStore.selectTone(value);
  }
});

// Get selected tone details for display in the trigger
const selectedToneDetails = computed(() => toneStore.selectedToneDetails);

// Get color classes for the selected tone
const selectedToneColors = computed(() => {
  if (!toneStore.selectedTone) return { bgColor: '', textColor: '' };
  return toneStore.getToneColors(toneStore.selectedTone);
});

const props = defineProps<{
  disabled?: boolean
}>()

const emits = defineEmits<{
  (e: 'openManageTones'): void
}>()
</script>

<template>
  <Select v-model="selectedToneModel" :disabled="props.disabled">
    <SelectTrigger :class="['w-40', props.disabled ? 'bg-gray-100 border-gray-200' : 'bg-white']">
      <!-- Display selected tone name with color, handle null case -->
      <div v-if="selectedToneDetails" class="truncate flex items-center" :class="{ 'opacity-70': props.disabled }">
        <span
          class="h-3 w-3 rounded-full mr-1.5 inline-block"
          :class="selectedToneColors.bgColor"
        ></span>
        <span class="truncate">
          {{ selectedToneDetails?.name || 'Select tone...' }}
        </span>
      </div>
      <div v-else class="truncate" :class="{ 'opacity-70': props.disabled }">Select tone...</div>
    </SelectTrigger>
    <SelectContent class="w-[calc(100vw-2rem)] sm:max-w-[450px]">
      <!-- Group for Predefined Tones -->
      <SelectGroup v-if="toneStore.predefinedTones.length > 0">
        <SelectLabel>Default tones</SelectLabel>
        <SelectItem
          v-for="tone in toneStore.predefinedTones"
          :key="`predefined-${tone.name}`"
          :value="{ type: 'predefined', identifier: tone.name } as SelectedToneIdentifier"
        >
          <div class="flex items-center">
            <span
              class="h-3 w-3 rounded-full mr-1.5 inline-block"
              :class="tone.bgColor"
            ></span>
            <div class="ml-1">
              <div>{{ tone.name }}</div>
              <div class="text-xs text-gray-500">{{ tone.description }}</div>
            </div>
          </div>
        </SelectItem>
      </SelectGroup>

      <!-- Separator if both groups exist -->
      <SelectSeparator v-if="toneStore.predefinedTones.length > 0 && toneStore.customTones.length > 0" />

      <!-- Group for Custom Tones -->
      <SelectGroup v-if="toneStore.customTones.length > 0">
        <SelectLabel>Custom tones</SelectLabel>
        <SelectItem
          v-for="tone in toneStore.customTones"
          :key="`custom-${tone.id}`"
          :value="{ type: 'custom', identifier: tone.id } as SelectedToneIdentifier"
        >
          <div class="flex items-center w-full pr-0">
            <span
              class="h-3 w-3 rounded-full mr-1.5 inline-block"
              :class="tone.bgColor"
            ></span>
            <div class="flex-grow ml-1">
              <div>{{ tone.name }}</div>
              <div class="text-xs text-gray-500">{{ tone.description || 'No description' }}</div>
            </div>
            <!-- Optional: Add edit/delete icons here later if needed directly in selector -->
          </div>
        </SelectItem>
      </SelectGroup>

      <div class="py-2 px-2">
        <button
          class="w-full flex items-center justify-center space-x-1 py-1.5 px-2 text-sm rounded-md hover:bg-gray-100 transition-colors"
          @click.stop="emits('openManageTones')"
        >
          <IconSettings class="h-4 w-4 mr-1" />
          <span>Manage custom tones</span>
        </button>
      </div>
    </SelectContent>
  </Select>
</template>