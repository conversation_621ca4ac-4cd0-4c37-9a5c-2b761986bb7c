<script setup lang="ts">
// @ts-ignore
import IconWand from '@tabler/icons-vue/dist/esm/icons/IconWand.mjs'
// @ts-ignore
import IconEdit from '@tabler/icons-vue/dist/esm/icons/IconEdit.mjs'
// @ts-ignore
import IconAbc from '@tabler/icons-vue/dist/esm/icons/IconAbc.mjs'
// @ts-ignore
import IconAlertCircle from '@tabler/icons-vue/dist/esm/icons/IconAlertCircle.mjs'

import { Switch } from '~/components/ui/switch'

// New extracted components
import HistoryDialog from './HistoryDialog.vue'
import VocabularyToggle from './VocabularyToggle.vue'
import ToneSelector from './ToneSelector.vue'

import type { WritingRevisionHistory } from '~/api/writeHistory';

const userText = defineModel<string>('userText', { required: true })
import type { SelectedToneIdentifier } from '~/stores/toneStore';
const selectedTone = defineModel<SelectedToneIdentifier | null>('selectedTone', { required: true })
const useVocabulary = defineModel<boolean>('useVocabulary', { required: true })
const grammarCheck = defineModel<boolean>('grammarCheck', { required: true })

const props = defineProps<{
  isRevising?: boolean, // Only prop needed now
  hasVocabularyCards?: boolean // New prop to check if user has vocabulary cards
}>()

const emits = defineEmits([
  'revise',
  'openManageTones',
  'restoreHistory'
]);

function handleRestore(historyEntry: WritingRevisionHistory) {
  emits('restoreHistory', historyEntry);
}

function selectAllText(event: FocusEvent) {
  const target = event.target as HTMLTextAreaElement;
  if (target) {
    target.select();
  }
}

function handleVocabularyToggle(newValue: boolean) {
  if (grammarCheck.value) {
    grammarCheck.value = false;
  }
  useVocabulary.value = newValue;
}

function handleGrammarCheckToggle(newValue: boolean) {
  if (newValue && useVocabulary.value) {
    useVocabulary.value = false;
  }
  grammarCheck.value = newValue;
}
</script>

<template>
  <div class="bg-white border border-gray-200 rounded-lg shadow-sm sm:p-5 px-2 py-5">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold text-gray-800 flex items-center">
        <IconEdit class="h-5 w-5 text-blue-600 mr-2" />
        Your Text
      </h2>
      <HistoryDialog :on-restore="handleRestore" />
    </div>

    <textarea
      v-model="userText"
      class="mt-2 w-full min-h-[200px] text-sm p-4 border border-gray-200 rounded-md shadow-inner focus:outline-none focus:ring-blue-500 bg-white"
      placeholder="Enter your text here..."
      @focus="selectAllText"
    ></textarea>

    <div class="mt-4 space-y-4">
      <VocabularyToggle
        v-model:useVocabulary="useVocabulary"
        @update:useVocabulary="handleVocabularyToggle"
      />
      <p v-if="useVocabulary" class="mt-2 text-xs text-gray-500">
        ✨ Works best with Gemini 2.0 Flash
      </p>
      
      <!-- No vocabulary cards warning message -->
      <div v-if="useVocabulary && props.hasVocabularyCards === false" class="mt-2 p-3 bg-amber-50 border border-amber-100 rounded-md flex items-start">
        <IconAlertCircle class="h-4 w-4 text-amber-600 mr-2 mt-0.5 flex-shrink-0" />
        <p class="text-xs text-amber-800">
          You don't have any vocabulary cards saved. This option requires saved vocabulary cards to work.
        </p>
      </div>

      <div>
        <div class="flex items-center">
          <Switch
            :model-value="grammarCheck"
            @update:model-value="handleGrammarCheckToggle"
            class="data-[state=checked]:!bg-blue-700"
          />
          <div class="flex items-center ml-2">
            <IconAbc class="h-5 w-5 text-blue-600 mr-1.5" />
            <span class="text-sm font-medium text-gray-800">Grammar check only</span>
          </div>
        </div>

        <div v-if="grammarCheck" class="mt-4 bg-blue-50 p-4 rounded-lg flex border border-blue-100">
          <IconAbc class="h-5 w-5 text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
          <p class="text-sm text-gray-700">
            Only correct spelling, punctuation; do not change style or tone.
          </p>
        </div>
      </div>
    </div>

    <div class="mt-5 flex justify-end space-x-3">
      <ToneSelector
        v-model:selectedTone="selectedTone"
        @openManageTones="$emit('openManageTones')"
        :disabled="grammarCheck"
      />

      <button
        class="flex items-center px-5 py-2 text-sm bg-blue-700 text-white rounded-md hover:bg-blue-800 font-medium transition-colors shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
        @click="$emit('revise')"
        :disabled="props.isRevising"
      >
        <IconWand class="h-4 w-4 mr-2" />
        <span>{{ grammarCheck ? 'Check' : 'Revise' }}</span>
      </button>
    </div>
  </div>
</template>

<style scoped>
.bg-blue-50 {
  background-color: rgba(239, 246, 255, 0.8);
}
</style>