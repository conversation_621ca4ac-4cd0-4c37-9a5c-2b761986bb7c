# Pickvocab Writing Assistant: Master Architecture Document

## 1. Introduction

The Pickvocab Writing Assistant is a feature designed to help users improve their writing. It leverages Large Language Models (LLMs) to provide revisions based on user-selected tones and optionally incorporates vocabulary words from the user's personal collection ("My Vocabulary"). The assistant allows users to browse multiple revision suggestions, view LLM feedback, see which vocabulary words were used, and manage a persistent history of their revision sessions.

This document outlines the current frontend architecture of the Writing Assistant feature, consolidating information from previous plans and reflecting the actual implementation in the codebase.

## 2. High-Level Architecture Overview

The frontend architecture follows a component-based structure using Vue 3 and Nuxt 3, leveraging composables for logic reuse and Pinia for state management. It interacts with a backend API for data persistence, LLM interactions, and vocabulary management.

**Diagram: Component & Logic Overview**

```mermaid
graph TD
    subgraph "Frontend (Nuxt 3 / Vue 3)"
        PVWI["pages/app/write/index.vue"] -- Manages --> C_IN["InputSection.vue"]
        PVWI -- Manages --> C_OUT["OutputSection.vue"]
        PVWI -- Manages --> <PERSON>_<PERSON>["ToneSelector.vue"]
        PVWI -- Manages --> C_HD["HistoryDialog.vue"]
        PVWI -- Uses --> H_WRH["useWriteRevisionHandler.ts"]
        PVWI -- Uses --> S_TS["stores/toneStore.ts"]
        PVWI -- Manages --> C_MTD["ManageCustomTonesDialog.vue"]

        H_WRH -- Uses --> H_RA["useRevisionApi.ts"]
        H_WRH -- Uses --> P_MV["useMyVocabularyPrompt.ts"]
        H_WRH -- Uses --> P_RS["reviseTextSimplePrompt.ts"]
        H_WRH -- Uses --> P_HL["highlightVocabularyPrompt.ts"]
        H_WRH -- Uses --> RU["revisionUtils.ts"]

        H_RA -- Uses --> API_GC["api/genericCard.ts (...RemoteGenericCardsApi)"]
        H_RA -- Uses --> API_WH["api/writeHistory.ts (WriteHistoryApi)"]
        H_RA -- Uses --> S_LLM["stores/llm.ts (useLLMStore / ChatSource)"]
        H_RA -- Uses --> RU

        S_TS -- Uses --> API_CT["api/write/customTones.ts (CustomTonesApi)"]

        C_IN -- Contains --> C_TD
        C_IN -- Contains --> VT["VocabularyToggle.vue"]
        C_IN -- Emits events to --> PVWI
        C_OUT -- Contains --> OSH["OutputSectionHeader.vue"]
        C_OUT -- Contains --> RTV["RevisedTextViewer.vue"]
        C_OUT -- Contains --> LFD["LLMFeedbackDisplay.vue"]
        C_OUT -- Contains --> UVL["UsedVocabularyList.vue"]
        C_OUT -- Emits events to --> PVWI
        UVL -- Contains --> VC["VocabularyCard.vue"]
        OSH -- Emits events to --> C_OUT
        C_HD -- Emits events to --> PVWI

        C_TD -- Uses --> S_TS
        C_MTD -- Uses --> S_TS
    end

    subgraph Backend API
        B_API["REST API"]
    end

    H_RA --> B_API
    API_GC --> B_API
    API_WH --> B_API
    API_CT --> B_API
    S_LLM --> B_API


    style PVWI fill:#f9f,stroke:#333,stroke-width:2px
    style H_WRH fill:#ccf,stroke:#333,stroke-width:2px
    style H_RA fill:#cdf,stroke:#333,stroke-width:2px
    style S_TS fill:#ffc,stroke:#333,stroke-width:2px
    style S_LLM fill:#ffc,stroke:#333,stroke-width:2px
    style C_IN fill:#eee,stroke:#333,stroke-width:1px
    style C_OUT fill:#eee,stroke:#333,stroke-width:1px
    style API_GC fill:#fec,stroke:#333,stroke-width:1px
    style API_WH fill:#fec,stroke:#333,stroke-width:1px
    style API_CT fill:#fec,stroke:#333,stroke-width:1px
```

**Key Architectural Components:**

*   **Page (`pages/app/write/index.vue`):** The main container component, responsible for orchestrating child components, integrating the core logic composable (`useWriteRevisionHandler`), managing dialogs, and handling local storage persistence for user preferences.
*   **UI Components (`components/app/write/*.vue`):** Reusable Vue components for specific UI sections like input fields (`InputSection`), revision output (`OutputSection`), tone selection (`ToneSelector`), history browsing (`HistoryDialog`), etc.
*   **Core Logic Composable (`useWriteRevisionHandler.ts`):** The heart of the feature's logic. Manages the state related to a single revision request (loading, results, errors, current revision index), orchestrates the revision workflow by calling the API abstraction layer, handles navigation between revisions, and manages history restoration.
*   **API Abstraction Composable (`useRevisionApi.ts`):** Encapsulates all direct interactions with the backend API related to the revision process. It handles similarity search initiation and polling, fetching cards, generating revisions and highlights via LLM calls (using `ChatSource` from `llmStore`), and saving/updating revision history. It utilizes specific API client classes.
*   **State Management (Pinia Stores):**
    *   `stores/toneStore.ts`: Manages the state for predefined and user-created custom writing tones, including fetching, creating, updating, and deleting custom tones via the `CustomTonesApi`.
    *   `stores/llm.ts`: (Used by `useRevisionApi`) Manages LLM configurations and provides `ChatSource` instances for interacting with the LLM backend service.
*   **API Clients (`api/`):** Type-safe classes responsible for making HTTP requests to specific backend endpoints (e.g., `WriteHistoryApi`, `CustomTonesApi`, `RemoteGenericCardsApi`).
*   **Prompt Helpers (`*Prompt.ts`):** Utility functions dedicated to constructing the specific prompts required by the LLM for different tasks (revision with vocabulary, simple revision, highlighting).
*   **Backend API:** Provides RESTful endpoints for authentication, vocabulary card management (CRUD, similarity search), custom tone management, revision history management, and proxying requests to the LLM.

## 3. Core Revision Flow

The process of generating revisions involves several steps, especially when incorporating user vocabulary via similarity search.

**Diagram: Revision Flow (with Vocabulary)**

```mermaid
sequenceDiagram
    participant User
    participant Page as index.vue
    participant Handler as useWriteRevisionHandler
    participant API as useRevisionApi
    participant Store as toneStore
    participant Backend

    User->>Page: Enters text, Selects Tone, Enables Vocab, Clicks Revise
    Page->>Handler: initiateRevision()
    Handler->>Handler: Set isLoading = true, currentTriggerType = 'Revise'
    Handler->>API: startSimilaritySearch(userText)
    API->>Backend: POST /api/cards/similarity-search/
    Backend-->>API: { taskId: '...' }
    API->>API: pollSimilarityResults(taskId)
    Note over API: Polls with exponential backoff until<br/>status is SUCCESS, FAILURE, or timeout
    loop Until SUCCESS/FAILURE/timeout
        API->>Backend: GET /api/cards/similarity-search/{taskId}/
        Backend-->>API: Response with status
    end
    Note over API: On SUCCESS: Returns fetched Card[]<br/>On FAILURE: Throws error that bubbles up to Handler
    API-->>Handler: fetchedCards: Card[] (on success path)
    
    Handler->>Handler: Create cardCacheById, indexToIdMap
    Handler->>Store: Read selectedToneDetails
    Handler->>Handler: Format cards for prompt (formatCardForPrompt)
    Handler->>Handler: Build prompt (useMyVocabularyPrompt)
    Handler->>Handler: generateAndProcessRevisions(prompt, true, indexToIdMap)
    Handler->>Handler: Set isGeneratingRevision = true
    Handler->>API: generateRevision(prompt)
    API->>Backend: POST /api/llm/chat/ (or similar)
    Backend-->>API: LLM Response (YAML)
    API->>Handler: parsedRevisions: RevisionData[] (without real_card_ids)
    Handler->>Handler: processLLMRevisions(parsedRevisions, indexToIdMap) -> adds real_card_ids
    Handler->>Handler: Set allRevisionsResult = processedRevisions
    Handler->>Handler: saveInitialRevisionHistory()
    Handler->>API: saveHistory(historyEntry, cardIds)
    API->>Backend: POST /api/write/history/
    Backend-->>API: Saved History Entry (with ID)
    API-->>Handler: savedEntry
    Handler->>Handler: Set currentHistoryEntryId = savedEntry.id
    Handler->>Handler: Trigger _fetchAndApplyCombinedHighlighting() (async, background)
    Handler->>Handler: Set isGeneratingRevision = false
    Handler->>Handler: Set isLoading = false
    Handler->>Page: Update UI state (display revision 1)

```

**Explanation:**

1.  **User Action:** The user provides text, selects preferences (tone, vocabulary toggle), and clicks "Revise".
2.  **Initiation (`index.vue` -> `useWriteRevisionHandler`):** The page calls `initiateRevision`. The handler sets loading states and determines the flow based on the `useVocabulary` toggle.
3.  **Similarity Search (if vocabulary enabled):**
    *   `useWriteRevisionHandler` calls `startSimilaritySearch` in `useRevisionApi`.
    *   `useRevisionApi` calls the backend to start the search task.
    *   `useRevisionApi` polls the backend for results using `pollSimilarityResults` with exponential backoff until success, failure, or timeout.
    *   On success, `useRevisionApi` returns the fetched `Card` objects to `useWriteRevisionHandler`.
4.  **Prompt Construction:** `useWriteRevisionHandler` formats the fetched cards (if any) and the selected tone details, then uses the appropriate prompt helper (`useMyVocabularyPrompt` or `reviseTextSimplePrompt`) to build the final prompt string.
5.  **LLM Call (`useWriteRevisionHandler` -> `useRevisionApi`):**
    *   `useWriteRevisionHandler` calls `generateRevision` in `useRevisionApi` with the prompt.
    *   `useRevisionApi` selects the appropriate LLM model (via `llmStore`), creates a `ChatSource`, sends the prompt to the backend LLM service, and receives the response.
    *   `useRevisionApi` parses the expected YAML structure from the LLM response.
6.  **Result Processing (`useWriteRevisionHandler`):**
    *   The handler receives the parsed `RevisionData` objects.
    *   It calls `processLLMRevisions` to map LLM vocabulary index references (from `user_vocabularies_used`) to actual card IDs (using the `indexToIdMap` created earlier) and stores them in `real_card_ids`.
    *   The processed revisions (including `real_card_ids`) are stored in the `allRevisionsResult` state ref.
7.  **Initial History Save:** The handler calls `saveInitialRevisionHistory`, which constructs the history entry payload and calls `saveHistory` in `useRevisionApi`. `useRevisionApi` uses `WriteHistoryApi` to send the data to the backend. The ID of the saved entry is stored.
8.  **Background Highlighting Trigger:** If vocabulary was used, the `_fetchAndApplyCombinedHighlighting` process is triggered asynchronously (see next section).
9.  **UI Update:** Loading states are reset, and the UI (`index.vue` and its children) updates to display the first revision from `allRevisionsResult`.

**(Simplified Flow without Vocabulary):** Steps 3 and the vocabulary-related parts of steps 4 and 6 are skipped. The `reviseTextSimplePrompt` is used, and no `indexToIdMap` or `real_card_ids` processing is needed. Highlighting (Section 4) is also skipped.

## 4. Vocabulary Highlighting Flow

To visually indicate which vocabulary words were used in the revisions, a separate background process highlights the relevant words within the revised text. This uses a single LLM call for all revisions needing highlights.

**Diagram: Highlighting Flow**

```mermaid
sequenceDiagram
    participant Handler as useWriteRevisionHandler
    participant API as useRevisionApi
    participant Backend

    Note over Handler: Triggered asynchronously after initial history save (if vocab used)
    Handler->>Handler: _fetchAndApplyCombinedHighlighting()
    Handler->>Handler: prepareHighlightingInput(allRevisionsResult) -> revisionsToHighlight
    Note over Handler: If no revisions need highlighting, process stops here
    
    Handler->>Handler: Build prompt (highlightVocabularyPrompt(revisionsToHighlight))
    Handler->>API: generateHighlights(prompt)
    API->>Backend: POST /api/llm/chat/ (or similar)
    Backend-->>API: LLM Response (YAML with highlighted_revisions)
    API->>Handler: parsedHighlights: HighlightedRevisionItem[]
    Handler->>Handler: applyHighlightsToState(parsedHighlights, revisionsToHighlight)
    Handler->>Handler: Create copy of allRevisionsResult
    loop For each parsedHighlight
        Handler->>Handler: Find matching revision in copy by index
        Handler->>Handler: Perform safety checks (length difference)
        Handler->>Handler: Update revision text in copy with highlightedText
    end
    Handler->>Handler: Update allRevisionsResult ref with modified copy
    Handler->>Handler: updateHistoryWithHighlights()
    Handler->>API: updateHistory(currentHistoryEntryId, { revisions: allRevisionsResult })
    API->>Backend: PATCH /api/write/history/{id}/
    Backend-->>API: Updated History Entry
    API-->>Handler: Success/Error (logged)

```

**Explanation:**

1.  **Trigger:** After the initial revisions are processed and saved (if vocabulary was used), `_fetchAndApplyCombinedHighlighting` is called asynchronously.
2.  **Preparation:** The function gathers the revisions that used vocabulary (`real_card_ids` is present) and formats them along with the vocabulary words into the `RevisionHighlightInput` structure needed by the prompt helper.
3.  **Prompt & LLM Call:** `highlightVocabularyPrompt` generates a prompt instructing the LLM to return highlighted versions for all provided revisions in a specific YAML format. `generateHighlights` in `useRevisionApi` sends this prompt to the LLM.
4.  **Parsing:** `useRevisionApi` parses the YAML response, expecting a `highlighted_revisions` list containing `{ index, highlightedText }` objects.
5.  **State Update:** `applyHighlightsToState` receives the highlighted items. It carefully updates a *copy* of the `allRevisionsResult` array, replacing the `revision` text for the corresponding index with the `highlightedText` received from the LLM, performing basic safety checks to prevent major text corruption. The modified copy then replaces the original `allRevisionsResult` ref, triggering reactivity.
6.  **History Update:** If the local state was updated successfully, `updateHistoryWithHighlights` calls `updateHistory` in `useRevisionApi`. `useRevisionApi` uses `WriteHistoryApi` to send a PATCH request to the backend, updating the previously saved history entry with the now-highlighted revision texts.

## 5. Key Components & Logic

*   **`pages/app/write/index.vue`:**
    *   **Role:** Main page component, layout integration.
    *   **Responsibilities:** Integrates `InputSection`, `OutputSection`, etc. Instantiates `useWriteRevisionHandler`. Connects UI events (clicks, input changes) to handler functions or store actions. Manages visibility of `ManageCustomTonesDialog` and `HistoryDialog`. Handles loading/saving `useVocabulary` and `selectedTone` preferences to/from `localStorage` on mount and on change. Provides computed properties derived from handler state (e.g., `totalRevisions`, combined `isLoading`).
*   **`useWriteRevisionHandler.ts`:**
    *   **Role:** Core workflow orchestration and state management for a revision cycle.
    *   **Responsibilities:** Holds reactive state for `userText`, `useVocabulary` (via refs passed in), `isRevising`, `isGeneratingRevision`, `isLoadingVocabularyCards`, `revisionError`, `allRevisionsResult`, `currentRevisionIndex`, `vocabularyWasUsedForLastRevision`, `currentHistoryEntryId`, `cardCacheById`. Exposes functions like `initiateRevision`, `refreshRevision`, `restoreFromHistory`, `nextRevision`, `previousRevision`. Coordinates calls to `useRevisionApi` for backend interactions. Processes results from the API layer (e.g., mapping vocabulary indices to IDs). Manages the sequence of operations (search -> prompt -> LLM -> save -> highlight -> update).
*   **`useRevisionApi.ts`:**
    *   **Role:** Abstraction layer for all backend and LLM API interactions.
    *   **Responsibilities:** Provides async functions (`startSimilaritySearch`, `pollSimilarityResults`, `generateRevision`, `generateHighlights`, `saveHistory`, `updateHistory`, `fetchCardsByIds`). Handles communication with specific API clients (`RemoteGenericCardsApi`, `WriteHistoryApi`). Manages LLM interaction setup (getting model config and creating `ChatSource` via `llmStore`). Parses API/LLM responses (including YAML). Implements polling logic.
*   **`stores/toneStore.ts`:**
    *   **Role:** Centralized state management for writing tones.
    *   **Responsibilities:** Holds state for `predefinedTones`, `customTones` (fetched from API), `selectedTone` (object identifier), `isLoading`, `error`. Provides actions for CRUD operations on custom tones (using `CustomTonesApi`), fetching custom tones, selecting a tone. Provides getters for `allAvailableTones` (combined list for UI) and `selectedToneDetails`.
*   **API Clients (`api/` directory):**
    *   **Role:** Low-level HTTP request handling.
    *   **Responsibilities:** Classes like `WriteHistoryApi`, `CustomTonesApi`, `RemoteGenericCardsApi` contain methods that map directly to backend REST API endpoints. They handle constructing request URLs, bodies, headers, making the HTTP call (using a shared Axios instance or similar), and returning the response data, often performing basic snake_case to camelCase conversion.
*   **Prompt Helpers (`*Prompt.ts`):**
    *   **Role:** Formatting prompts for the LLM.
    *   **Responsibilities:** Functions like `useMyVocabularyPrompt`, `reviseTextSimplePrompt`, `highlightVocabularyPrompt` take inputs (user text, tone description, vocabulary list, revisions to highlight) and return a formatted string prompt tailored for the specific LLM task, often including instructions for the desired output format (e.g., YAML).
*   **Backend (Conceptual):**
    *   **Role:** Data persistence, business logic, LLM interaction gateway.
    *   **Responsibilities:** User authentication/authorization. CRUD operations for `GenericCard`, `CustomTone`, `WritingRevisionHistory`. Endpoint for initiating similarity searches (likely queuing a background task) and retrieving results. Endpoint(s) to interact with the configured LLM service (potentially handling API keys, context management, etc.).

## 6. Data Structures

Key TypeScript interfaces define the shape of data flowing through the system:

*   **`RevisionData` (`useRevisionApi.ts`):** Represents a single revision object, typically returned by the LLM and stored in history. Includes `revision` (text), `feedback`, `learning_focus` (string array), `user_vocabularies_used` (optional string array of LLM indices), and `real_card_ids` (optional string array of actual Card IDs).
*   **`HighlightedRevisionItem` (`useRevisionApi.ts`):** Represents the result of the highlighting LLM call for a single revision. Includes `index` (matching the original revision's index) and `highlightedText`.
*   **`SelectedToneIdentifier` (`toneStore.ts`):** Identifies the currently selected tone. `{ type: 'predefined' | 'custom', identifier: string | number }`.
*   **`HistoryEntryData` (`useRevisionApi.ts`):** Structure used when creating/updating history entries via the API. Includes `triggerType`, `originalText`, `useMyVocabulary`, `selectedTone` (stringified identifier), and the array of `revisions` (`RevisionData[]`).
*   **`Card` (`utils/card.ts`):** Represents a user's vocabulary card (details depend on `CardType`).
*   **`CustomTone` (`api/write/customTones.ts`):** Represents a user-defined custom tone fetched from the backend.

## 7. State Management

State is managed using a combination of local component state (via `ref`, `computed` within composables) and shared state (via Pinia stores):

*   **`useWriteRevisionHandler.ts`:** Manages state specific to a single revision *request cycle*. This includes loading flags (`isRevising`, `isGeneratingRevision`), the results of the current request (`allRevisionsResult`, `currentRevisionIndex`), error messages (`revisionError`), and temporary data like the `cardCacheById`. This state is generally transient and resets when a new revision is initiated.
*   **`stores/toneStore.ts`:** Manages state that needs to be shared across components or persist beyond a single revision cycle, specifically the list of available predefined and custom tones, and the currently selected tone identifier. It fetches and caches custom tones from the backend.
*   **`stores/llm.ts`:** Manages shared state related to LLM configurations and available models.
*   **`pages/app/write/index.vue`:** While not a store, it directly manages the persistence of `useVocabulary` and `selectedTone` to `localStorage`, acting as the bridge between the UI, the handler composable, the tone store, and browser storage for these specific preferences.

## 8. Persistence

User data and preferences are persisted in two ways:

*   **Browser `localStorage`:**
    *   Handled directly in `pages/app/write/index.vue`.
    *   Stores the user's last selected `useVocabulary` setting (boolean stored as string 'true'/'false') under the key `pickvocab.write.useVocabulary`.
    *   Stores the user's last selected tone identifier (`SelectedToneIdentifier` object stringified as JSON) under the key `pickvocab.write.selectedTone`.
    *   These values are loaded on component mount and saved whenever the corresponding refs (`useVocabulary`, `toneStore.selectedTone`) change.
*   **Backend Database:**
    *   **Revision History:** Every completed revision cycle (triggered by "Revise" or "Refresh") results in a `WritingRevisionHistory` entry being saved to the backend via the `WriteHistoryApi`. This includes the original text, user preferences at the time (tone, vocab toggle), the trigger type, and the full list of generated revisions (including feedback, learning focus, and `real_card_ids`). Highlighted text is subsequently updated via a PATCH request.
    *   **Custom Tones:** User-created custom tones are saved in the backend via the `CustomTonesApi` and managed by the `toneStore`.
    *   **Vocabulary Cards:** User vocabulary cards (`GenericCard`) are stored and managed separately in the backend.
