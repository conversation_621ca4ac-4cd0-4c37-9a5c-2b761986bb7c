We have already implemented the UI skeleton for the Write Assistant page. Now, we will progressively add the underlying logic.

Our first goal is to handle user interactions: when the user types their writing, selects a tone, toggles the "Use My Vocabulary" option, and clicks "Revise."

We'll begin by implementing the case where the user enables "Use My Vocabulary."

Upon clicking "Revise" with "Use My Vocabulary" enabled, perform the following steps:
- Build a prompt for LLM to revise user's writing
- The prompt is located at write/useMyVocabularyPrompt.ts
- We need to build a selectedToneStyleDescription = `${toneName} - ${toneDescription}`
- Initiate a similarity search to find up to 20 user vocabulary cards related to the input text by calling:
`const cards = await genericCardApi.startSimilaritySearch(text, { limit: 20 });`
- After that, we need to write `formatCardForPrompt` function to format the card list:
```ts
function formatCardForPrompt(card: Card, idx: number): string {
  if (card.cardType === CardType.DefinitionCard) {
    return {
      word: `word: ${card.word}\ndefinition: ${card.definition.definition || ''}`,
      id: idx,
    };
  } else {
    // Context card
    const wordInContext = card.wordInContext;
    const definition = wordInContext.definition?.definition || wordInContext.definitionShort?.explanation || '';
    return {
      word: `word: ${wordInContext.word}\ndefinition: ${definition}`,
      id: idx
    };
  }
}
```
- userVocabularies = cards.map((card, idx) => formatCardForPrompt(card, idx))
- We pass selectedToneStyleDescription, userText, userVocabularies to build the prompt
- Output the constructed prompt to the console for verification:
`console.log(prompt);`

Focus on completing this flow before proceeding to handle the case when "Use My Vocabulary" is disabled.

## Handling "Use My Vocabulary" Disabled (Plan v6 - Approved 2025-04-07)

When the "Use My Vocabulary" option is disabled, the flow will be adjusted as follows:

1.  **Skip Vocabulary Search:** The similarity search for user vocabulary (lines 11-12) will be skipped entirely.
2.  **New Prompt File:** A new prompt file, `reviseTextSimplePrompt.ts`, will be created in `pickvocab-client/components/app/write/`. This prompt will focus solely on revising the `userText` based on the `selectedToneStyleDescription`, without incorporating user vocabulary. It will instruct the LLM to provide 3 revisions and will define a YAML output format *excluding* the `user_vocabularies_used` field.
3.  **Refactor LLM Call Logic (`useWriteRevisionHandler.ts`):**
    *   The core logic for calling the LLM API, parsing the YAML response, and handling errors/state updates (currently within the polling logic) will be extracted into a reusable private async function (e.g., `_callLLMAndParseResponse(prompt: string)`).
4.  **Implement Disabled Case Flow (`useWriteRevisionHandler.ts`):**
    *   Within the `initiateRevision` function, the `else` block (triggered when `useVocabulary.value` is false) will:
        *   Construct the `selectedToneStyleDescription`.
        *   Call the new `reviseTextSimplePrompt` function to generate the prompt string.
        *   Call the refactored `_callLLMAndParseResponse` function with the generated prompt.
        *   Manage the `isRevising` loading state.
5.  **Update Enabled Case Flow (`useWriteRevisionHandler.ts`):**
    *   The existing logic within `pollSimilarityResults` (after successfully fetching cards) will be updated to call the refactored `_callLLMAndParseResponse` function instead of containing the LLM call logic directly.

**Visual Flow (Vocabulary Disabled):**

```mermaid
sequenceDiagram
    participant User
    participant IV as index.vue
    participant C as useWriteRevisionHandler.ts
    participant SP as reviseTextSimplePrompt.ts
    participant LLM

    User->>IV: Clicks "Revise"
    IV->>C: initiateRevision()
    C->>C: Check useVocabulary (false)
    C->>SP: Generate prompt (no vocab)
    SP-->>C: promptString (simple revision)
    C->>C: _callLLMAndParseResponse(promptString)
    C->>LLM: sendMessage(promptString)
    LLM-->>C: YAML response (no user_vocabularies_used)
    C->>C: Parse YAML, store allRevisionsResult
    C->>C: Set currentRevisionIndex = 0
    C-->>IV: Update state (isLoading=false, computed results)
    IV->>OS: Pass props (OutputSection)
    OS->>User: Display Revision 1/3
```

## LLM Interaction and Response Handling (Plan v4 - Approved 2025-04-07)

After successfully retrieving vocabulary cards from the similarity search (or if "Use My Vocabulary" is disabled, this step will follow directly after prompt construction):

1.  **Create ChatSource:** Inside the `_callLLMAndParseResponse` function, determine the LLM model to use (preferring `llmStore.activeUserModel`, falling back to a default if necessary). Create a **new** `ChatSource` instance for **each revision request** using `llmStore.createChatSource(modelToUse)`. This ensures the correct model is used and the chat history is isolated for each revision.
2.  **Send Prompt:** Call `chatSource.sendMessage(prompt)` using the newly created `chatSource` instance with the appropriate prompt (`useMyVocabularyPrompt` or `reviseTextSimplePrompt`).
3.  **Parse Response:**
    *   The LLM is expected to return a response string containing a YAML code block (as defined in `useMyVocabularyPrompt.ts`).
    *   Extract the YAML content from the code block.
    *   Parse the YAML string using a library (e.g., `js-yaml`). The expected structure contains a `revisions` list.
4.  **Extract Data:**
    *   Safely access the *first* object in the `revisions` list (`parsedResponse?.revisions?.[0]`).
    *   Extract the `revision` (string), `feedback` (string), `learning_focus` (list of strings), and optionally `user_vocabularies_used` (list of IDs, only present if vocabulary was enabled) from this first revision object.
5.  **Update State (`pickvocab-client/components/app/write/useWriteRevisionHandler.ts`):**
    *   Store the extracted data in dedicated reactive refs (e.g., `revisedTextResult`, `llmFeedbackResult`, `learningFocusResult`, `usedVocabularyIdsResult`).
    *   Map the `usedVocabularyIdsResult` (if present) back to the original `Card` objects fetched earlier to populate `usedVocabularyCardsResult`.
    *   Manage loading (`isRevising`) and error (`revisionError`) states.
    *   Return these state refs from the composable.
6.  **Update UI (`index.vue`):**
    *   Receive the new state refs from the composable.
    *   Pass `revisedTextResult`, `llmFeedbackResult`, `learningFocusResult`, and `usedVocabularyCardsResult` as props to the `OutputSection` component.
    *   Display any errors using the `revisionError` state.
7.  **Update UI (`OutputSection.vue`):**
    *   Accept `learningFocus` as a new prop (list of strings).
    *   Pass both `llmFeedbackText` and `learningFocus` props down to the `LLMFeedbackDisplay` component.
8.  **Update UI (`LLMFeedbackDisplay.vue`):**
    *   Accept `learningFocus` as a new prop.
    *   Modify the template to render the `learningFocus` list (e.g., under a subheading) directly within the same visual container as the main `llmFeedbackText`.

This ensures the data remains structured while achieving the desired combined display in the UI.

## Revision Navigation (Plan v5 - Approved 2025-04-07)

To allow users to browse through multiple LLM revisions, the following changes will be implemented:

1.  **State Management (`pickvocab-client/components/app/write/useWriteRevisionHandler.ts`):**
    *   Store the entire `revisions` array from the LLM response in a new reactive ref (`allRevisionsResult`).
    *   Add a reactive ref (`currentRevisionIndex`) to track the currently viewed revision (defaulting to 0).
    *   Convert existing result refs (`revisedTextResult`, `llmFeedbackResult`, etc.) to `computed` properties that derive their value from `allRevisionsResult[currentRevisionIndex]`.
    *   Implement `nextRevision()` and `previousRevision()` functions to modify `currentRevisionIndex`.
    *   Expose the new state and functions from the composable.
2.  **Page Component (`index.vue`):**
    *   Receive the new state (`currentRevisionIndex`, `allRevisionsResult.length`) and functions (`nextRevision`, `previousRevision`) from the composable.
    *   Pass `currentRevisionIndex` and `totalRevisions` as props to `OutputSection`.
    *   Listen for navigation events from `OutputSection` and trigger the corresponding composable functions.
3.  **Output Component (`OutputSection.vue` / `OutputSectionHeader.vue`):**
    *   Accept `currentRevisionIndex` and `totalRevisions` as props.
    *   Add "Previous" and "Next" buttons.
    *   Display "Revision {currentRevisionIndex + 1} of {totalRevisions}".
    *   Disable buttons appropriately based on the current index.
    *   Emit navigation events (`navigateRevision`) when buttons are clicked.

**Visual Flow:**

```mermaid
sequenceDiagram
    participant User
    participant IV as index.vue
    participant OS as OutputSection.vue
    participant OSH as OutputSectionHeader.vue
    participant C as useWriteRevisionHandler.ts (Composable)
    participant LLM

    User->>IV: Clicks "Revise"
    IV->>C: initiateRevision()
    C->>LLM: sendMessage(prompt)
    LLM-->>C: YAML response (with 3 revisions)
    C->>C: Parse YAML, store allRevisionsResult = [rev1, rev2, rev3]
    C->>C: Set currentRevisionIndex = 0
    C-->>IV: Update state (isLoading=false, computed results for index 0)
    IV->>OS: Pass props (computed results, index 0, total 3)
    OS->>OSH: Pass props (index 0, total 3)
    OSH->>User: Display Revision 1/3, Enable "Next", Disable "Prev"

    User->>OSH: Clicks "Next" button
    OSH->>OS: Emit navigateRevision('next')
    OS->>IV: Emit navigateRevision('next')
    IV->>C: Call nextRevision()
    C->>C: Update currentRevisionIndex = 1
    C-->>IV: Update computed results for index 1
    IV->>OS: Pass updated props (computed results, index 1, total 3)
    OS->>OSH: Pass updated props (index 1, total 3)
    OSH->>User: Display Revision 2/3, Enable "Next", Enable "Prev"

    User->>OSH: Clicks "Prev" button
    OSH->>OS: Emit navigateRevision('prev')
    OS->>IV: Emit navigateRevision('prev')
    IV->>C: Call previousRevision()
    C->>C: Update currentRevisionIndex = 0
    C-->>IV: Update computed results for index 0
    IV->>OS: Pass updated props (computed results, index 0, total 3)
    OS->>OSH: Pass updated props (index 0, total 3)
    OSH->>User: Display Revision 1/3, Enable "Next", Disable "Prev"
```
## Settings Persistence (Plan v8 - Approved 2025-04-08)

To improve user experience, the application now persists certain user preferences across sessions using the browser's `localStorage`.

### Persisted Settings

- **"Use My Vocabulary" Toggle:**  
  The user's choice to enable or disable the "Use My Vocabulary" feature is saved.

- **Selected Writing Tone:**  
  The tone selected by the user for writing revisions is saved.

### Storage Keys

- `"pickvocab.write.useVocabulary"`  
- `"pickvocab.write.selectedTone"`

### Load on Component Initialization

- When the Write Assistant page (`index.vue`) loads, it attempts to read these settings from `localStorage`.
- **Validation:**  
  - For the selected tone, the application checks if the saved tone value exists in the current list of available tones.  
  - If the saved tone is invalid (e.g., the tone was removed or renamed), the application defaults to `'Professional'`.

### Save on Change

- Whenever the user changes the "Use My Vocabulary" toggle or selects a different tone, the new value is immediately saved back to `localStorage` under the respective key.

### Summary

This persistence ensures that users' preferred settings are retained across browser sessions, providing a smoother and more personalized experience.

## History Saving & Highlighting Strategy (Single Prompt Approach)

**Goal:** Simplify highlighting and history updates by using a single LLM call to highlight all revisions simultaneously, avoiding race conditions and the need for a custom backend endpoint.

**Core Idea:** Instead of N background LLM calls for N revisions, make 1 background LLM call that receives all revisions needing highlights and returns all highlighted texts in a structured format. Update the backend history in a single PATCH request after highlighting succeeds.

**Implementation Status:**
- [x] Phase 1: `highlightVocabularyPrompt.ts` updated to accept an array of `RevisionHighlightInput` and generate a prompt with explicit YAML structure instructions. Example output and property names (e.g., `highlightedText`) are aligned with the parser.
- [x] Phase 2: `useWriteRevisionHandler.ts` refactored to remove old highlighting logic, save initial history, trigger a single background LLM call for highlighting, parse the YAML response robustly, update local state atomically, and PATCH the history entry. TypeScript types (e.g., `HighlightedRevisionItem`) now match the YAML (`{ index: number; highlightedText: string }`).
- [x] Phase 3: Backend confirmed to require no changes; DRF `ModelViewSet` supports PATCH.
- [x] Phase 4: `writeHistory.ts` API client updated to include a PATCH-based `update` method.
- [x] All `console.log` and `console.info` statements removed from the relevant files.
- [x] All user feedback and bug reports (YAML structure, type mismatch) addressed.
- [x] Fixed `real_card_ids` preservation: Ensured IDs are added to `allRevisionsResult` state and updated `originalCardsMap` in `pollSimilarityResults` to allow lookup by both index and real ID during highlighting.
- [x] Refactored card caching mechanism: Renamed `originalCardsMap` to `cardCacheById` to better reflect its purpose. Improved the approach by:
  - Using only canonical card IDs as keys in the cache instead of both indices and IDs
  - Creating a temporary `indexToIdMap` during similarity search to translate LLM index references to actual card IDs
  - Passing the mapping through the revision generation pipeline
  - Separating the concerns of card storage and index-to-ID translation
  - Simplifying lookups throughout the codebase by using only real card IDs

**Current Prompt Example (as implemented):**
```yaml
highlighted_revisions:
  - index: 0
    highlightedText: "Highlighted text for revision 0..."
  - index: 2
    highlightedText: "Highlighted text for revision 2..."
```

**Notes:**
- The parser expects the property `highlightedText` (not `text`) in the YAML output.
- The implementation is now complete, type-correct, robust to LLM output variations, free of extraneous console output, and preserves all necessary metadata (`real_card_ids`, etc.).
- See commit history and code comments for further details on each phase.