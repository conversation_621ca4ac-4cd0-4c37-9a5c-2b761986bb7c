<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { Dialog, DialogHeader, DialogTitle, DialogTrigger, DialogScrollContent } from '~/components/ui/dialog'
import {
  Pagination,
  PaginationEllipsis,
  PaginationFirst,
  PaginationLast,
  PaginationList,
  PaginationListItem,
  PaginationNext,
  PaginationPrev,
} from '@/components/ui/pagination'
// @ts-ignore
import IconHistory from '@tabler/icons-vue/dist/esm/icons/IconHistory.mjs'
// @ts-ignore
import IconRefresh from '@tabler/icons-vue/dist/esm/icons/IconRefresh.mjs'
// @ts-ignore
import IconTrash from '@tabler/icons-vue/dist/esm/icons/IconTrash.mjs'

import { WriteHistoryApi, type WritingRevisionHistory } from '~/api/writeHistory';
import { useToneStore } from '~/stores/toneStore';
import { isSelectedToneIdentifier, parseToneIdentifier, resolveToneName as resolveNameFromUtil } from '~/utils/toneUtils';

const props = defineProps<{
  onRestore: (historyEntry: WritingRevisionHistory) => void
}>()

const emit = defineEmits(['restore'])

// Control dialog visibility
const open = ref(false)

// API client
const historyApi = new WriteHistoryApi();

// State
const historyItems = ref<WritingRevisionHistory[]>([]);
const isLoading = ref(false);
const loadError = ref<string | null>(null);
const currentPage = ref(1);
const totalPages = ref(1);
const ITEMS_PER_PAGE = 20; // Assuming backend uses 10 items per page

// Tone store for resolving tone names
const toneStore = useToneStore();

// Computed map for fast custom tone lookup
const customToneNameMap = computed(() => {
 const map = new Map<number, string>();
 for (const t of toneStore.customTones) {
   map.set(t.id, t.name);
 }
 return map;
});

// Clean resolver for tone name - using the utility function now
function resolveToneName(selectedTone: unknown): string {
  return resolveNameFromUtil(selectedTone, customToneNameMap.value);
}

// Get tone colors for display
function getToneColorClasses(selectedTone: unknown): { bgColor: string, textColor: string } {
  return toneStore.getToneColors(selectedTone);
}

// Computed property for total items required by shadcn pagination
const totalItems = computed(() => totalPages.value * ITEMS_PER_PAGE);

// Load history data
async function loadHistory(page = 1) {
  isLoading.value = true;
  loadError.value = null;
  
  try {
    const result = await historyApi.list({ page });
    historyItems.value = result.result;
    totalPages.value = result.totalPages;
    currentPage.value = page;
  } catch (error) {
    console.error('Error loading revision history:', error);
    loadError.value = 'Failed to load revision history';
  } finally {
    isLoading.value = false;
  }
}

// Delete a history entry
async function deleteHistoryItem(id: number, event: Event) {
  event.stopPropagation(); // Prevent triggering restore
  
  if (!confirm('Are you sure you want to delete this revision history entry?')) {
    return;
  }
  
  try {
    await historyApi.delete(id);
    // Refresh the list
    loadHistory(currentPage.value);
  } catch (error) {
    console.error('Error deleting revision history entry:', error);
    alert('Failed to delete revision history entry');
  }
}

// Restore a history entry
function restoreHistoryItem(historyEntry: WritingRevisionHistory) {
  props.onRestore(historyEntry);
  // Close the dialog after restoring
  open.value = false;
}

// Format date for display
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString();
}

// Truncate text to a maximum length
function truncateText(text: string, maxLength = 150): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

// Watch for dialog open state changes
watch(open, (newValue) => {
  if (newValue === true) {
    loadHistory();
  }
});

// Load history on component mount
onMounted(() => {
  loadHistory();
});
</script>

<template>
  <Dialog v-model:open="open">
    <DialogTrigger as-child>
      <button class="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 bg-white px-3 py-1.5 rounded-md border border-gray-200 shadow-sm transition-colors" @click="open = true">
        <IconHistory class="h-4 w-4 mr-1" />
        <span>History</span>
      </button>
    </DialogTrigger>
    <DialogScrollContent class="max-w-full sm:max-w-2xl h-[600px] overflow-y-hidden">
      <DialogHeader>
        <DialogTitle class="flex items-center">
          <IconHistory class="h-5 w-5 mr-2 text-blue-600" />
          Revision History
        </DialogTitle>
      </DialogHeader>
      
      <div class="h-[400px] flex justify-center items-center">
        <!-- Loading state -->
        <div v-if="isLoading" class="flex justify-center p-8">
          <div class="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
        </div>
        
        <!-- Error state -->
        <div v-else-if="loadError" class="p-4 bg-red-50 text-red-600 rounded-md">
          {{ loadError }}
          <button @click="loadHistory()" class="ml-2 text-blue-500 hover:underline">
            Try again
          </button>
        </div>
        
        <!-- Empty state -->
        <div v-else-if="historyItems.length === 0" class="p-8 text-center text-gray-500">
          No revision history available.
        </div>
        
        <!-- Results -->
        <div v-else class="space-y-4 h-[400px] overflow-y-auto w-full">
          <div 
            v-for="item in historyItems" 
            :key="item.id" 
            class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors w-full"
            @click="restoreHistoryItem(item)"
          >
            <div class="flex items-center mb-2">
              <IconHistory class="h-4 w-4 mr-2 text-gray-500" />
              <span class="text-sm text-gray-500">{{ formatDate(item.createdAt) }}</span>
            </div>
            
            <div class="flex gap-2 mb-2 flex-wrap">
              <span 
                v-if="item.selectedTone && item.triggerType !== 'GrammarCheck'" 
                :class="[getToneColorClasses(item.selectedTone).bgColor, getToneColorClasses(item.selectedTone).textColor, 'text-xs px-2 py-0.5 rounded']"
              >
                {{ resolveToneName(item.selectedTone) }}
              </span>
              <span v-if="item.triggerType === 'GrammarCheck'" class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">
                Grammar Check
              </span>
              <span v-if="item.useMyVocabulary" class="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded">
                Using Vocabulary
              </span>
            </div>
            
            <p class="text-gray-800 overflow-hidden break-words break-all">{{ truncateText(item.originalText) }}</p>
            
            <div class="flex justify-end mt-2">
              <button 
                class="text-gray-500 hover:text-red-600 p-1"
                @click="deleteHistoryItem(item.id, $event)"
                title="Delete"
              >
                <IconTrash class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Pagination -->
      <Pagination
        v-if="totalPages > 1"
        v-model:page="currentPage"
        :total="totalItems"
        :items-per-page="ITEMS_PER_PAGE"
        :sibling-count="1"
        @update:page="loadHistory"
        class="mt-4 flex justify-center"
      >
        <PaginationList v-slot="{ items }" class="flex items-center gap-1">
          <PaginationFirst class="h-8 w-8 p-0" />
          <PaginationPrev class="h-8 w-8 p-0" />

          <template v-for="(item, index) in items">
            <PaginationListItem v-if="item.type === 'page'" :key="index" :value="item.value" as-child>
              <Button class="w-8 h-8 p-0 text-sm" :variant="item.value === currentPage ? 'default' : 'outline'">
                {{ item.value }}
              </Button>
            </PaginationListItem>
            <PaginationEllipsis v-else :key="item.type" :index="index" />
          </template>

          <PaginationNext class="h-8 w-8 p-0" />
          <PaginationLast class="h-8 w-8 p-0" />
        </PaginationList>
      </Pagination>
      
      <!-- Refresh button -->
      <div class="flex justify-center mt-4">
        <button 
          @click="loadHistory()" 
          class="flex items-center text-blue-600 hover:text-blue-800"
        >
          <IconRefresh class="h-4 w-4 mr-1" />
          Refresh
        </button>
      </div>
    </DialogScrollContent>
  </Dialog>
</template>