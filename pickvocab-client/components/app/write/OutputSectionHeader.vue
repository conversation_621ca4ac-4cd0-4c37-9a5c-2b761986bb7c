<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
// @ts-ignore
import IconMessageCircle from '@tabler/icons-vue/dist/esm/icons/IconMessageCircle.mjs';
// @ts-ignore
import IconRefresh from '@tabler/icons-vue/dist/esm/icons/IconRefresh.mjs';
// @ts-ignore
import IconCopy from '@tabler/icons-vue/dist/esm/icons/IconCopy.mjs';

const props = defineProps<{
  // No props needed anymore
}>();

const emits = defineEmits([
  'refreshRevision',
  'copyRevisedText',
]);
</script>

<template>
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-xl font-semibold text-gray-800 flex items-center">
      <IconRefresh class="h-5 w-5 text-emerald-600 mr-2" />
      Revised Text
    </h2>
    <div class="flex space-x-2">
      <button 
        class="p-1.5 text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 bg-white border border-gray-200 shadow-sm transition-colors"
        @click="$emit('refreshRevision')"
        aria-label="Refresh Revision"
      >
        <IconRefresh class="h-5 w-5" />
      </button>
      <button 
        class="p-1.5 text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 bg-white border border-gray-200 shadow-sm transition-colors"
        @click="$emit('copyRevisedText')"
        aria-label="Copy Revised Text"
      >
        <IconCopy class="h-5 w-5" />
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Component-specific styles if needed */
</style>