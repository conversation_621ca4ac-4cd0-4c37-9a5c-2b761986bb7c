<script setup lang="ts">
import { ref } from 'vue';
import { <PERSON>alog, DialogHeader, DialogTitle, DialogDescription, DialogContent } from '~/components/ui/dialog';
// @ts-ignore
import IconTrash from '@tabler/icons-vue/dist/esm/icons/IconTrash.mjs';
// @ts-ignore
import IconPlus from '@tabler/icons-vue/dist/esm/icons/IconPlus.mjs';
// @ts-ignore
import IconLoader2 from '@tabler/icons-vue/dist/esm/icons/IconLoader2.mjs'; // For loading state
import { useToneStore } from '~/stores/toneStore'; // Import store

const toneStore = useToneStore();

// Props: only need 'show' to control visibility from parent
const props = defineProps<{
  show: boolean
}>();

// Emits: only need 'close'
const emit = defineEmits<{
  (e: 'close'): void
}>();

const showAddToneForm = ref(false);
const newToneName = ref('');
const newToneDescription = ref('');

const addError = ref<string | null>(null); // Local error state for add form

async function handleAddTone() {
  if (!newToneName.value.trim()) return;
  addError.value = null; // Clear previous add error
  try {
    await toneStore.addTone({
      name: newToneName.value.trim(),
      description: newToneDescription.value.trim()
    });
    // Clear form and hide on success
    newToneName.value = '';
    newToneDescription.value = '';
    showAddToneForm.value = false;
  } catch (err: any) {
    console.error("Failed to add tone:", err);
    // Use the error message from the store if available, otherwise provide a generic one
    addError.value = toneStore.error || 'Failed to add tone. Please try again.';
  }
}

async function handleRemoveTone(id: number) {
  // Optional: Add confirmation dialog here
  if (!confirm(`Are you sure you want to delete this tone?`)) {
     return;
  }
  try {
    await toneStore.removeTone(id);
  } catch (err) {
    console.error("Failed to remove tone:", err);
    // Error display can rely on the global toneStore.error shown elsewhere, or add local error state
  }
}

function emitClose(value: boolean) {
  if (!value) emit('close');
}
</script>

<template>
  <Dialog :open="show" @update:open="emitClose">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Manage custom tones</DialogTitle>
        <DialogDescription>
          Add new custom tones or remove existing ones.
        </DialogDescription>
      </DialogHeader>

      <!-- List existing custom tones -->
      <div class="py-4 space-y-3 relative">
        <!-- Loading Overlay -->
        <div v-if="toneStore.isLoading" class="absolute inset-0 bg-white/70 flex items-center justify-center z-10 rounded-md">
           <IconLoader2 class="h-6 w-6 animate-spin text-blue-600" />
        </div>

        <!-- Global Error Display -->
        <div v-if="toneStore.error && !addError" class="text-sm text-red-600 bg-red-50 p-2 rounded-md border border-red-200">
          Error: {{ toneStore.error }}
        </div>

        <h3 class="text-base font-semibold text-gray-800 mb-2">Existing custom tones</h3>
        <div v-if="!toneStore.isLoading && toneStore.customTones.length === 0" class="text-sm text-gray-500 italic">No custom tones added yet.</div>
        <ul v-else class="space-y-3 max-h-[300px] overflow-y-auto rounded-md">
          <!-- Iterate over tones from the store -->
          <li v-for="tone in toneStore.customTones" :key="tone.id" class="flex items-center justify-between text-sm p-2 bg-white border border-gray-200 rounded-md shadow-sm">
            <div class="flex-grow mr-2">
              <span class="font-medium text-gray-800 block">{{ tone.name }}</span>
              <span class="text-xs text-gray-500 block mt-0.5">{{ tone.description || 'No description' }}</span>
            </div>
            <!-- Call handleRemoveTone with tone.id -->
            <button
              type="button"
              @click="handleRemoveTone(tone.id)"
              :disabled="toneStore.isLoading"
              class="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-100 rounded-full flex-shrink-0 ml-2 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Remove tone"
            >
              <IconTrash class="h-4 w-4" />
            </button>
          </li>
        </ul>

        <!-- Add New Tone Section -->
        <div class="pt-2">
          <!-- Button to show the form -->
          <button 
            v-if="!showAddToneForm"
            @click="showAddToneForm = true"
            :disabled="toneStore.isLoading"
            class="w-full flex items-center justify-center space-x-1 py-1.5 px-2 text-sm rounded-md hover:bg-gray-100 transition-colors border border-dashed border-gray-300 text-gray-600 hover:text-gray-800 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <IconPlus class="h-4 w-4 mr-1" />
            <span>Add new tone</span>
          </button>
          
          <!-- Form to add new tone -->
          <div v-if="showAddToneForm" class="space-y-3 border border-gray-200 p-3 rounded-md bg-gray-50/50">
            <h3 class="text-base font-semibold text-gray-800">Add new tone details</h3>
            <!-- Add Form Error Display -->
            <div v-if="addError" class="text-sm text-red-600 bg-red-50 p-2 rounded-md border border-red-200 mt-2">
              {{ addError }}
            </div>
            <div class="space-y-2">
              <label for="toneName" class="text-sm font-medium text-gray-700">Name</label>
              <input 
                id="toneName"
                v-model="newToneName"
                type="text" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Friendly, Concise"
              />
            </div>
            
            <div class="space-y-2">
              <label for="toneDescription" class="text-sm font-medium text-gray-700">Description (optional)</label>
              <textarea 
                id="toneDescription"
                v-model="newToneDescription"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[80px]"
                placeholder="Describe this tone"
              ></textarea>
            </div>

            <button 
              class="flex items-center justify-center ml-auto px-4 py-2 text-sm bg-blue-700 text-white rounded-md hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
              @click="handleAddTone"
              :disabled="!newToneName.trim() || toneStore.isLoading"
            >
               <IconLoader2 v-if="toneStore.isLoading" class="h-4 w-4 mr-2 animate-spin" />
              <span>Add</span>
            </button>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<style scoped>
</style>