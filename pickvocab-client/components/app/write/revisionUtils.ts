import { CardType, type Card } from '~/utils/card';

// Define the expected structure for the formatted vocabulary item for the prompt
export interface FormattedVocabulary {
  word: string;
  id: string;
}

/**
 * Formats a card for use in prompts
 * @param card The card to format
 * @param idx The index to use as ID
 * @returns A formatted vocabulary object
 */
export function formatCardForPrompt(card: Card, idx: number): FormattedVocabulary {
  if (card.cardType === CardType.DefinitionCard) {
    const definitionText = card.definition?.definition ?? '';
    return {
      word: `word: ${card.word}\ndefinition: ${definitionText}`,
      id: idx.toString(),
    };
  } else if (card.cardType === CardType.ContextCard) {
    const wordInContext = card.wordInContext;
    if (!wordInContext) {
      console.warn(`Card ID ${card.id || idx} is a ContextCard but missing wordInContext`);
      return { word: `word: Unknown Word\ndefinition: Missing context`, id: idx.toString() };
    }
    const definition = wordInContext.definition?.definition || wordInContext.definitionShort?.explanation || '';
    return {
      word: `word: ${wordInContext.word}\ndefinition: ${definition}`,
      id: idx.toString()
    };
  }
  console.error(`Unhandled card type for card at index ${idx}. Card data:`, card);
  return { word: 'word: Error\ndefinition: Unhandled card type', id: idx.toString() };
}

/**
 * Helper to extract content from YAML code blocks
 * @param codeBlock The code block to extract from
 * @returns The extracted content
 */
export function extractFromCodeBlock(codeBlock: string): string {
  // Match YAML code blocks specifically, or fall back to generic code blocks
  const yamlMatch = codeBlock.match(/```yaml\n([\s\S]*?)\n```/);
  if (yamlMatch?.[1]) {
    return yamlMatch[1];
  }
  const genericMatch = codeBlock.match(/```(?:\w*\n)?([\s\S]*?)\n```/);
  return genericMatch?.[1] ?? codeBlock; // Return original if no block found
} 