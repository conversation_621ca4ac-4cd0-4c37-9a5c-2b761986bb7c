# Design: Custom Writing Tones (Revised v2)

This document outlines the database schema and API endpoint design for supporting user-specific custom writing tones in the Pickvocab application, revised based on existing model conventions.

## 1. Database Schema (Django Model - Final Revision)

A new Django model, `CustomTone`, will be created, likely within a `writing` app or similar in the `pickvocab-server` project. It follows conventions like using the default primary key and naming the user foreign key `owner` using `get_user_model()`.

*(Requires `from django.contrib.auth import get_user_model` at the top of `models.py`)*

**Model:** `CustomTone`

| Field Name   | Django Field Type        | Description                                      | Notes                                                                 |
| :----------- | :----------------------- | :----------------------------------------------- | :-------------------------------------------------------------------- |
| `id`         | *(Default AutoField)*    | Default Django auto-incrementing primary key.    | *(Handled by Django)*                                                 |
| `owner`      | `models.ForeignKey`      | Link to the user who owns this tone.             | `get_user_model()`, `on_delete=models.CASCADE`, `related_name='custom_tones'` |
| `name`       | `models.CharField`       | The user-defined name for the tone.              | `max_length=100`                                                      |
| `description`| `models.TextField`       | A user-defined description or prompt for the tone. | `blank=True` (Allow empty description)                               |
| `created_at` | `models.DateTimeField`   | Timestamp when the tone was created.             | `auto_now_add=True`                                                   |
| `updated_at` | `models.DateTimeField`   | Timestamp when the tone was last updated.        | `auto_now=True`                                                       |

**Constraints (Revised):**

*   A `UniqueConstraint` will ensure that an `owner` cannot have multiple `CustomTone` entries with the same `name`.
    ```python
    # In models.py Meta class
    constraints = [
        models.UniqueConstraint(fields=['owner', 'name'], name='unique_tone_name_per_owner')
    ]
    ```

## 2. API Endpoints (Django REST Framework - Revised Notes)

These endpoints assume standard DRF setup with Token or Session authentication.

**Base URL:** `/api/custom-tones/`

**Permissions (Revised):**
*   `IsAuthenticated`: All endpoints require the user to be logged in.
*   `IsOwner`: For detail, update, and delete endpoints (`/{tone_id}/`), a custom permission class is needed to verify that `request.user` matches the `owner` field of the `CustomTone` object being accessed.

**Endpoints:**

| Method | URL Path                  | Description                     | Request Body (JSON)                                  | Success Response (2xx)                                       | Error Responses (4xx)                                     |
| :----- | :------------------------ | :------------------------------ | :--------------------------------------------------- | :----------------------------------------------------------- | :-------------------------------------------------------- |
| `POST` | `/`                       | Create a new custom tone.       | `{ "name": "string", "description": "string" }`      | `201 Created`: `{ "id": integer, "name": "string", ... }`    | `400 Bad Request` (Validation errors), `401 Unauthorized` |
| `GET`  | `/`                       | List user's custom tones.       | *(None)*                                             | `200 OK`: `[ { "id": integer, "name": "string", ... }, ... ]`| `401 Unauthorized`                                        |
| `GET`  | `/{tone_id}/`             | Retrieve a specific tone.       | *(None)*                                             | `200 OK`: `{ "id": integer, "name": "string", ... }`          | `401 Unauthorized`, `403 Forbidden`, `404 Not Found`      |
| `PUT`  | `/{tone_id}/`             | Update a specific tone.         | `{ "name": "string", "description": "string" }`      | `200 OK`: `{ "id": integer, "name": "string", ... }`          | `400 Bad Request`, `401 Unauthorized`, `403 Forbidden`, `404 Not Found` |
| `PATCH`| `/{tone_id}/`             | Partially update a specific tone.| `{ "name": "string" }` or `{ "description": "string" }` | `200 OK`: `{ "id": integer, "name": "string", ... }`          | `400 Bad Request`, `401 Unauthorized`, `403 Forbidden`, `404 Not Found` |
| `DELETE`| `/{tone_id}/`             | Delete a specific tone.         | *(None)*                                             | `204 No Content`                                             | `401 Unauthorized`, `403 Forbidden`, `404 Not Found`      |

*(Note: Full response objects include the default integer `id`, `owner` (usually represented by user ID), `name`, `description`, `created_at`, `updated_at`)*

## 3. Authentication/Authorization (Revised Notes)

*   **Authentication:** Standard Django REST Framework authentication identifies `request.user`.
*   **Authorization:**
    *   The list (`GET /`) and create (`POST /`) endpoints implicitly filter/set the `owner` field based on `request.user`.
    *   For detail (`GET /{tone_id}/`), update (`PUT`/`PATCH /{tone_id}/`), and delete (`DELETE /{tone_id}/`) endpoints, a custom DRF permission class (e.g., `IsOwner`) is required to check if the `CustomTone` object's `owner` field matches `request.user`.

## 4. Identification Strategy (Revised Notes)

*   **Backend:** Custom tones are uniquely identified by their default integer `id`.
*   **Frontend:**
    1.  Fetch predefined tones (current mechanism).
    2.  Fetch user's custom tones via `GET /api/custom-tones/` (receiving objects with integer `id`).
    3.  Combine lists for UI display, internally tracking `type` (`predefined` or `custom`) and `identifier` (`name` or integer `id`).
    4.  When revising, use the selected tone's `name` and `description` to build the LLM prompt, regardless of type.
    5.  For persisting the selected tone preference (e.g., in `localStorage`), store both type and identifier (e.g., `{ type: 'custom', id: 123 }` or `{ type: 'predefined', name: 'Professional' }`) to allow correct re-selection on load.

## 5. Implementation Status

✅ **Backend Implementation Complete** (2025-04-12)
- Model created in `pickvocab-server/app/models.py`
- Serializer created in `pickvocab-server/app/serializers.py`
- ViewSet created in `pickvocab-server/app/views.py`
- URL routing configured in `pickvocab-server/app/urls.py`
- Admin interface configured in `pickvocab-server/app/admin.py`

Next steps:
- Implement frontend components to interact with the API

## 6. Frontend Implementation Plan (Updated after implementation)

This section outlines the plan and actual implementation for the frontend components and logic to support custom writing tones.

**1. API Layer (`pickvocab-client/api/write/customTones.ts`)**

- Created a type-safe API class for custom tones, supporting list, create, update (PATCH), and delete operations.
- All API calls use the shared axios instance and map backend snake_case to frontend camelCase.

**2. State Management (Pinia Store - `pickvocab-client/stores/toneStore.ts`)**

- Implemented a Pinia store (`useToneStore`) to manage:
    - `predefinedTones`: Hardcoded list of default tones.
    - `customTones`: Fetched from the backend.
    - `selectedTone`: Now an object `{ type: 'predefined' | 'custom', identifier: string | number } | null`.
    - Loading and error state.
- Store actions handle all CRUD operations and selection logic.
- Getters provide a combined list for UI and details for the selected tone.

**3. Component Modifications**

- **`ToneSelector.vue`**: Uses the store for all tone data and selection. Dropdown is populated from the store, and selection is an object, not a string.
- **`ManageCustomTonesDialog.vue`**: Uses the store for CRUD, loading, and error state. No longer receives tones as props.
- **`useWriteRevisionHandler.ts`**: Uses the store for tone selection and details. Handles serialization of the selected tone object for history (JSON.stringify) and deserialization when restoring.
- **`InputSection.vue`**: No longer receives legacy tone props. The `selectedTone` v-model is now the tone object, not a string.
- **Parent Page (`pages/app/write/index.vue`)**:
    - Loads tones from the store on mount.
    - Handles dialog visibility.
    - Passes the correct v-models and events to child components.
    - Persists `selectedTone` in localStorage as a JSON string, and restores it on mount.

**4. Data Flow and Integration**

- All tone selection and CRUD operations are now centralized in the store.
- The UI is fully reactive to changes in the store.
- The selected tone is always an object, and is correctly serialized for backend compatibility.

**5. Compatibility and Migration**

- The system handles legacy string values for `selectedTone` in history and localStorage, converting them to the new object format as needed.

**Diagram (Mermaid Sequence):**

```mermaid
sequenceDiagram
    participant Page as WritePage (index.vue)
    participant Store as ToneStore (Pinia)
    participant API as CustomTonesAPI
    participant Selector as ToneSelector.vue
    participant Dialog as ManageTonesDialog.vue
    participant Handler as useWriteRevisionHandler

    Page->>Store: loadPredefinedTones()
    Page->>Store: fetchCustomTones()
    Store->>API: GET /api/custom-tones/
    API-->>Store: CustomTone[]
    Store-->>Page: Tones loaded

    Page->>Selector: Render (injects Store)
    Selector->>Store: Read allAvailableTones
    Selector->>Store: Read selectedTone
    Selector-->>Page: Display dropdown

    User->>Selector: Selects a tone (e.g., Custom Tone ID 5)
    Selector->>Store: selectTone({ type: 'custom', identifier: 5 })

    User->>Page: Enters text, clicks Revise
    Page->>Handler: initiateRevision() (injects Store)
    Handler->>Store: Read selectedTone ({ type: 'custom', identifier: 5 })
    Handler->>Store: Read customTones (to get details for ID 5)
    Handler->>Handler: Build prompt using tone name & description
    Handler->>API: Call LLM (via llmStore/chatSource)
    API-->>Handler: Revision results
    Handler->>Store: saveRevisionHistory(selectedTone=JSON.stringify(...))
    Handler-->>Page: Display revision

    User->>Selector: Clicks "Manage custom tones"
    Selector-->>Page: Emit openManageTones
    Page->>Dialog: Show Dialog (injects Store)

    Dialog->>Store: Read customTones
    Dialog-->>Page: Display list

    User->>Dialog: Adds new tone ("Casual", "Informal style")
    Dialog->>Store: addTone({ name: "Casual", ... })
    Store->>API: POST /api/custom-tones/
    API-->>Store: New CustomTone object
    Store->>Store: Update customTones array
    Store-->>Dialog: List updates
    Store-->>Selector: Dropdown updates (via getter)

    User->>Dialog: Deletes tone (ID 7)
    Dialog->>Store: removeTone(7)
    Store->>API: DELETE /api/custom-tones/7
    API-->>Store: Success (204)
    Store->>Store: Update customTones array
    Store-->>Dialog: List updates
    Store-->>Selector: Dropdown updates
```

**Status:**
All planned steps have been implemented. The frontend now fully supports custom writing tones with a robust, type-safe, and reactive architecture.