import { ref } from 'vue';
import YAML from 'yaml';
import { RemoteGenericCardsApi, type SimilaritySearchResult } from '~/api/genericCard';
import { WriteHistoryApi, type WritingRevisionHistory } from '~/api/writeHistory';
import { useLLMStore } from '~/stores/llm';
import type { LLMModel } from '~/utils/llm';
import type { Card } from '~/utils/card';
import { extractFromCodeBlock } from './revisionUtils';
import type { RevisionHighlightInput } from './highlightVocabularyPrompt';

// Define the structure for a single revision from the LLM response
export interface RevisionData {
  revision: string;
  user_vocabularies_used?: string[]; // Index references for LLM-generated content
  real_card_ids?: string[]; // Actual card IDs for restored content
  feedback: string;
  learning_focus?: string[]; // Optional as per YAML structure
}

// Define the structure for a single highlighted revision item from the LLM response
export interface HighlightedRevisionItem {
  index: number; // Corresponds to the originalIndex in RevisionHighlightInput
  highlightedText: string;  // The revision text potentially with highlighting markup
}

// Polling configuration
const INITIAL_DELAY_MS = 50;
const MAX_DELAY_MS = 5000;
const POLLING_TIMEOUT_MS = 30000;

// Input for history creation
export interface HistoryEntryData {
  triggerType: 'Revise' | 'Refresh' | 'GrammarCheck';
  originalText: string;
  useMyVocabulary: boolean;
  selectedTone: string;
  revisions: RevisionData[];
}

/**
 * Composable that abstracts all API interactions for the revision process
 */
export function useRevisionApi() {
  const genericCardApiInstance = new RemoteGenericCardsApi();
  const writeHistoryApi = new WriteHistoryApi();
  const llmStore = useLLMStore();

  /**
   * Starts a similarity search operation
   * @param text The text to search
   * @param options Options like limit and threshold
   * @returns The search task data
   */
  async function startSimilaritySearch(text: string, options?: { limit?: number, threshold?: number }) {
    return await genericCardApiInstance.startSimilaritySearch(text, options);
  }

  /**
   * Poll for similarity search results with exponential backoff
   * @param taskId The task ID from startSimilaritySearch
   * @returns Promise resolving to the cards found
   */
  async function pollSimilarityResults(taskId: string): Promise<Card[]> {
    return new Promise((resolve, reject) => {
      let overallTimeoutId: NodeJS.Timeout | null = null;
      let nextPollTimeoutId: NodeJS.Timeout | null = null;
      let currentDelay = INITIAL_DELAY_MS;

      const cleanup = () => {
        if (overallTimeoutId) clearTimeout(overallTimeoutId);
        if (nextPollTimeoutId) clearTimeout(nextPollTimeoutId);
      };

      const poll = async () => {
        try {
          const searchResults = await genericCardApiInstance.getSimilaritySearchResults(taskId);

          if (searchResults.status === 'SUCCESS') {
            cleanup();
            const fetchedCards: Card[] = searchResults.results?.map(r => r.card) || [];
            resolve(fetchedCards);
          } else if (searchResults.status === 'FAILURE') {
            cleanup();
            reject(new Error(searchResults.error || searchResults.message || 'Similarity search failed'));
          } else if (searchResults.status === 'PENDING' || searchResults.status === 'STARTED') {
            const nextDelay = Math.min(currentDelay * 1.2, MAX_DELAY_MS);
            nextPollTimeoutId = setTimeout(poll, nextDelay);
            currentDelay = nextDelay;
          } else {
            cleanup();
            reject(new Error(`Unknown similarity search status: ${searchResults.status}`));
          }
        } catch (error) {
          cleanup();
          reject(error);
        }
      };

      overallTimeoutId = setTimeout(() => {
        cleanup();
        reject(new Error('Similarity search polling timed out'));
      }, POLLING_TIMEOUT_MS);

      nextPollTimeoutId = setTimeout(poll, currentDelay);
    });
  }

  /**
   * Generate a revision using the LLM
   * @param prompt The prompt to send to the LLM
   * @returns Array of revision data
   */
  async function generateRevision(prompt: string): Promise<RevisionData[]> {
    try {
      // Determine the model configuration to use
      let modelConfigToUse: LLMModel | undefined | null = llmStore.activeUserModel;

      if (!modelConfigToUse) {
        const defaultModelId = llmStore.pickvocabChatSource?.modelId;
        if (typeof defaultModelId === 'number') {
          modelConfigToUse = llmStore.getModelById(defaultModelId);
        }
      }

      if (!modelConfigToUse) {
        throw new Error('No language model configuration available for revision');
      }

      // Create a chat source
      const chatSource = llmStore.createChatSource(modelConfigToUse);
      if (!chatSource) {
        throw new Error(`Failed to create ChatSource for model: ${modelConfigToUse.name}`);
      }

      // Send the message and wait for response
      const result = await chatSource.sendMessage(prompt);

      // Extract and parse YAML from the response
      const yamlContent = extractFromCodeBlock(result.message);
      const parsedResponse = YAML.parse(yamlContent);

      if (!Array.isArray(parsedResponse?.revisions)) {
        throw new Error('LLM response does not contain a valid revisions array');
      }

      // Validate and normalize the received revisions
      return parsedResponse.revisions
        .filter((rev: any): rev is RevisionData =>
          typeof rev?.revision === 'string' && typeof rev?.feedback === 'string'
        )
        .map((rev: any) => ({
          ...rev,
          learning_focus: rev.learning_focus || [],
          user_vocabularies_used: rev.user_vocabularies_used || []
        }));
    } catch (error) {
      console.error('Error during LLM interaction:', error);
      throw error;
    }
  }

  /**
   * Generate highlights for revisions containing vocabulary
   * @param prompt The formatted prompt to send to the LLM
   * @returns Array of highlighted revision items
   */
  async function generateHighlights(prompt: string): Promise<HighlightedRevisionItem[]> {
    try {
      // Use the same model determination logic as in generateRevision
      let modelConfigToUse: LLMModel | undefined | null = llmStore.activeUserModel;
      if (!modelConfigToUse) {
        const defaultModelId = llmStore.pickvocabChatSource?.modelId;
        if (typeof defaultModelId === 'number') {
          modelConfigToUse = llmStore.getModelById(defaultModelId);
        }
      }
      if (!modelConfigToUse) {
        throw new Error('No language model configuration available for highlighting');
      }

      const chatSource = llmStore.createChatSource(modelConfigToUse);
      if (!chatSource) {
        throw new Error(`Failed to create ChatSource for highlighting model: ${modelConfigToUse.name}`);
      }

      // Send the message and wait for response
      const result = await chatSource.sendMessage(prompt);
      const yamlContent = extractFromCodeBlock(result.message);
      const parsedResponse = YAML.parse(yamlContent);

      if (!parsedResponse || !Array.isArray(parsedResponse?.highlighted_revisions)) {
        throw new Error('Invalid YAML structure in highlighting response. Missing highlighted_revisions array');
      }

      return parsedResponse.highlighted_revisions as HighlightedRevisionItem[];
    } catch (error) {
      console.error('Error during highlighting LLM call or parsing:', error);
      throw error;
    }
  }

  /**
   * Save a revision history entry
   * @param entry The history entry data
   * @param cardIds Array of card IDs used in the revisions
   * @returns The created history entry
   */
  async function saveHistory(entry: HistoryEntryData, cardIds: string[]): Promise<WritingRevisionHistory> {
    try {
      return await writeHistoryApi.create(entry, cardIds);
    } catch (error) {
      console.error('Error saving revision history:', error);
      throw error;
    }
  }

  /**
   * Update an existing history entry
   * @param entryId The ID of the entry to update
   * @param updates Partial updates to apply
   * @returns The updated history entry
   */
  async function updateHistory(entryId: number, updates: Partial<HistoryEntryData>): Promise<WritingRevisionHistory> {
    try {
      return await writeHistoryApi.update(entryId, updates);
    } catch (error) {
      console.error(`Error updating revision history for ID ${entryId}:`, error);
      throw error;
    }
  }

  /**
   * Fetch cards by their IDs
   * @param ids Array of card IDs to fetch
   * @returns Array of cards
   */
  async function fetchCardsByIds(ids: string[]): Promise<Card[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    try {
      const result = await genericCardApiInstance.list({
        ids: ids.join(',')
      });
      return result.result || [];
    } catch (error) {
      console.error('Error fetching cards by IDs:', error);
      throw error;
    }
  }

  return {
    startSimilaritySearch,
    pollSimilarityResults,
    generateRevision,
    generateHighlights,
    saveHistory,
    updateHistory,
    fetchCardsByIds
  };
}