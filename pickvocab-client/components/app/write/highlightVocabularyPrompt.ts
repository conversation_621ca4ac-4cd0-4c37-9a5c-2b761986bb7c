export interface RevisionHighlightInput {
  originalIndex: number; // Keep track of the original index
  revisionText: string;
  vocabularyList: string[];
}

/**
 * Creates a prompt for the LLM to highlight specific vocabulary words in multiple text revisions
 * using Markdown bolding, returning the results in a structured YAML format.
 *
 * @param revisionsToHighlight An array of objects, each containing the original index,
 *                             the text to highlight, and its corresponding vocabulary list.
 * @returns The prompt string.
 */
export function highlightVocabularyPrompt(revisionsToHighlight: RevisionHighlightInput[]): string {
  if (!revisionsToHighlight || revisionsToHighlight.length === 0) {
    console.warn('highlightVocabularyPrompt called with empty or invalid revisionsToHighlight array.');
    return ''; // Or handle as appropriate
  }

  // Construct the input data block for the prompt
  const inputDataBlock = revisionsToHighlight.map(revision => `
  - index: ${revision.originalIndex}
    text: |
      ${revision.revisionText.replace(/\n/g, '\n      ')}
    vocabulary:
${revision.vocabularyList.map(word => `      - ${word}`).join('\n')}
  `).join('');
  return `
You will receive a list of text revisions, each associated with an original index and a list of vocabulary words/phrases used within that specific revision.

Your task is to process **each revision independently**. For each revision, apply Markdown bold tags (\`**word**\`) to all occurrences of the vocabulary words/phrases provided *for that specific revision*. Follow the crucial matching guidelines below.

**Input Data:**
The input is provided as a YAML list, where each item represents a revision to be processed:

\`\`\`yaml
${inputDataBlock}
\`\`\`

**Crucial Matching Guidelines (Apply to each revision independently):**
- Your primary goal is to identify phrases in the text that *semantically match* the vocabulary items for that revision, even if they aren't character-for-character identical.
- **Handle Variations:** Bold phrases in the text that are variations of the vocabulary items (e.g., tense, pluralization, minor word changes).
- **Case Insensitivity:** Match vocabulary items regardless of case, but preserve the original casing of the text within the bold tags.
- **Prioritize Meaning:** Prioritize semantic meaning when deciding whether to bold a phrase.
- **Exact Matches:** Still bold exact matches.
- **No Rewriting:** Only add bold tags (\`** **\`). Do *not* modify the text in any other way.
- **Multiple Occurrences:** Bold *all* matching occurrences within the specific revision.

**Output Format:**
Return your response as a **single YAML code block**. The root of the YAML structure **must be an object** with a single key named \`highlighted_revisions\`. The value of this key **must be a list** where each item corresponds to an input revision and includes the original \`index\` and the resulting \`highlightedText\` with the vocabulary bolded according to the rules.

**Crucially, ensure the output is ONLY the YAML code block, starting with \`\`\`yaml and ending with \`\`\`. Do not include any introductory text or explanations outside the code block.**

**Overall Input/Output Example (Demonstrating Structure and Guideline Application):**

*Example Input:*
\`\`\`yaml
- index: 0
  text: |
    The quick brown fox jumps over the lazy dog. He saw two big dogs later.
  vocabulary:
    - quick brown fox
    - lazy dog
    - dog
- index: 1
  text: |
    You get the hang of it, congrats! Learning takes time.
  vocabulary:
    - getting the hang of
    - learn
- index: 2
  text: |
    Let's deliver a temporary patch to fix the issue at hand, buying us time to develop a more leading, permanent solution over the long haul. This isn't a janky duct tape fix; it's a strategic move to ensure we're not just imposing a quick solution on someone but actually solving the problem.
  vocabulary:
    - delivered
    - leading
    - over the long haul
    - imposing something on someone
    - janky duct tape
\`\`\`

*Example Output:*
\`\`\`yaml
highlighted_revisions:
  - index: 0
    highlightedText: |
      The **quick brown fox** jumps over the **lazy dog**. He saw two big **dogs** later.
  - index: 1
    highlightedText: |
      You **get the hang of it**, congrats! **Learning** takes time.
  - index: 2
    highlightedText: |
      Let's **deliver** a temporary patch to fix the issue at hand, buying us time to develop a more **leading**, permanent solution **over the long haul**. This isn't a **janky duct tape** fix; it's a strategic move to ensure we're not just **imposing** a quick solution **on someone** but actually solving the problem.
\`\`\`

Now, process the provided input data based *strictly* on these rules and return the result in the specified YAML format.
  `;
} 