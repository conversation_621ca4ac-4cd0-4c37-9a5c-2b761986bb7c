# Plan: Advertise Writing Assistant on Landing Page

**Goal:** Effectively advertise the new Writing Assistant feature as a major differentiator by creating a dedicated section on the landing page and a separate, detailed feature page.

**Strategy:**

1.  **Landing Page Summary Section:** Create a concise, visually appealing section on the main landing page (`pickvocab-client/pages/index.vue`) to introduce the Writing Assistant and pique user interest.
2.  **Dedicated Feature Page:** Create a new, comprehensive page (e.g., `pickvocab-client/pages/writing-assistant.vue`) detailing the feature's capabilities, benefits, and use cases, optimized for SEO.
3.  **Linking:** Connect the landing page section to the dedicated feature page via a "Learn More" (or similar) CTA.
4.  **Refine Existing Content:** Update the existing "Personal Language Assistant" section on the landing page to be consistent with the launched feature.

**Implementation Steps:**

1.  **Gather Assets & Content:** ✅
    *   Obtain final text copy (headlines, benefit points, detailed descriptions) for both the landing page section and the dedicated feature page from the user.
    *   **Placeholder Visuals:** Initially, placeholder elements (e.g., simple styled divs or images) will be used where visual assets are required. The user will provide final assets later.
    *   Determine the desired path for the new dedicated page (e.g., `/writing-assistant`, `/features/writing-assistant`).
    *   Determine the exact placement for the new section within `pages/index.vue`.

2.  **Create Dedicated Feature Page File:**
    *   Create the new Vue component file at the chosen path (e.g., `pickvocab-client/pages/writing-assistant.vue`).
    *   Add basic structure (template, script setup).
    *   Populate the page with the detailed content and **placeholder visuals** based on Step 1.
    *   Include appropriate SEO metadata (`useSeoMeta`).
    *   Add clear CTAs (e.g., "Try it Now" linking to `/app/write`).
    *   Ensure the page uses the standard site layout/styling.

3.  **Add Section to Landing Page (`pages/index.vue`):** ✅
    *   Locate the desired insertion point within the template.
    *   Add a new `<div class="...">` section.
    *   Implement the section using Tailwind CSS, incorporating:
        *   The main headline.
        *   The short list of key benefits.
        *   A **placeholder** for the primary visual asset.
        *   A CTA button linking to the new dedicated feature page (from Step 2).
    *   Ensure responsiveness and consistency with the existing landing page design.

4.  **Update Existing "Personal Language Assistant" Section (`pages/index.vue`):** ✅
    *   Find the list item: "Revise your writing using words from your notebooks &nbsp;<span class="text-blue-600 dark:text-blue-500">(coming soon)</span>"
    *   Remove the `&nbsp;<span ...>(coming soon)</span>` part.
    *   Review the wording of other bullet points in this section to ensure they complement the new dedicated section and avoid redundancy. Make minor adjustments if necessary.

**Copywriting and UI Skeleton:**

## 1. Landing Page Section Content ✅

### Position
Place after the "Spaced repetition learning" section and before the "Personal language assistant" section.

### Copy Elements
- **Headline:** "Writing Assistant with your vocabulary"
  - "Writing Assistant" emphasized in blue
- **Subheading:** "Improve your writing fluency while putting your vocabulary into practice"
- **Feature Points:**
  1. "Fix grammar and enhance wording for more natural expression"
  2. "Improve overall writing clarity and fluency"
  3. "Incorporate words from your notebooks into your writing"
  4. "Track your progress with revision history"
- **CTA Button:** "Learn more about the Writing Assistant →" (links to dedicated page)

### UI Structure
- Grid layout with two columns (single column on mobile)
- Left column: Feature points with checkmark icons, CTA button
- Right column: Image placeholder (screenshot of the writing assistant)
- Consistent styling with other landing page sections (colors, spacing, typography)

## 2. Dedicated Feature Page Content

### SEO Elements
- **Title:** "Elevate Your Writing with Pickvocab's AI Assistant"
- **Description:** "Improve writing fluency and use your vocabulary in real writing with our AI-powered assistant - perfect for language learners."
- **OG Title:** "Pickvocab Writing Assistant - Enhanced Writing with Your Vocabulary"
- **OG Description:** "Improve writing clarity, fix grammar issues, and put your vocabulary knowledge into active use."

### Page Structure
1. **Hero Section**
   - **Headline:** "Writing Assistant"
   - **Subheading:** "Polish your writing while actively using the vocabulary you've learned."
   - **CTA Buttons:** "Try it now" and "Explore Pickvocab"
   - Background color: Light blue (consistent with brand)

2. **Feature Section 1: Writing Enhancement**
   - **Headline:** "Improve your writing fluency and clarity"
   - **Description:** "Struggling with awkward phrasing or grammar issues? Our Writing Assistant helps you express yourself more naturally and professionally in English."
   - **Feature Points:**
     1. "Correct grammar mistakes and improve sentence structure"
     2. "Enhance word choice and phrasing for clearer expression"
     3. "Ensure consistency in tone and style throughout your text"
   - **Visual:** Screenshot showing before/after text improvement

3. **Feature Section 2: Vocabulary Application**
   - **Headline:** "Finally use the words you've learned"
   - **Description:** "Most language learners collect hundreds of vocabulary words but struggle to use them in real communication. Our Writing Assistant bridges this gap by helping you incorporate words you've saved into your everyday writing."
   - **Feature Points:**
     1. "Automatically finds relevant vocabulary from your notebooks"
     2. "Intelligently incorporates your learned words into natural, fluent text"
     3. "Reinforces your learning by creating a practical connection to each word"
   - **Visual:** Screenshot showing vocabulary integration

4. **Feature Section 3: Revision History**
   - **Headline:** "Track your progress over time"
   - **Description:** "See how your writing evolves as you incorporate more of your vocabulary into active use."
   - **Feature Points:**
     1. "Save and access your complete revision history"
     2. "Compare different versions of your writing"
     3. "See which vocabulary words you've successfully put into practice"
   - **Visual:** Screenshot of revision history interface

5. **Feature Section 4: Learning Feedback**
   - **Headline:** "Learn from detailed feedback"
   - **Description:** "Every revision comes with personalized feedback to help you understand how to improve your writing and use your vocabulary effectively."
   - **Feature Points:**
     1. "Get insights on grammar patterns that need attention"
     2. "See which specific vocabulary words were used and how"
     3. "Get suggestions for other vocabulary words you could incorporate next time"
   - **Visual:** Screenshot of feedback interface

6. **Final CTA Section**
   - **Headline:** "Start improving your writing today"
   - **Subheading:** "Take the next step in your language learning journey by refining your writing and using the words you learn."
   - **CTA Button:** "Try the Writing Assistant"
   - Background color: Light blue (matching hero section)

## 3. Update to Existing Personal Language Assistant Section

Remove "(coming soon)" from the line:
"Revise your writing using words from your notebooks &nbsp;<span class="text-blue-600 dark:text-blue-500">(coming soon)</span>"

**Confidence:** 95% - This plan provides comprehensive copywriting and UI skeleton details that align with the site's existing design patterns. Execution depends on receiving final visual assets. 