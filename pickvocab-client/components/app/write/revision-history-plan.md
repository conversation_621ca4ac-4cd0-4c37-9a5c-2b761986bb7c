# Revision History Support - Detailed Implementation Plan

## Introduction

This document outlines a comprehensive plan to implement persistent revision history support for the PickVocab Write Assistant. The goal is to enable users to save, browse, and restore their past writing revision sessions, including all three LLM-generated revisions, user preferences (such as tone and vocabulary toggle), and any feedback-based improvements. The plan covers backend data modeling, API design, frontend integration points, UI/UX considerations, and storage strategies. It also includes diagrams to illustrate the data relationships and workflow.

The design ensures that every revision session—whether triggered by a new revision, a refresh, or user feedback—is saved as a distinct, restorable history entry, along with the vocabulary cards fetched during that session. This will provide users with a rich, transparent, and efficient revision history experience.


---

## 1. Data Schema Design

### Main Model: `WritingRevisionHistory`

| Field               | Type                     | Description                                         |
|---------------------|--------------------------|-----------------------------------------------------|
| id                  | Auto-increment integer   | Primary key                                         |
| user                | FK to User               | Owner of the history entry                          |
| created_at          | timestamp                | When the entry was created                          |
| trigger_type        | enum ('Revise', 'Refresh') | What triggered this entry            |
| original_text       | text                     | User's input text                                   |
| use_my_vocabulary   | boolean                  | Was "Use My Vocabulary" enabled                     |
| selected_tone       | string                   | Tone selected (e.g., "Professional")                |
| revisions           | JSON array               | List of 3 revision objects (see below)              |


Each **revision object** inside `revisions` JSON contains:

```json
{
  "revision": "Revised text string",
  "feedback": "LLM feedback string",
  "learning_focus": ["point1", "point2"],
  "user_vocabularies_used": ["card_id1", "card_id2"] // optional
}
```

---

### Related Model: `GenericCard`

- Existing vocabulary card model (assumed to have `id`, `word`, etc.)

---

### Intermediate Table: `WritingRevisionHistoryCards`

| Field     | Type                   | Description                                  |
|-----------|------------------------|----------------------------------------------|
| id        | Auto-increment integer | Primary key                                 |
| history   | FK to WritingRevisionHistory | The revision history entry             |
| card      | FK to GenericCard      | A vocabulary card fetched during similarity search |

- This table links **only the vocabulary cards that were actually used** in any of the 3 revisions generated by the LLM for that session.
- It does **not** include vocabulary cards fetched during similarity search but **not incorporated** into the revisions.

---

## 2. API Endpoints

| Endpoint                          | Method | Purpose                                         |
|-----------------------------------|--------|-------------------------------------------------|
| `/api/write/history/`             | POST   | Save a new history entry and related cards      |
| `/api/write/history/`             | GET    | List user's history entries (paginated)         |
| `/api/write/history/{id}/`        | GET    | Fetch full details of a specific entry          |
| `/api/write/history/{id}/`        | DELETE | Delete an entry and its card links              |

---

## 3. Integration Points

- **After LLM returns revisions (Revise/Refresh):**
  - Save `WritingRevisionHistory` with metadata and revisions
  - Save related `GenericCard` IDs into `WritingRevisionHistoryCards`
- **On Write page load:**
  - Fetch recent history list
- **When user selects a history item:**
  - Fetch full entry (JSON data with revisions)
  - Restore input text, preferences, revisions
  - *Note:* We do **not** need to fetch related cards from the intermediate table, since the `user_vocabularies_used` lists inside the revisions JSON already capture which vocabulary cards were used.

---

## 4. UI/UX Implications

- Replace mock history with real data
- Show timestamp, trigger type, snippet, tone, vocab toggle
- Allow viewing full details and restoring
- On restore, set input text, preferences, revisions
- Optionally display **all related vocabulary cards** fetched during that session
- Optionally allow naming, tagging, deleting entries

---

## 5. Storage & Retrieval

- Store revisions as embedded JSON for atomicity
- Use intermediate table for efficient many-to-many queries
- Index on user and created_at
- Paginate history list
- Lazy load full details
- Optionally prune old entries per user

---

## 6. Data Model Diagram

```mermaid
classDiagram
    class User {
        id
        username
    }
    class WritingRevisionHistory {
        id
        user
        created_at
        trigger_type
        original_text
        use_my_vocabulary
        selected_tone
        revisions
    }
    class GenericCard {
        id
        word
        ...
    }
    class WritingRevisionHistoryCards {
        id
        history (FK)
        card (FK)
    }

    User "1" --> "many" WritingRevisionHistory
    WritingRevisionHistory "1" --> "many" WritingRevisionHistoryCards
    GenericCard "1" --> "many" WritingRevisionHistoryCards
```

---

## 7. Workflow Diagram

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant LLM
    participant Backend

    User->>Frontend: Click Revise/Refresh
    Frontend->>LLM: Send prompt
    LLM-->>Frontend: 3 revisions
    Frontend->>Backend: Save history entry + related cards
    Backend-->>Frontend: Success


    User->>Frontend: Open History
    Frontend->>Backend: Fetch history list
    Backend-->>Frontend: List

    User->>Frontend: Select entry
    Frontend->>Backend: Fetch full entry + cards
    Backend-->>Frontend: Entry details
    Frontend->>Frontend: Restore input, preferences, revisions
```

---
## Progress

- Django models `WritingRevisionHistory` and `WritingRevisionHistoryCards` have been added to `pickvocab-server/app/models.py`.
- The `GenericCard` model was confirmed to exist.
- The `trigger_type` field in `WritingRevisionHistory` is a plain `CharField(max_length=20)` without choices.
- Serializers for `WritingRevisionHistory` and `WritingRevisionHistoryCards` have been implemented in `pickvocab-server/app/serializers.py`.
- Views and API endpoints have been implemented in `pickvocab-server/app/views.py` and registered under `/api/write/history/`.
- Admin views for these models (`admin.py`) have been implemented.
- Frontend API client has been created in `pickvocab-client/api/writeHistory.ts` with interfaces and methods for:
  - Listing history entries with pagination
  - Getting a specific entry by ID
  - Creating new history entries
  - Deleting history entries
- Frontend integration with the Write component has been completed:
  - Modified `useWriteRevisionHandler.ts` to call the WriteHistoryApi after revisions are generated
  - Implemented saving revisions on initial generation (trigger_type: 'Revise')
  - Implemented saving revisions on refresh (trigger_type: 'Refresh')
- Updated the HistoryDialog component:
  - Replaced mock data with real history from the API
  - Added loading and error states
  - Implemented pagination for history entries
  - Added delete functionality for history entries
- Implemented restore functionality:
  - Created methods to restore a history entry (setting original text, selected tone, vocabulary toggle, and populating revisions)
  - Added restore button to history entries
  - Implemented double-click on entries to restore
  - Updated UI to reflect restored state

## Completed Implementation

All planned features for the Revision History support have been successfully implemented:

1. **Backend Implementation:**
   - Created and migrated database models
   - Implemented serializers for API data handling
   - Created RESTful API endpoints for CRUD operations
   - Added admin interface support

2. **Frontend API Integration:**
   - Developed TypeScript interfaces matching the backend models
   - Implemented API client with methods for all required operations
   - Added error handling and pagination support

3. **Write Assistant Integration:**
   - Integrated history saving across revision triggers (Revise, Refresh)
   - Ensured proper handling of vocabulary card references
   - Connected user preferences with history entries

4. **History UI Implementation:**
   - Replaced mock data with real API-driven history list
   - Implemented loading states and error handling
   - Added interactive features (restore, delete)
   - Enhanced UI to display complete revision context

5. **Restore Functionality:**
   - Implemented full state restoration (text, preferences, revisions)
   - Created intuitive UX for accessing and restoring history
   - Ensured proper handling of revision and vocabulary data during restore

## Summary

- Persistent, restorable revision history with full context
- User preferences saved per entry
- 3 revisions per entry, with vocab usage info
- Many-to-many link to all vocabulary cards fetched during similarity search
- Efficient storage, retrieval, and UI integration