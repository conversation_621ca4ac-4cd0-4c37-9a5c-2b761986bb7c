# Grammar Check Only Mode Plan

This document describes the detailed steps required to implement a "Grammar check only" mode in the Writing Assistant feature. In this mode:
- The AI only corrects grammar (spelling, punctuation) without altering tone or style.
- Only one revision is generated with fields: `revision`, `feedback`, `learning_focus`.
- History entries use `triggerType: 'GrammarCheck'` and are persisted like other revisions.

## 1. Add `grammarCheck` State in Page (`index.vue`)
- Declare `const grammarCheck = ref(false)` alongside existing `userText`, `useVocabulary`.
- Persist to `localStorage` under key `pickvocab.write.grammarCheck` on mount and on change.
- When `grammarCheck` is enabled, immediately clear `useVocabulary` (reset vocab toggle).

## 2. Extend `useWriteRevisionHandler` Signature
- Change handler signature to accept: `(userText, useVocabulary, grammarCheck)`.
- Widen `currentTriggerType` union to include `'GrammarCheck'`.
- In `initiateRevision()`, detect `grammarCheck.value` first, and call a new `initiateGrammarCheck()` helper.

## 3. Implement `initiateGrammarCheck()` in Handler
- Build a prompt via new `grammarCheckPrompt(userText.value)`.
- Call shared routine: `await generateAndProcessRevisions(prompt, false)`.
- Ensure history is saved with `triggerType = 'GrammarCheck'` (in `saveRevisionHistory`).
- Skip vocabulary search, direct single-Library call only.

## 4. Create New Prompt Helper (`grammarCheckPrompt.ts`)
- Export `function grammarCheckPrompt(userText: string): string`.
- Instructions:
  - Correct only grammar mistakes (spelling, punctuation).
  - Do not alter tone/style/structure.
  - Return exactly one YAML item with:
    ```yaml
    revisions:
      - revision: |-
          <corrected text>
        feedback: |-
          <list of grammar issues and suggestions>
        learning_focus:
          - <key learning point 1>
          - <key learning point 2>
    ```

## 5. Update Input UI (`InputSection.vue`)
- Add a new switch:
  ```vue
  <Switch v-model="grammarCheck" /> <span>Grammar check only</span>
  ```
  below the "Use My Vocabulary" toggle.
- Add helper text: "Only correct spelling, punctuation; do not change style or tone.".
- Pass `:disabled="grammarCheck"` to `<VocabularyToggle>` and `<ToneSelector>`.
- Change primary button label to `{{ grammarCheck ? 'Check' : 'Revise' }}`.

## 6. Extend UI Components to Accept `disabled`
- **VocabularyToggle.vue**: add `disabled` prop and bind it to its `<Switch :disabled>`.  
- **ToneSelector.vue**: add `disabled` prop and bind it to `<Select :disabled>`.

## 7. Adjust History API Typing (`useRevisionApi.ts`)
- Extend the `triggerType` union in `HistoryEntryData` to include `'GrammarCheck'`.
- Ensure `saveHistory` and `saveRevisionHistory` support passing `triggerType: 'GrammarCheck'`.

## 8. Update History Display (`HistoryDialog.vue`)
- In the loop rendering history entries (`v-for="entry in history"`):
  - Check if `entry.triggerType === 'GrammarCheck'`.
  - If it is, render a badge or label (e.g., `<Badge variant="outline">Grammar Check</Badge>`) next to the entry timestamp or title.
  - Conditionally render the tone display (`v-if="entry.triggerType !== 'GrammarCheck'"`), hiding it for grammar check entries.
