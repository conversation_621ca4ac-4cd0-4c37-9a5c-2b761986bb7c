<script setup lang="ts">
import { Switch } from '~/components/ui/switch'
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs'

const useVocabulary = defineModel<boolean>('useVocabulary', { required: true })

const props = defineProps<{
  disabled?: boolean
}>()
</script>

<template>
  <div>
    <div class="flex items-center">
      <Switch
        v-model="useVocabulary"
        class="data-[state=checked]:!bg-blue-700"
        :disabled="props.disabled"
      />
      <div class="flex items-center ml-2">
        <IconBook2 class="h-5 w-5 text-blue-600 mr-1.5" />
        <span class="text-sm font-medium text-gray-800">Use My Vocabulary</span>
      </div>
    </div>

    <div v-if="useVocabulary" class="mt-4 bg-blue-50 p-4 rounded-lg flex border border-blue-100">
      <IconBook2 class="h-5 w-5 text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
      <p class="text-sm text-gray-700">
        The AI will try to incorporate relevant words from your vocabulary notebooks to help you practice and reinforce your learning.
      </p>
    </div>
  </div>
</template>

<style scoped>
.bg-blue-50 {
  background-color: rgba(239, 246, 255, 0.8);
}
</style>