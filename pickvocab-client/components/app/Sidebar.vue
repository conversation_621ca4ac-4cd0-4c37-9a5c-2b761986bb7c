<script setup lang="ts">
// import { IconCards, IconBook2, IconBolt, IconKey, IconExclamationCircle } from '@tabler/icons-vue';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
// @ts-ignore
import IconBolt from '@tabler/icons-vue/dist/esm/icons/IconBolt.mjs';
// @ts-ignore
import IconKey from '@tabler/icons-vue/dist/esm/icons/IconKey.mjs';
// @ts-ignore
import IconExclamationCircle from '@tabler/icons-vue/dist/esm/icons/IconExclamationCircle.mjs';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';
// @ts-ignore
import IconSparkles from '@tabler/icons-vue/dist/esm/icons/IconSparkles.mjs';
// @ts-ignore
import IconPuzzle from '@tabler/icons-vue/dist/esm/icons/IconPuzzle.mjs';
// @ts-ignore
import IconPencil from '@tabler/icons-vue/dist/esm/icons/IconPencil.mjs';
// @ts-ignore
import IconBooks from '@tabler/icons-vue/dist/esm/icons/IconBooks.mjs';
import { isMobile } from 'is-mobile';
import { useAuthStore } from '~/stores/auth';
import { ref, onMounted } from 'vue';
const props = defineProps<{
  isDemoPage?: boolean
}>();

const store = useAppStore();
const llmStore = useLLMStore();
const isDeviceMobile = isMobile();
const authStore = useAuthStore();

const showWritingAssistant = ref(false);

onMounted(async () => {
  if (authStore.currentUser) {
    showWritingAssistant.value = await store.isFeatureFlagEnabled('revise_with_embedding', authStore.currentUser.id);
  }
});
</script>

<template>

  <aside id="logo-sidebar"
    class="fixed top-0 left-0 z-[35] w-64 h-dvh pt-20 lg:pt-16 transition-transform -translate-x-full bg-white border-r border-gray-200 sm:translate-x-0 dark:bg-gray-800 dark:border-gray-700"
    aria-label="Sidebar">
    <div class="h-full px-3 lg:pt-2 pb-4 overflow-y-auto bg-white dark:bg-gray-800">
      <ul class="flex flex-col h-full space-y-2 font-medium">
        <!-- <li>
          <a href="#"
            class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
              fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                d="m21 21-3.5-3.5M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z" />
            </svg>
            <span class="ms-3">Search</span>
          </a>
        </li> -->
        <li>
          <NuxtLink :to="isDemoPage ? '/demo/cards' : '/app/cards'" data-drawer-target="logo-sidebar"
            data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar"
            class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
            <icon-cards stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-cards>
            <span class="flex-1 ms-3 whitespace-nowrap">All Cards</span>
            <!-- <span
              class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">Pro</span> -->
          </NuxtLink >
          <NuxtLink :to="isDemoPage ? '/demo/cards' : '/app/cards'"
            class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
            <icon-cards stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-cards>
            <span class="flex-1 ms-3 whitespace-nowrap">All Cards</span>
            <!-- <span
              class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">Pro</span> -->
          </NuxtLink >
        </li>
        <li>
          <NuxtLink :to="isDemoPage ? '/demo/notebooks' : '/app/notebooks'" data-drawer-target="logo-sidebar"
            data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar"
            class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
            <icon-book-2 stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-book-2>
            <span class="flex-1 ms-3 whitespace-nowrap">Notebooks</span>
            <!-- <span
              class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">Pro</span> -->
          </NuxtLink >
          <NuxtLink :to="isDemoPage ? '/demo/notebooks' : '/app/notebooks'"
            class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
            <icon-book-2 stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-book-2>
            <span class="flex-1 ms-3 whitespace-nowrap">Notebooks</span>
            <!-- <span
              class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">Pro</span> -->
          </NuxtLink >
        </li>
        <li>
          <template v-if="!isDemoPage">
            <div @click="store.showReviewModal()" data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar"
              aria-controls="logo-sidebar"
              class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group cursor-pointer">
              <!-- <svg
              class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
              aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 16">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M1 8h11m0 0L8 4m4 4-4 4m4-11h3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-3" />
            </svg> -->
              <!-- <span class="flex-1 ms-3 whitespace-nowrap">Sign In</span> -->
              <icon-bolt stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-bolt>
              <span class="flex-1 ms-3 whitespace-nowrap">Review</span>
            </div>
            <div @click="store.showReviewModal()"
              class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group cursor-pointer">
              <!-- <svg
              class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
              aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 16">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M1 8h11m0 0L8 4m4 4-4 4m4-11h3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-3" />
            </svg> -->
              <!-- <span class="flex-1 ms-3 whitespace-nowrap">Sign In</span> -->
              <icon-bolt stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-bolt>
              <span class="flex-1 ms-3 whitespace-nowrap">Review</span>
            </div>
          </template>
          <template v-else>
            <div data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar"
              class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group bg-opacity-70 text-opacity-70 cursor-not-allowed">
              <!-- <svg
              class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
              aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 16">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M1 8h11m0 0L8 4m4 4-4 4m4-11h3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-3" />
            </svg> -->
              <!-- <span class="flex-1 ms-3 whitespace-nowrap">Sign In</span> -->
              <icon-bolt stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-bolt>
              <span class="flex-1 ms-3 whitespace-nowrap">Review</span>
            </div>
            <div
              class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group bg-opacity-70 text-opacity-70 cursor-not-allowed">
              <!-- <svg
              class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
              aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 16">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M1 8h11m0 0L8 4m4 4-4 4m4-11h3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-3" />
            </svg> -->
              <!-- <span class="flex-1 ms-3 whitespace-nowrap">Sign In</span> -->
              <icon-bolt stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-bolt>
              <span class="flex-1 ms-3 whitespace-nowrap">Review</span>
            </div>
          </template>
        </li>
        <li>
          <template v-if="!isDemoPage">
            <NuxtLink to="/app/ask" data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar"
              aria-controls="logo-sidebar"
              class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group cursor-pointer">
              <icon-sparkles stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-sparkles>
              <span class="flex-1 ms-3 whitespace-nowrap">Ask</span>
              <!-- <span
                class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">Pro</span> -->
            </NuxtLink >
            <NuxtLink to="/app/ask"
              class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group cursor-pointer">
              <icon-sparkles stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-sparkles>
              <span class="flex-1 ms-3 whitespace-nowrap">Ask</span>
              <!-- <span
                class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">Pro</span> -->
            </NuxtLink >
          </template>
          <template v-else>
            <div data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar"
              class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group bg-opacity-70 text-opacity-70 cursor-not-allowed">
              <icon-sparkles stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-sparkles>
              <span class="flex-1 ms-3 whitespace-nowrap">Ask</span>
              <!-- <span
                class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">Pro</span> -->
            </div>
            <div
              class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group bg-opacity-70 text-opacity-70 cursor-not-allowed">
              <icon-sparkles stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-sparkles>
              <span class="flex-1 ms-3 whitespace-nowrap">Ask</span>
              <!-- <span
                class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">Pro</span> -->
            </div>
          </template>
        </li>
        <li v-if="showWritingAssistant">
          <template v-if="!isDemoPage">
            <NuxtLink to="/app/write" data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar"
              aria-controls="logo-sidebar"
              class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group cursor-pointer">
              <icon-pencil stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-pencil>
              <span class="flex-1 ms-3 whitespace-nowrap">Writing Assistant</span>
            </NuxtLink >
            <NuxtLink to="/app/write"
              class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group cursor-pointer">
              <icon-pencil stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-pencil>
              <span class="flex-1 ms-3 whitespace-nowrap">Writing Assistant</span>
            </NuxtLink >
          </template>
          <template v-else>
            <div data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar"
              class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group bg-opacity-70 text-opacity-70 cursor-not-allowed">
              <icon-pencil stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-pencil>
              <span class="flex-1 ms-3 whitespace-nowrap">Writing Assistant</span>
            </div>
            <div
              class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group bg-opacity-70 text-opacity-70 cursor-not-allowed">
              <icon-pencil stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-pencil>
              <span class="flex-1 ms-3 whitespace-nowrap">Writing Assistant</span>
            </div>
          </template>
        </li>
        <li>
          <template v-if="!isDemoPage">
            <NuxtLink :to="'/app/reader'" data-drawer-target="logo-sidebar"
              data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar"
              class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
              <icon-books stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-books>
              <span class="flex-1 ms-3 whitespace-nowrap">Read Books</span>
              <span
                  class="inline-flex items-center justify-center px-2 ms-3 text-xs font-medium text-blue-700 bg-blue-100 rounded dark:bg-blue-900 dark:text-blue-300">New</span>
            </NuxtLink >
            <NuxtLink :to="'/app/reader'"
              class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
              <icon-books stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-books>
              <span class="flex-1 ms-3 whitespace-nowrap">Read Books</span>
              <span
                  class="inline-flex items-center justify-center px-2 ms-3 text-xs font-medium text-blue-700 bg-blue-100 rounded dark:bg-blue-900 dark:text-blue-300">New</span>
            </NuxtLink >
          </template>
          <template v-else>
            <div data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar"
              class="sm:hidden flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group bg-opacity-70 text-opacity-70 cursor-not-allowed">
              <icon-books stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-books>
              <span class="flex-1 ms-3 whitespace-nowrap">Read Books</span>
              <span
                  class="inline-flex items-center justify-center px-2 ms-3 text-xs font-medium text-blue-700 bg-blue-100 rounded dark:bg-blue-900 dark:text-blue-300">New</span>
            </div>
            <div
              class="hidden sm:flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group bg-opacity-70 text-opacity-70 cursor-not-allowed">
              <icon-books stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-books>
              <span class="flex-1 ms-3 whitespace-nowrap">Read Books</span>
              <span
                  class="inline-flex items-center justify-center px-2 ms-3 text-xs font-medium text-blue-700 bg-blue-100 rounded dark:bg-blue-900 dark:text-blue-300">New</span>
            </div>
          </template>
        </li>
        <li class="!mt-auto">
          <div @click="store.showAPIKeyModal()"
            class="flex items-center text-sm p-2 text-gray-700 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group cursor-pointer">
            <!-- <svg
              class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
              aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 16">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M1 8h11m0 0L8 4m4 4-4 4m4-11h3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-3" />
            </svg> -->
            <!-- <span class="flex-1 ms-3 whitespace-nowrap">Sign In</span> -->
            <icon-key stroke="1.5" class="w-5 h-5 text-gray-700 dark:text-white"></icon-key>
            <span class="flex-1 ms-3 whitespace-nowrap">API Keys</span>
            <icon-exclamation-circle v-if="llmStore.getErrorFromValidation('global')"
              class="flex-shrink-0 w-5 h-5 text-red-800 dark:text-white"></icon-exclamation-circle>
          </div>
        </li>
        <li v-if="!isDeviceMobile">
          <a href="https://chromewebstore.google.com/detail/pickvocab-ai-powered-dict/nfhhjfaahjkjdjbkpacapdblonogknag?hl=en" target="_blank"
            class="flex items-center text-sm p-2 text-blue-600 rounded-lg bg-blue-50 dark:text-blue-400 dark:bg-gray-700 hover:bg-blue-100 dark:hover:bg-gray-600 group cursor-pointer border border-blue-100 dark:border-blue-800">
            <icon-puzzle stroke="1.5" class="w-5 h-5 text-blue-600 dark:text-blue-400"></icon-puzzle>
            <span class="flex-1 ms-3 whitespace-nowrap">Install Extension</span>
          </a>
        </li>
      </ul>
    </div>
  </aside>

</template>

<style scoped></style>
