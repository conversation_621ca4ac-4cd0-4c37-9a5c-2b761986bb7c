<script setup lang="ts">
// import { IconArrowRight } from '@tabler/icons-vue';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';
import { CardType } from '~/utils/card';

const props = defineProps<{
  card: Card
}>();
</script>

<template>
  <div class="w-full h-full box-border">
    <div class="sm:ml-64 pt-14 pl-10 pr-10 flex flex-col h-full">
      <div class="w-full flex justify-center items-center flex-grow">
        <div v-if="card.cardType === CardType.DefinitionCard">
          <p class="text-3xl text-gray-700 font-semibold">
            {{ card.word }}
            <span class="inline-block text-sm border rounded-xl px-2 align-middle"
              :class="stylesForPartOfSpeech(card.definition.partOfSpeech)"
              v-if="card.definition.partOfSpeech !== undefined">
              {{ card.definition.partOfSpeech }}
            </span>
          </p>
        </div>
        <div v-else>
          <p class="text-3xl text-gray-700 font-semibold">
            {{ card.wordInContext.word }}
            <span class="inline-block text-sm border rounded-xl px-2 align-middle"
              :class="stylesForPartOfSpeech(card.wordInContext.definition.partOfSpeech)"
              v-if="card.wordInContext.definition?.partOfSpeech !== undefined">
              {{ card.wordInContext.definition.partOfSpeech }}
            </span>
          </p>
        </div>
      </div>
      <div class="sticky w-full bottom-4 flex justify-center">
        <div class="flex text-sm text-gray-700 py-2 px-2 rounded-md border border-gray-100 shadow-md bg-white">
          <button @click="$emit('show-definition')" class="px-2 py-1 border rounded-md border-gray-300 flex items-center hover:bg-gray-100">
              <icon-arrow-right class="inline-block w-5 h-5 me-2"></icon-arrow-right>
              <span>Show definition</span>
            </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
