<script setup lang="ts">
// import { IconBolt, IconX } from '@tabler/icons-vue';
// @ts-ignore
import IconBolt from '@tabler/icons-vue/dist/esm/icons/IconBolt.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';
import type { Modal } from 'flowbite';
import SubmitButton from '../../ui/SubmitButton.vue';

const store = useAppStore();
const authStore = useAuthStore();
const router = useRouter();
const { isShowReviewModal } = storeToRefs(store);

let suggestedDecks: Ref<Deck[]> = ref([]);
const selectedDecks = ref<Deck[]>([]);
const deckSelectedIdx = ref(0);
const searchDeckText = ref('');
const deckRefs = ref([]);
const isSubmitting = ref(false);

let modal: Modal;

onMounted(() => {
  useFlowbite(({ Modal }) => {
    const $modelEl = document.getElementById('review-modal');
    modal = new Modal($modelEl);

    modal.updateOnShow(async () => {
      searchDeckText.value = '';
      suggestedDecks.value = [{
        id: -1,
        name: 'Master notebook',
        description: 'Contains all of your cards',
        cards: [],
        totalCards: 0,
        owner: authStore.currentUser?.id,
        isDemo: false,
      } as Deck].concat(await store.searchDecks(searchDeckText.value)); // TODO: lazy load on scroll here

      selectedDecks.value = [suggestedDecks.value[0]];
      deckSelectedIdx.value = 0;
      window.addEventListener('keydown', handleKeyPress, true);
    });

    modal.updateOnHide(() => {
      store.hideReviewModal();
      window.removeEventListener('keydown', handleKeyPress, true);
    });
  });
});

watch(isShowReviewModal, (value) => {
  if (value === true) {
    modal.show();
  } else {
    modal.hide();
  }
});

watch(deckSelectedIdx, (value) => {
  (deckRefs.value[value] as any).scrollIntoView(false);
});

let currentController: AbortController | undefined;

watch(searchDeckText, async () => {
  if (currentController) {
    currentController.abort();
  }
  currentController = new AbortController();
  suggestedDecks.value = await store.searchDecks(searchDeckText.value, currentController.signal);
  if (deckSelectedIdx.value >= suggestedDecks.value.length) {
    deckSelectedIdx.value = 0;
  }
});

function addSelectedDeck(idx: number) { // suggestedDecks idx
  const selectedDeckIdx = selectedDecks.value.findIndex((deck) => deck.id === suggestedDecks.value[idx].id);
  if (selectedDeckIdx !== -1) {
    removeDeck(selectedDeckIdx);
    return;
  }
  selectedDecks.value.push(suggestedDecks.value[idx]);
}

function removeDeck(idx: number) { // selectedDeck idx
  if (idx >= 0) {
    selectedDecks.value.splice(idx, 1);
  }
}

function isInSelectedDecks(deck: Deck) {
  return selectedDecks.value.find((d) => d.id === deck.id);
}

function handleKeyPress(e: KeyboardEvent) {
  if (e.key === 'ArrowUp') {
    e.stopPropagation();
    e.preventDefault();
    deckSelectedIdx.value = Math.max(0, deckSelectedIdx.value - 1);
  } else if (e.key === 'ArrowDown') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedDecks.value.length > 0) {
      deckSelectedIdx.value = Math.min(suggestedDecks.value.length - 1, deckSelectedIdx.value + 1);
    }
  } else if (e.key === 'Enter') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedDecks.value.length > 0) {
      addSelectedDeck(deckSelectedIdx.value);
      searchDeckText.value = '';
    }
  }
}

async function createSetOfReviewedCards() {
  isSubmitting.value = true;
  
  try {
    const baseReview: BaseReview = {
      deckIds: selectedDecks.value.filter((deck) => deck.id !== -1).map((deck) => deck.id),
      isMaster: selectedDecks.value.some((deck) => deck.id === -1),
      cards: [],
    }
    const review = await store.createReview(baseReview);
    modal.hide();
    router.push({ name: 'app-reviews-id', params: { id: review.id } });
  } catch (error) {
    console.error('Error creating review:', error);
    // Optional: Show error message to user
  } finally {
    isSubmitting.value = false;
  }
}
</script>

<template>
  <!-- Main modal -->
  <div id="review-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <icon-bolt stroke-width="2.5" class="inline-block w-5 h-5 me-3"></icon-bolt>
            <span>Review</span>
          </h3>
          <button type="button" @click="store.hideReviewModal()"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <form class="px-4 pb-4 md:px-5 md:pb-5">
          <div class="col-span-2">
            <div class="col-span-2 mt-4">
              <label for="name" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Create a set of reviewed cards from notebooks:</label>
              <input type="text"
                class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                required v-model="searchDeckText" placeholder="Search">

              <div class="flex flex-wrap items-center mt-2">
                <div v-for="(deck, idx) in selectedDecks"
                  class="flex items-center bg-gray-50 py-1 px-2 rounded border border-gray-300 text-gray-600 text-sm mr-2 mt-1">
                  <span class="mr-3" :class="{ 'font-semibold': deck.id === -1 }">{{ deck.name }}</span>
                  <icon-x @click="removeDeck(idx)" class="inline-block w-3 h-3 cursor-pointer"></icon-x>
                </div>
              </div>

              <div class="pb-4">
                <div class="mt-4 h-[360px] overflow-y-auto">
                  <div class="pr-4">
                    <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Your notebooks</p>
                    <ul class="my-4 space-y-3">
                      <li v-for="(deck, idx) in suggestedDecks" ref="deckRefs"
                        class="cursor-pointer p-3 text-sm font-bold text-gray-600 border-2 rounded-lg hover:bg-gray-200 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
                        :class="{
                          'bg-gray-200': idx === deckSelectedIdx,
                          'bg-gray-50': idx !== deckSelectedIdx,
                          'border-blue-600': isInSelectedDecks(deck),
                        }" @click="addSelectedDeck(idx)">
                        <div class="flex items-center ms-3">
                          <span class="">{{ deck.name }}</span>
                        </div>
                        <p class="ms-3 font-normal text-gray-500" v-if="deck.description">{{ deck.description }}
                        </p>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <SubmitButton 
              text="Review"
              loadingText="Creating..."
              :isLoading="isSubmitting" 
              @click="createSetOfReviewedCards" 
            />
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
