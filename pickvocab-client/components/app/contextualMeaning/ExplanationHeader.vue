<script setup lang="ts">
const props = withDefaults(defineProps<{
  word: string
}>(), {});
</script>

<template>
  <div class="flex items-center py-2 border-b">
    <p class="text-3xl text-gray-700 font-semibold">{{ word }}</p>

    <div v-if="$slots.wordInfo" class="ml-2">
      <slot name="wordInfo"></slot>
    </div>

    <div class="ml-auto" v-if="$slots.settings">
      <slot name="settings"></slot>
    </div>
  </div>
</template>

<style scoped></style>
