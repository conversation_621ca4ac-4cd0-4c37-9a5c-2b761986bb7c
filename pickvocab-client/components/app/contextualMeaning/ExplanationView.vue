<script setup lang="ts">
import ExplanationHeader from "./ExplanationHeader.vue";
import ExplanationSimple from "./ExplanationSimple.vue";
import ExplanationFull from "./ExplanationFull.vue";
import ExplanationWordInfo from "./ExplanationWordInfo.vue";
import ExplanationWordSetting from "./ExplanationWordSetting.vue";
import type { WordInContextEntry } from "pickvocab-dictionary";
import Spinner from '~/components/app/utils/Spinner.vue';

const props = withDefaults(defineProps<{
  word?: string,
  wordEntry?: WordInContextEntry,
  llmModel?: LLMModel,
  isLoading?: boolean,
}>(), {
  isLoading: false,
});

const isDetailed = defineModel<boolean>('isDetailed', { default: false });
const selectedSimpleViewLanguage = defineModel<string>('selectedSimpleViewLanguage', {
  default: 'English'
});

const emit = defineEmits<{
  (e: 'addCard', wordEntry: WordInContextEntry, callback?: () => void): void;
  (e: 'refresh', language?: string): void;
  (e: 'simpleLookupForLanguage', language: string): void;
}>();
</script>

<template>
  <div class="p-6 xl:p-8 w-full bg-white border border-gray-200 rounded-md">
    <ExplanationHeader v-if="word !== undefined" :word="word">
      <template #wordInfo>
        <ExplanationWordInfo
          :word-entry="wordEntry"
          :llm-model="llmModel"
          @refresh="emit('refresh', isDetailed ? 'English' : selectedSimpleViewLanguage)"
        />
      </template>

      <template #settings>
        <ExplanationWordSetting
          :word-entry="wordEntry"
          :llm-model="llmModel"
          :is-loading="isLoading"
          @add-card="(wordEntry, callback) => emit('addCard', wordEntry, callback)"
          @refresh="emit('refresh', isDetailed ? 'English' : selectedSimpleViewLanguage)"
          v-model:is-detailed="isDetailed"
        />
      </template>
    </ExplanationHeader>
    <ExplanationSimple
      v-if="wordEntry !== undefined && !isDetailed"
      :word-entry="wordEntry"
      v-model:selected-language="selectedSimpleViewLanguage"
      class="mt-4"
    ></ExplanationSimple>
    <ExplanationFull
      v-if="wordEntry !== undefined && isDetailed"
      :word-entry="wordEntry"
      class="mt-4"
    >
    </ExplanationFull>
    <Spinner v-if="isLoading" class="mt-24 mb-12" />
  </div>
</template>

<style scoped></style>
