<script setup lang="ts">
import { FwbSelect } from "flowbite-vue";
import type { WordInContextEntry } from "pickvocab-dictionary";
import TiptapEditor from '~/components/editor/TiptapEditor.vue';
import { languages } from '@/utils/languages';

const props = withDefaults(
  defineProps<{
    wordEntry: WordInContextEntry;
    showContext?: boolean;
    availableLanguages?: string[];
  }>(),
  {
    showContext: false,
  }
);

const selectedLanguage = defineModel<string>("selectedLanguage", {
  default: "English",
});

const filteredLanguages = computed(() => {
  if (props.availableLanguages && props.availableLanguages.length > 0) {
    return languages.filter(lang => props.availableLanguages!.includes(lang.value));
  }
  return languages;
});
</script>

<template>
  <div v-if="wordEntry.definitionShort">
    <div v-if="showContext">
      <blockquote class="text-gray-600 text-sm">
        <TiptapEditor
          :text="wordEntry.context"
          :selected-text="wordEntry.word"
          :offset="wordEntry.offset"
          :css-classes="'tiptap prose prose-sm sm:prose-base lg:prose-lg xl:prose-2xl focus:outline-none w-full max-h-[300px] overflow-auto !text-gray-600 !bg-gray-50 p-4'"
          :show-options="false"
          :show-bubble-menu="false"
          :editable="false"
        />
      </blockquote>
      <!-- <blockquote class="px-4 border-l-4 border-gray-200 border-solid italic text-gray-600 text-sm" v-html="getHighlightedContext(wordEntry)"></blockquote> -->
      <!-- <p class="text-gray-600 text-sm">The fact that the battery life is on par with the macbook air 15 while the vivobook has a 120Hz screen and two fans is plain bonkers for a windows machine</p> -->
    </div>
    <div class="mt-8">
      <fwb-select
        class="sm:w-48"
        v-model="selectedLanguage"
        :options="filteredLanguages"
        placeholder="Select Language"
      />
    </div>
    <div class="mt-8">
      <p class="text-xl text-gray-700 font-semibold">Explanation</p>
      <p
        v-if="selectedLanguage === 'English'"
        class="text-base text-gray-600 mt-2"
      >
        {{ wordEntry.definitionShort.explanation }}
      </p>
      <p
        v-else-if="wordEntry.definitionShort.languages && wordEntry.definitionShort.languages[selectedLanguage].explanation"
        class="text-base text-gray-600 mt-2"
      >
        {{ wordEntry.definitionShort.languages[selectedLanguage].explanation }}
      </p>
    </div>
    <div class="mt-8 border-t">
      <NuxtLink
        :to="{ name: 'app-dictionary', query: { word: wordEntry.word } }"
        target="_blank"
        rel="nofollow"
        class="mt-4 font-semibold text-blue-800 flex items-center hover:underline"
      >
        <span>See other definitions of "{{ wordEntry.word }}"</span>
        <icon-arrow-right class="w-4 h-4 ml-1"></icon-arrow-right>
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped></style>
