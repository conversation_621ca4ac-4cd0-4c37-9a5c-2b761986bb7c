<script setup lang="ts">
// @ts-ignore
import IconDotsVertical from '@tabler/icons-vue/dist/esm/icons/IconDotsVertical.mjs';
// @ts-ignore
import IconReload from '@tabler/icons-vue/dist/esm/icons/IconReload.mjs';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
import { type WordInContextEntry } from 'pickvocab-dictionary';
import { FwbToggle } from "flowbite-vue";
import Dropdown from '~/components/app/utils/Dropdown.vue';
import SubmitButton from '~/components/ui/SubmitButton.vue';

const props = withDefaults(defineProps<{
  wordEntry?: WordInContextEntry,
  llmModel?: LLMModel,
  isLoading?: boolean,
}>(), {
  isLoading: false,
});

const isAddCardLoading = ref(false);
const isDetailed = defineModel<boolean>('isDetailed', { default: false });
const emit = defineEmits(['addCard', 'refresh']);

const llmStore = useLLMStore();

function handleAddCard() {
  if (!props.wordEntry) return;
  
  isAddCardLoading.value = true;
  // Pass a callback that will be called when operation completes
  emit('addCard', props.wordEntry, () => {
    isAddCardLoading.value = false;
  });
}
</script>

<template>
  <div class="flex items-center">
    <FwbToggle
      class="mr-2"
      :class="{ 'opacity-50': isLoading }"
      label="Detailed"
      :disabled="isLoading"
      v-model="isDetailed"
    ></FwbToggle>
    <Dropdown
      :offsetDistance="-10"
      :offsetSkidding="-70"
      class="ml-auto sm:hidden"
      :hide="!wordEntry"
    >
      <template #trigger>
        <div class="p-2 hover:rounded hover:bg-gray-100 cursor-pointer">
          <icon-dots-vertical class="w-5 h-5"></icon-dots-vertical>
        </div>
      </template>
      <template #body>
        <div
          v-if="wordEntry"
          class="z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600"
        >
          <div class="px-4 py-3" role="none">
            <div v-if="llmModel" class="flex items-center">
              <img
                :src="llmStore.providerMap[llmModel.provider].logo"
                alt="llm-model-img"
                class="w-4 h-4"
              />
              <p
                class="ml-1 text-sm font-medium text-gray-900 dark:text-white"
                role="none"
              >
                {{ llmModel.name }}
              </p>
            </div>
            <p
              v-if="wordEntry && wordEntry.createdAt"
              class="mt-1 text-sm text-gray-600 truncate dark:text-gray-300"
              role="none"
            >
              {{ new Date(wordEntry.createdAt).toLocaleString() }}
            </p>
          </div>
          <ul class="py-1" role="none">
            <li>
              <SubmitButton
                text="Save"
                loading-text="Saving..."
                :is-loading="isAddCardLoading"
                @click="handleAddCard"
                :is-primary="false"
                class="flex items-center px-5 py-2 text-sm text-gray-600 hover:bg-gray-100 w-full !justify-start"
              >
                <icon-cards class="inline-block w-4 h-4 mr-2"></icon-cards>
                <span v-if="!isAddCardLoading">Save</span>
                <span v-else>Saving...</span>
              </SubmitButton>
            </li>
            <li>
              <div
                @click="emit('refresh')"
                class="flex items-center px-5 py-2 text-sm text-gray-600 hover:bg-gray-100 cursor-pointer"
                role="menuitem"
              >
                <icon-reload class="inline-block w-4 h-4"></icon-reload>
                <span class="ml-2">Re-lookup</span>
              </div>
            </li>
          </ul>
        </div>
      </template>
    </Dropdown>
    <SubmitButton 
      text="Save" 
      loading-text="Saving..."
      :is-loading="isAddCardLoading" 
      @click="handleAddCard"
      :is-primary="false"
      class="hidden sm:block text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-1.5 ml-auto"
    />
  </div>
</template>

<style scoped></style>
