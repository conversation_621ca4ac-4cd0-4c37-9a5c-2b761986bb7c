<script setup lang="ts">
import type { WordInContextEntry } from 'pickvocab-dictionary';
// @ts-ignore
import IconReload from '@tabler/icons-vue/dist/esm/icons/IconReload.mjs';
import { FwbTooltip } from 'flowbite-vue';

const props = withDefaults(defineProps<{
  wordEntry?: WordInContextEntry,
  llmModel?: LLMModel
}>(), {});

const llmStore = useLLMStore();

const emit = defineEmits(['refresh']);
</script>

<template>
  <FwbTooltip class="hidden sm:block" theme="dark">
    <template #trigger>
      <icon-reload @click="emit('refresh')" stroke-width="2.5"
        class="inline-block text-gray-400 w-4 h-4 cursor-pointer hover:text-blue-500"></icon-reload>
    </template>
    <template #content>
      <div v-if="wordEntry"
        class="flex items-center px-3 py-2 border font-medium text-sm rounded-lg shadow-sm bg-white">
        <span v-if="llmModel" class="flex items-center">
          <img :src="llmStore.providerMap[llmModel.provider].logo" alt="llm-model-img" class="w-4 h-4">
          <span class="ml-1">{{ llmModel.name }}&nbsp;</span>
        </span>
        <span v-if="wordEntry.updatedAt">{{ new Date(wordEntry.updatedAt).toLocaleString() }}</span>
      </div>
    </template>
  </FwbTooltip>
</template>

<style scoped></style>
