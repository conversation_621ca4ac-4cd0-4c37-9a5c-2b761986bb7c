import { ref } from 'vue';
import type { Ref } from 'vue';
import { db } from '~/components/app/epubReader/recentBooksDB';
import type { RecentBookHandleEntry } from '~/components/app/epubReader/recentBooksDB';

const MAX_RECENT_BOOKS = 20;

export function useRecentBooks() {
  const recentBooksList: Ref<RecentBookHandleEntry[]> = ref([]);
  const isLoading: Ref<boolean> = ref(false);
  const error: Ref<string | null> = ref(null);

  async function addOrUpdateRecentBook(
    entry: Omit<RecentBookHandleEntry, 'lastOpened' | 'id' | 'coverImage'> & { id: string, lastOpened?: number, coverImage?: string }
  ): Promise<string | void> {
    isLoading.value = true;
    error.value = null;
    try {
      const bookId = entry.id; // id is the bookId
      const existingEntry = await db.recentBooks.get(bookId);

      const entryToPut: RecentBookHandleEntry = {
        ...entry,
        id: bookId, // Ensure id is the bookId string
        coverImage: entry.coverImage || undefined, // Add coverImage
        lastOpened: entry.lastOpened || Date.now(),
      };

      await db.recentBooks.put(entryToPut);

      // Pruning logic
      const count = await db.recentBooks.count();
      if (count > MAX_RECENT_BOOKS) {
        const booksToDelete = await db.recentBooks
          .orderBy('lastOpened')
          .limit(count - MAX_RECENT_BOOKS)
          .toArray();
        const idsToDelete = booksToDelete.map(b => b.id);
        await db.recentBooks.bulkDelete(idsToDelete);
      }
      // After adding/updating, refresh the list
      await fetchRecentBooks();
      return entryToPut.id;
    } catch (e: any) {
      console.error('Failed to add or update recent book:', e);
      error.value = e.message || 'Failed to add or update recent book';
    } finally {
      isLoading.value = false;
    }
  }

  async function fetchRecentBooks(limit: number = 10): Promise<void> {
    isLoading.value = true;
    error.value = null;
    try {
      const books = await db.recentBooks
        .orderBy('lastOpened')
        .reverse() // Sorts by lastOpened descending
        .limit(limit)
        .toArray();
      recentBooksList.value = books;
    } catch (e: any) {
      console.error('Failed to fetch recent books:', e);
      error.value = e.message || 'Failed to fetch recent books';
      recentBooksList.value = []; // Clear list on error
    } finally {
      isLoading.value = false;
    }
  }

  async function getRecentBookById(id: string): Promise<RecentBookHandleEntry | undefined> {
    error.value = null;
    try {
      const book = await db.recentBooks.get(id);
      return book;
    } catch (e: any) {
      console.error(`Failed to get recent book by id ${id}:`, e);
      error.value = e.message || `Failed to get recent book by id ${id}`;
      return undefined;
    }
  }

  async function removeRecentBook(id: string): Promise<void> {
    isLoading.value = true;
    error.value = null;
    try {
      await db.recentBooks.delete(id);
      // After successful deletion, refresh the list
      await fetchRecentBooks();
    } catch (e: any) {
      console.error(`Failed to remove recent book with id ${id}:`, e);
      error.value = e.message || `Failed to remove recent book with id ${id}`;
    } finally {
      isLoading.value = false;
    }
  }

  async function clearAllRecentBooks(): Promise<void> {
    isLoading.value = true;
    error.value = null;
    try {
      await db.recentBooks.clear();
      recentBooksList.value = []; // Update reactive list
    } catch (e: any) {
      console.error('Failed to clear all recent books:', e);
      error.value = e.message || 'Failed to clear all recent books';
    } finally {
      isLoading.value = false;
    }
  }

  return {
    recentBooksList,
    isLoading,
    error,
    addOrUpdateRecentBook,
    fetchRecentBooks,
    getRecentBookById,
    removeRecentBook,
    clearAllRecentBooks,
  };
}