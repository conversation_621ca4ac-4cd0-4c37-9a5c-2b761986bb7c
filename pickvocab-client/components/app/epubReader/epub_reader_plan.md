# Dedicated EPUB Reading View Plan

## Goal

Create a dedicated, full-screen, mobile-friendly reading page (`/app/reader`) with a stripped-down design, focusing purely on an immersive reading experience. This view will allow users to open local EPUB files (and potentially other formats later) and perform contextual lookups on selected text, displaying results in a non-intrusive modal.

## Chosen Library

*   `epub.js` (already added to `pickvocab-client` dependencies)

## UI/Styling Guidelines

*   **CSS Framework:** Utilize [Tailwind CSS](https://tailwindcss.com/) for all custom styling.
*   **Component Library:** Employ [shadcn-vue](https://www.shadcn-vue.com/) for pre-built UI components (e.g., buttons, modals, dialogs) where appropriate to ensure a consistent look and feel. Favor using these components over building custom ones from scratch if a suitable shadcn-vue component exists
* Use context7 mcp server or web search to fetch shadcn-vue library docs if needed.

## Plan Phases

### Phase 1: Core Reader Page and EPUB Rendering

1.  **Create New Route and Page Component: [COMPLETED]**
    *   **Action:** Define a new route `/app/reader`.
    *   **Action:** Create a new Vue page component at `pickvocab-client/pages/app/reader.vue`.
    *   **Details:** This page will serve as the container for the dedicated reader. It should aim for a full-screen, minimal UI from the start.

2.  **Implement File Opening and Initial Layout in `reader.vue`: [COMPLETED]**
    *   **UI:** Added an "Open Book" button and a hidden file input (`<input type="file" accept=".epub,application/epub+zip">`) to `reader.vue`.
    *   **Logic:** Implemented a method to handle file selection. When a user selects a file:
        *   Determine file type (initially only EPUB).
        *   Read the file as an `ArrayBuffer`.
        *   Store this `ArrayBuffer` and file type in local `ref`s.
    *   **UI:** Improved layout with clean, minimal styling. Use h-screen and calc() to ensure the reader takes full viewport height.
    *   **Layout:** Basic full-screen structure that maximizes reading space.

3.  **Create `StandaloneEpubReader.vue` Component: [COMPLETED]**
    *   **Location:** `pickvocab-client/components/app/epubReader/StandaloneEpubReader.vue`.
    *   **Props:**
        *   `epubData: ArrayBuffer` (required, passed from `reader.vue` when fileType is 'epub').
    *   **Template:** Created a clean layout with dedicated content area and fixed control bar.
    *   **Initialization:**
        *   If `epubData` is present, initialize `epub.js`: `const book = ePub(props.epubData);`.
        *   Render the book with proper dimensions.
        *   Implemented robust error handling and dimension detection.
    *   **Controls:**
        *   Implemented "Previous Chapter" and "Next Chapter" navigation (originally page-based).
        *   Added font size adjustment button that cycles between small, medium and large.
        *   Prepared a placeholder for table of contents.
    *   **Rendering Flow:**
        *   **[COMPLETED] Changed from `paginated` to `scrolled-doc` flow.** The reader now displays content as a continuous scroll within each chapter. Navigation (buttons, swipe, keyboard) advances chapter by chapter.
    *   **Styling:**
        *   Custom CSS for book content to improve typography and readability.
        *   Rich styling for control elements with hover effects.
        *   Custom container and iframe styling to ensure proper rendering.
    *   **Cleanup:** Proper component cleanup on unmount.

4.  **Implement Table of Contents (TOC) Sidebar in `StandaloneEpubReader.vue`: [COMPLETED]**
    *   **Objective:** Allow users to view and navigate the book's structure using a collapsible sidebar.
    *   **Component Integration:**
        *   Modify `StandaloneEpubReader.vue` to include a button in the control bar (e.g., using a "list" or "menu" icon) to toggle the TOC sidebar.
        *   Utilize a `Sheet` component from `shadcn-vue` for the sidebar's appearance and behavior.
    *   **Logic (`useEpubReader.ts`):**
        *   After the `book` instance is ready (`book.value.ready`), access `book.value.navigation.toc` to retrieve the table of contents data.
        *   Store this TOC data in a reactive `ref` (e.g., `tableOfContents = ref([])`).
        *   Expose the `tableOfContents` ref from the composable.
        *   Implement and expose a new method `navigateToTocItem(href: string)` that calls `rendition.value.display(href)` to navigate to the selected TOC entry.
    *   **UI (`StandaloneEpubReader.vue`):**
        *   The sidebar should display the `tableOfContents` as a hierarchical list (if applicable, though `epub.js` often provides a flat list or nested `subitems`).
        *   Each TOC item in the list should be clickable.
        *   Clicking a TOC item should call the `navigateToTocItem` method from `useEpubReader` with the corresponding `href`.
        *   Ensure the sidebar is appropriately styled and responsive.
        *   Add state to manage the visibility of the TOC sidebar (e.g., `isTocOpen = ref(false)`).

### Phase 2: Text Selection and Contextual Lookup Integration

5.  **Implement Text Selection and Bubble Menu in `StandaloneEpubReader.vue` (via `useEpubReader.ts`):**
    *   **Event Listener: [COMPLETED]** 
    Continue using `rendition.on('selected', (cfiRange, contents) => { ... })` in `useEpubReader.ts`.
    *   **Data Extraction & State:**
        *   Extract selected text: `const selectedText = contents.window.getSelection()?.toString().trim();`
        *   If `selectedText` is not empty:
            *   Get the `Selection` object: `const selection = contents.window.getSelection();`
            *   Get the `Range`: `const range = selection.getRangeAt(0);`
            *   Get bounding rectangle for position: `const rect = range.getBoundingClientRect();` (These are relative to the iframe).
            *   Store `selectedText` and `rect` (or derived coordinates for the menu) in reactive `ref`s within `useEpubReader.ts` (e.g., `currentSelection = ref({ text: '', rect: null })`).
            *   The composable should expose `currentSelection`.
    *   **Bubble Menu Component (`EpubBubbleMenu.vue`):**
        *   **Action:** Create a new component `pickvocab-client/components/app/epubReader/EpubBubbleMenu.vue`.
        *   **Props:** `selectionData: { text: string, rect: DOMRect } | null`, `iframeElement: HTMLIFrameElement | null`.
        *   **UI:**
            *   The menu should only be visible if `selectionData` is not null and `selectionData.text` is not empty.
            *   It should be styled similarly to the `tiptap-bubble-menu` (e.g., small, floating, with buttons).
            *   Buttons:
                *   "Lookup": Triggers the contextual lookup.
                *   "Copy": Copies the selected text.
                *   (Optional) "Create Card": If direct card creation from selection is desired.
        *   **Positioning:**
            *   The component will need to calculate its absolute position on the page.
            *   It will use `selectionData.rect` (which is relative to the iframe) and the position/offset of the `iframeElement` itself (which the `StandaloneEpubReader.vue` will pass as a prop, likely the iframe used by `epub.js` rendition). The position should be above or below the selection.
        *   **Emits:** `@lookup`, `@copy`, `@create-card`.
    *   **Integration in `StandaloneEpubReader.vue`:**
        *   Import and use `EpubBubbleMenu.vue`.
        *   Pass the `currentSelection` from `useEpubReader` and a `ref` to the `epub.js` iframe to `EpubBubbleMenu.vue`.
        *   Handle `@lookup` event: This will set the data for the `LookupModal.vue` and show it.
        *   Handle `@copy` event: Implement copy functionality.

6.  **Develop/Refine `LookupModal.vue` Component: [COMPLETED]**
    *   **Action:** Create a new component at `pickvocab-client/components/app/epubReader/LookupModal.vue`.
    *   **Modal Framework:** Utilize a `Dialog` component from `shadcn-vue` for the base modal structure.
    *   **Props:**
        *   `word: string` (required)
        *   `context: string` (required)
        *   `offset: number` (required—to mirror `ContextView.vue`)
        *   `open: boolean` (controls visibility via `v-model:open` on the `Dialog`)
    *   **Emits:**
        *   `update:open` (for `v-model:open` binding)
        *   `addCard(wordEntry: WordInContextEntry, callback?: () => void)` (same signature as in `ContextView.vue`)
    *   **Internal State:**
        *   `wordEntry = ref<WordInContextEntry | undefined>()`
        *   `isLoading = ref(false)`
        *   `errorMessage = ref('')`
        *   `isDetailed = ref(false)` (for `<ExplanationView>`)
        *   `selectedSimpleViewLanguage = ref('English')` (for `<ExplanationView>`)
        *   `llmStore = useLLMStore()`
        *   `dictionary = computed(...)` (same as in `ContextView.vue`)
    *   **Logic:**
        *   Rename lookup handler to `handleLookup()` (matching `ContextView.vue`):
            1.  `isLoading.value = true`; clear `wordEntry` and `errorMessage`
            2.  `const { selectedText, text, offset } = props`
            3.  `const reduced = reduceContext(text, selectedText, offset, 3)`
            4.  Call either
                - `await dictionary.value.getMeaningInContext(reduced.selectedText, reduced.text, reduced.offset)`
                - or `getMeaningInContextShort(...)` based on `isDetailed.value`
            5.  Populate `wordEntry.value` or `errorMessage.value`; then `isLoading.value = false`
        *   Mirror `ContextView.vue` event handlers on `<ExplanationView>`:
            *   `@refresh="(language) => refresh(language)"`
            *   `@simpleLookupForLanguage="(language) => simpleLookupForLanguage(language)"`
            *   `@addCard="(entry, cb) => emit('addCard', entry, cb)"`
    *   **UI (within `<DialogContent>`):**
        *   `<Dialog v-model:open="open">` wrapping:
            *   `<DialogTitle>` / `<DialogClose>`
            *   Show `errorMessage` if present
            *   `<ExplanationView
                   :word="word"
                   :wordEntry="wordEntry"
                   :llmModel="llmModel"
                   :isLoading="isLoading"
                   v-model:isDetailed="isDetailed"
                   v-model:selectedSimpleViewLanguage="selectedSimpleViewLanguage"
                   @refresh="refresh"
                   @simpleLookupForLanguage="simpleLookupForLanguage"
                   @addCard="(entry, cb) => emit('addCard', entry, cb)"
               />`
            *   Optional global `<Spinner>` if `isLoading` and no `wordEntry`

7.  **Integrate Bubble Menu Trigger and `LookupModal.vue` in `StandaloneEpubReader.vue`: [COMPLETED]**
    *   **Parent State:**
        *   `lookupOpen = ref(false)`
        *   `lookupWord = ref('')`
        *   `lookupContext = ref('')`
        *   `lookupOffset = ref(0)`
    *   **Bubble Menu Handler (`@lookup`):**
        ```ts
        function handleLookup(event: { selectedText: string; text: string; offset: number }) {
          lookupWord.value    = event.selectedText;
          lookupContext.value = event.text;
          lookupOffset.value  = event.offset;
          lookupOpen.value    = true;
        }
        ```
    *   **Methods to Mirror `ContextView.vue`:**
        *   `simpleLookupForLanguage(language: string)` imported or defined in parent
        *   `refresh(language?: string)` imported or defined in parent
        *   `addCard(wordEntry: WordInContextEntry, callback?: () => void)` same as in `ContextView.vue`
    *   **Template Integration:**
        ```html
        <EpubBubbleMenu
          :selectionData="currentSelection"
          :iframeElement="iframeRef"
          @lookup="handleLookup"
          @copy="handleCopy"
        />
        <LookupModal
          :word="lookupWord"
          :context="lookupContext"
          :offset="lookupOffset"
          v-model:open="lookupOpen"
          @addCard="addCard"
        />
        ```
    *   **Post-Lookup Flow:**
        *   On `@addCard`, parent calls its `addCard(wordEntry, callback)` which
            mirrors ContextView.vue's logic to create a card then navigate.

### Phase 3: Styling, Refinements, and Generalization [PARTIALLY COMPLETED]

8.  **Reader Styling and UX: [COMPLETED]**
    *   Implemented beautiful typography with custom CSS.
    *   Added fixed control bar instead of floating navigation.
    *   Ensured proper full-screen experience with height calculations.
    *   Added font resizing functionality.
    *   Improved navigation (now chapter-based). Page counter is less relevant in scrolled-doc per chapter.

9.  **Transition to Scrolled-Document Flow with Chapter Navigation: [COMPLETED]**
    *   **Goal:** Change the EPUB reader from a paginated view to a continuous scrolling document for each chapter, with navigation controls (buttons, swipes, keyboard) advancing by chapter.
    *   **Logic (`useEpubReader.ts`):**
        *   Modified `epub.js` rendition option `flow` from `'paginated'` to `'scrolled-doc'`.
        *   Replaced `prevPage`/`nextPage` functions with `prevChapter`/`nextChapter` logic, utilizing `book.spine.items` for chapter definition and navigation.
        *   Updated `relocated` and `rendered` event handlers to manage `currentChapterIndex` and ensure the view scrolls to the top of the new chapter.
        *   Adjusted swipe and keyboard navigation to use the new chapter-based functions.
    *   **UI (`StandaloneEpubReader.vue`):**
        *   Updated navigation button `@click` handlers and `title` attributes to reflect chapter navigation.
        *   Modified the `epubContentArea` div to use `overflow-y-auto` to enable scrolling of chapter content.
    *   **Impact:** Reading is now done by scrolling through a chapter, and "next/prev" actions move to the adjacent chapter.

10. **EPUB Content Theme Switcher: [TODO]**
    *   **Goal:** Allow users to toggle between two themes for the EPUB content area only:
        - **Default theme:** White background, #333 text (current)
        - **Rust theme:** #E1E1DB background, #333 text
    *   **UI:**
        - Add a theme toggle button to the navigation control bar (next to navigation/page controls).
        - The button should be visually elegant and clearly indicate the current theme (e.g., label or icon).
    *   **Logic:**
        - Register both themes with epub.js using `rendition.themes.register()`.
        - On toggle, call `rendition.themes.select('rust' | 'default')` to switch the theme for the content iframe.
        - Store the selected theme in `localStorage` so the user's choice persists across reloads.
        - On reader initialization, read the theme from `localStorage` and apply it immediately after registering themes.
    *   **Scope:** Only the EPUB content area (iframe) is themed. The outer UI remains unchanged.
    *   **Accessibility:** Ensure sufficient contrast and clear feedback when toggling themes.
    *   **Testing:** Confirm theme switching works, persists, and does not affect other UI elements.

11. **`LookupModal.vue` Styling and UX: [TODO]**
    *   **Current State:** No dedicated component yet.
    *   **TODO:** Implement `LookupModal.vue` as a `shadcn-vue Dialog`. Styling will primarily involve the modal frame and ensuring `ExplanationView` integrates smoothly within it. Test and refine the overall user experience of the lookup process.

12. **Mobile Responsiveness and Testing: [IN PROGRESS]**
    *   Added responsive containers.
    *   **Implemented `max-width` constraints for optimal reading experience:**
        *   **Rationale:** To prevent excessively long lines of text on wider screens, which can hinder readability and comprehension. The chosen `max-width` values aim for an optimal line length (around 60-75 characters) across various devices. The content is centered using `mx-auto`.
        *   **Breakpoints and Values (applied to the EPUB content container):**
            *   **Default (Mobile < 640px):** `w-full` (utilizes available width, relies on padding for spacing within the content itself if needed).
            *   **Small screens (`sm:`, >= 640px):** `sm:max-w-2xl` (max-width: 42rem / 672px)
            *   **Medium screens (`md:`, >= 768px):** `md:max-w-3xl` (max-width: 48rem / 768px)
            *   **Large screens (`lg:`, >= 1024px):** `lg:max-w-4xl` (max-width: 56rem / 896px)
            *   **Extra-large screens (`xl:`, >= 1280px):** `xl:max-w-5xl` (max-width: 64rem / 1024px)
            *   **2X-large screens (`2xl:`, >= 1536px):** `2xl:max-w-5xl` (capped at 64rem / 1024px to maintain optimal line length).
    *   **TODO:** Full testing on various screen sizes to confirm `max-width` behavior and overall layout.
    *   **TODO:** Further refinements for small screens, ensuring controls and content are easily accessible.

13. **Future Format Support (Placeholder in Plan):**
    *   The `reader.vue` page has logic to determine which specific reader component to instantiate based on `fileType`.
    *   The `accept` attribute is ready for future updates to support more formats.

## Features Implemented Beyond Initial Plan

1. **Font Size Controls:**
   * Added a font size toggle button that cycles between small, medium, and large text.
   * Improves accessibility and reading comfort.

2. **Enhanced Navigation:**
   * Implemented keyboard navigation (arrow keys for chapters).
   * Created a dedicated control bar with intuitive icons (now for chapter navigation).

3.  **Typography & Readability:**
   * Custom CSS styling for book content.
   * Serif font family selection optimized for reading.
   * Proper line height and text spacing.

4. **Context Extraction:**
   * More sophisticated text selection that captures surrounding context.
   * Helper function to extract relevant snippets around selected text.

## Autonomous Actions Taken During Planning

*   Searched web for JavaScript/Vue EPUB reader libraries.
*   Read `pickvocab-web-extension/entrypoints/content/utils.ts` (selection, context, popup UI).
*   Read `pickvocab-web-extension/entrypoints/content.ts` (event triggers).
*   Read `pickvocab-web-extension/entrypoints/content/DefinitionPopup.vue` (lookup API/UI).
*   Read `pickvocab-client/components/app/contextualMeaning/ContextView.vue` (target component).
*   Listed `pickvocab-client/pages/` directories (structure). 