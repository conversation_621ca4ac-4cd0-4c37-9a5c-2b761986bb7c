# EPUB Reader Refactoring Plan

**Goal:** Refactor the `useEpubReader.ts` composable into smaller, more manageable and focused composables to improve maintainability, readability, and separation of concerns.

**Current File:** [`pickvocab-client/components/app/epubReader/useEpubReader.ts`](pickvocab-client/components/app/epubReader/useEpubReader.ts)

## Proposed Structure:

The main `useEpubReader.ts` will act as an orchestrator, initializing and coordinating several new, more specialized composables.

1.  **`useEpubReader.ts` (Main Orchestrator)**
    *   **Responsibilities:**
        *   Initialize and coordinate all sub-composables.
        *   Manage top-level reactive props passed from the component (e.g., `epubDataProp`, `filenameProp`, `epubContentArea`).
        *   Handle the primary lifecycle hooks (`onMounted`, `onUnmounted`) and top-level watchers (e.g., watching `epubDataProp` to re-initialize the reader).
        *   Expose the final aggregated reactive state and methods to the component that consumes `useEpubReader`.
    *   **Dependencies:**
        *   `useEpubCoreLogic.ts`
        *   `useEpubThemeManager.ts`
        *   `useEpubInteractionManager.ts`
        *   `useEpubNavigationHandler.ts`
        *   `useEpubReadingProgress.ts` (existing)

2.  **`useEpubCoreLogic.ts` (Core ePub.js Interaction & Book State)**
    *   **Responsibilities:**
        *   Managing the `Book` and `Rendition` instances from `epubjs`.
        *   Initializing the `Book` instance with EPUB data.
        *   Rendering the book to the specified container (`rendition.renderTo`).
        *   Handling `rendition.hooks.content` for iframe setup (e.g., applying styles, attaching initial listeners if closely tied to content rendering).
        *   Managing the display of EPUB content (`rendition.display()`).
        *   Destroying `Book` and `Rendition` instances.
        *   Handling `rendition.on('relocated')` and `rendition.on('rendered')` events to update current chapter/location information.
        *   Loading and providing `tableOfContents` and `spineItems` from the book.
        *   Tracking `currentChapterIndex`.
    *   **Exposes (examples):** `book`, `rendition`, `tableOfContents`, `spineItems`, `currentChapterIndex`, `isReaderReady` (derived from rendition), `initializeCoreLogic`, `destroyCoreLogic`, `displayCfiOrPath`.

3.  **`useEpubThemeManager.ts` (Theme Management)**
    *   **Responsibilities:**
        *   Defining and managing theme rules (e.g., `themeRules` for 'default', 'rust').
        *   Applying themes to the `rendition` (`rendition.themes.select()`, `rendition.themes.register()`).
        *   Managing `currentTheme` state.
        *   Calculating `currentThemeBackgroundColor`.
        *   Persisting and retrieving theme preference (e.g., using `localStorage`).
    *   **Exposes (examples):** `currentTheme`, `currentThemeBackgroundColor`, `selectTheme`, `initializeThemeManager`.

4.  **`useEpubInteractionManager.ts` (User Interactions & iframe Listeners)**
    *   **Responsibilities:**
        *   Handling text selection events (`rendition.on('selected')`) and managing `currentSelection` state (`SelectionData`).
        *   Managing iframe-specific click listeners (e.g., `handleIframeClick` for clearing selection when clicking outside selected text).
        *   Setting up and managing swipe navigation (using `@vueuse/core` `useSwipe`).
        *   Handling global keydown events for reader-specific actions (like arrow keys for page turns, if not in NavigationHandler).
        *   Managing `actualIframeElement` ref.
    *   **Exposes (examples):** `currentSelection`, `actualIframeElement`, `clearSelection`, `setupInteractionListeners`, `destroyInteractionListeners`.

5.  **`useEpubNavigationHandler.ts` (Chapter and TOC Navigation)**
    *   **Responsibilities:**
        *   Implementing `prevChapter` and `nextChapter` logic using `rendition.prev()` / `rendition.next()` or by displaying specific spine item hrefs.
        *   Implementing `navigateToTocItem` by displaying the target href.
        *   Could also house keyboard navigation logic if deemed more appropriate here than in InteractionManager.
    *   **Exposes (examples):** `prevChapter`, `nextChapter`, `navigateToTocItem`.

6.  **`useEpubReadingProgress.ts` (Existing - Progress Tracking)**
    *   This composable already exists and is responsible for:
        *   Generating `bookId`.
        *   Saving and loading reading progress (CFI, scroll position) to `localStorage`.
        *   Tracking scroll position within the EPUB content iframe.
        *   Debouncing save operations.
    *   It will be utilized by `useEpubReader.ts` or `useEpubCoreLogic.ts` to initialize, destroy, and trigger progress saving/loading.

## Refactoring Steps (High-Level):

1.  **Create New Files:**
    *   `pickvocab-client/components/app/epubReader/useEpubCoreLogic.ts`
    *   `pickvocab-client/components/app/epubReader/useEpubThemeManager.ts`
    *   `pickvocab-client/components/app/epubReader/useEpubInteractionManager.ts`
    *   `pickvocab-client/components/app/epubReader/useEpubNavigationHandler.ts`

2.  **Identify and Move Logic:**
    *   Systematically go through the existing `useEpubReader.ts`.
    *   For each piece of state (refs, computeds), function, and event handler, determine which new composable it belongs to based on the defined responsibilities.
    *   Move the code, ensuring to pass necessary reactive state or props from the orchestrator (`useEpubReader.ts`) to these new composables.
    *   Move relevant type definitions (like `TocItem`, `SelectionData`) to the composables where they are primarily used, or to a shared types file if used across multiple.

3.  **Define Composable Interfaces (Props & Returns):**
    *   For each new composable, clearly define what it accepts as input (e.g., `Ref<Book | null>`, `Ref<Rendition | null>`, `Ref<HTMLDivElement | null>`) and what it returns (exposed reactive state and methods).

4.  **Update `useEpubReader.ts` (Orchestrator):**
    *   Import and instantiate the new composables.
    *   Pass the required reactive state, props, and other dependencies to them.
    *   Aggregate the exposed state and methods from these sub-composables and re-expose them as the public API of `useEpubReader`.
    *   Refactor `initializeReader` and `destroyEpubInstances` to delegate tasks to the corresponding initialization/destruction functions within the sub-composables.

5.  **Manage Dependencies and Imports:**
    *   Ensure all necessary imports (Vue, epubjs, `@vueuse/core`, etc.) are correctly placed in the new files or passed down appropriately.
    *   Update imports in `useEpubReader.ts`.

6.  **Review and Test:**
    *   Thoroughly review the changes for correctness and completeness.
    *   Manually test all aspects of the EPUB reader functionality:
        *   Loading and displaying books.
        *   Theme selection and persistence.
        *   Text selection and clearing selection.
        *   Chapter navigation (TOC, prev/next buttons, swipe, keyboard).
        *   Progress saving and loading.
        *   Resize handling.
        *   Book switching and cleanup.

## Mermaid Diagram of Proposed Structure:

```mermaid
graph TD
    Component --> A[useEpubReader.ts Orchestrator]

    A --> B[useEpubCoreLogic.ts\n(Book, Rendition, Display)]
    A --> C[useEpubThemeManager.ts\n(Themes, Styling)]
    A --> D[useEpubInteractionManager.ts\n(Selection, Swipe, iframe Clicks)]
    A --> E[useEpubNavigationHandler.ts\n(Prev/Next Chapter, TOC Nav)]
    A --> F[useEpubReadingProgress.ts\n(Existing: Progress, Scroll)]

    subgraph EPUB_Instance_Interactions
        B --> G((ePub.js Book & Rendition))
        C --> G
        D --> G
        E --> G
        F --> G
    end

    classDef orchestrator fill:#f9f,stroke:#333,stroke-width:2px;
    classDef composable fill:#bbf,stroke:#333,stroke-width:2px;
    classDef existing fill:#dfd,stroke:#333,stroke-width:2px;
    classDef external fill:#ff9,stroke:#333,stroke-width:2px;

    class A orchestrator;
    class B,C,D,E composable;
    class F existing;
    class G external;
```

This plan aims to create a more modular and maintainable structure for the EPUB reader functionality.