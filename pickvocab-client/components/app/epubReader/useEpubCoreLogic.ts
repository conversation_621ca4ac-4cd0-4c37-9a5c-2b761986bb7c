import { ref, computed, nextTick, type Ref } from 'vue';
import ePub, { Book, Rendition, Contents } from 'epubjs';
import type { Location, NavItem } from 'epubjs'; // Removed Section import

/**
 * Interface for Table of Contents items.
 */
export interface TocItem extends NavItem {
  // NavItem already includes id, href, label, subitems, parent
  // Add any custom properties if needed in the future
}

/**
 * Options for initializing the useEpubCoreLogic composable.
 */
export interface EpubCoreLogicOptions {
  /** Reactive reference to the EPUB data ArrayBuffer. */
  epubDataProp: Ref<ArrayBuffer | null>;
  /** Reactive reference to the HTMLDivElement where the EPUB content will be rendered. */
  epubContentArea: Ref<HTMLDivElement | null>;
  /** Theme rules to be registered with the rendition. */
  themeRules: Record<string, any>; // Consider a more specific type for theme rules

  /** Callback invoked when the EPUB iframe is ready and its contents are loaded. */
  onIframeReady: (
    iframe: HTMLIFrameElement,
    view: Contents // epub.js Contents object, includes window and document
  ) => void;
  /** Callback invoked when the rendition relocates to a new section. */
  onRelocated: (location: Location, renditionInstance: Rendition) => void;
  /** Callback invoked when a new section is rendered. */
  onRendered: (section: { href: string, index: number }, view: Contents) => void;
  /** Callback invoked when the book's navigation (TOC and Spine) is loaded. */
  onBookNavigationLoaded: (toc: TocItem[], spine: any[]) => void; // Changed Section[] to any[]
  /** Callback invoked if book initialization fails. */
  onBookInitFailed?: (error: Error) => void;
}

/**
 * Composable for managing the core ePub.js Book and Rendition instances.
 * It handles the lifecycle of the EPUB rendering, content display,
 * and emits events for other composables to hook into.
 */
export function useEpubCoreLogic(options: EpubCoreLogicOptions) {
  const {
    epubDataProp,
    epubContentArea,
    themeRules,
    onIframeReady,
    onRelocated,
    onRendered,
    onBookNavigationLoaded,
    onBookInitFailed,
  } = options;

  const book = ref<Book | null>(null);
  const rendition = ref<Rendition | null>(null);
  const tableOfContents = ref<TocItem[]>([]);
  const spineItems = ref<any[]>([]); // Changed Section[] to any[]
  const currentChapterIndex = ref(0);
  const actualIframeElement = ref<HTMLIFrameElement | null>(null);

  const isReaderReady = computed(() => !!rendition.value && !!book.value?.spine);

  /**
   * Destroys the current Book and Rendition instances.
   */
  const destroyCore = () => {
    if (book.value) {
      book.value.destroy();
      book.value = null;
    }
    if (rendition.value) {
      rendition.value.destroy();
      rendition.value = null;
    }
    actualIframeElement.value = null;
    tableOfContents.value = [];
    spineItems.value = [];
    currentChapterIndex.value = 0;
  };

  /**
   * Initializes the ePub Book and Rendition instances.
   * Sets up rendering, themes, content hooks, and event listeners.
   */
  const initializeBookAndRendition = async (): Promise<boolean> => {
    if (!epubDataProp.value || !epubContentArea.value) {
      destroyCore();
      return false;
    }

    try {
      book.value = ePub(epubDataProp.value);
      await book.value.ready; // Wait for the book to be parsed

      const container = epubContentArea.value;
      if (!container || container.offsetWidth === 0 || container.offsetHeight === 0) {
        // This check might be too early if container visibility is set later.
        // Consider if this is the best place or if it should be handled by the caller.
        console.warn('EPUB container not ready or has no dimensions.');
        destroyCore();
        onBookInitFailed?.(new Error('EPUB container not ready or has no dimensions.'));
        return false;
      }

      rendition.value = book.value.renderTo(container, {
        width: '100%', // Use '100%' for scrolled-doc, or container.offsetWidth for paginated
        height: '100%',
        spread: 'none',
        flow: 'scrolled-doc', // Ensure this matches desired reading mode
        manager: 'default',
      });

      // Register themes
      if (rendition.value && themeRules) {
        for (const themeName in themeRules) {
          rendition.value.themes.register(themeName, themeRules[themeName]);
        }
        // Note: Actual theme selection will be handled by ThemeManager via orchestrator
      }

      // Setup content hook
      rendition.value.hooks.content.register((contents: Contents) => {
        const doc = contents.document;
        if (doc.documentElement) {
          doc.documentElement.style.height = 'auto';
          doc.documentElement.style.overflowY = 'scroll';
          doc.documentElement.style.setProperty('-webkit-touch-callout', 'none'); // Disable callout on touch
        }
        if (doc.body) {
          doc.body.style.height = 'auto';
          doc.body.style.overflowY = 'visible';
        }
        doc.addEventListener('contextmenu', (e: Event) => e.preventDefault());

        if (contents.window && contents.window.frameElement) {
          const currentIframe = contents.window.frameElement as HTMLIFrameElement;
          actualIframeElement.value = currentIframe;
          onIframeReady(currentIframe, contents);
        }
      });

      // Setup event listeners for rendition
      rendition.value.on('relocated', (location: Location) => {
        if (location.start?.index !== undefined) {
          currentChapterIndex.value = location.start.index;
        }
        if (rendition.value) { // Ensure rendition still exists
            onRelocated(location, rendition.value as Rendition); // Cast for type safety if TS struggles
        }
      });

      rendition.value.on('rendered', (section: { href: string, index: number }, view: Contents) => {
        if (section.index !== undefined && currentChapterIndex.value !== section.index) {
          currentChapterIndex.value = section.index;
        }
        onRendered(section, view);
      });
      
      // Load navigation items (TOC and Spine)
      // This needs to happen after book is ready.
      await book.value.loaded.navigation; // Wait for navigation to be loaded
      tableOfContents.value = book.value.navigation.toc as TocItem[];
      
      // Accessing spine items (epubjs v0.3)
      if (book.value && book.value.spine) {
        await book.value.loaded.spine; // Ensure spine is loaded
        const bookSpine = book.value.spine as any; // Cast to any to bypass TS errors for now
        const numSections = bookSpine.length;
        const tempSpineItems: any[] = []; // Use any for items temporarily
        if (typeof numSections === 'number') { // Check if length is valid
          for (let i = 0; i < numSections; i++) {
            const section = bookSpine.get(i); // Use the casted spine
            if (section) {
              tempSpineItems.push(section);
            }
          }
        }
        spineItems.value = tempSpineItems; // No final cast to Section[] needed now
        if (tempSpineItems.length === 0 && numSections > 0) {
            console.warn('Spine items could not be retrieved using spine.get(), though spine.length suggests sections exist.');
        } else if (tempSpineItems.length === 0) {
            console.warn('No spine items found or retrieved.');
        }
      } else {
        spineItems.value = [];
        console.warn('Book or book.spine not available for populating spineItems.');
      }
      onBookNavigationLoaded(tableOfContents.value, spineItems.value);

      return true;
    } catch (error) {
      console.error('Failed to initialize EPUB Book and Rendition:', error);
      destroyCore();
      onBookInitFailed?.(error as Error);
      return false;
    }
  };

  /**
   * Displays EPUB content, optionally at a specific CFI.
   * @param cfi Optional ePub CFI string to navigate to.
   */
  const displayContent = async (cfi?: string): Promise<void> => {
    if (!rendition.value) {
      console.warn('Rendition not available for displayContent.');
      return;
    }
    try {
      if (cfi) {
        await rendition.value.display(cfi);
      } else {
        await rendition.value.display();
      }
      // Location and chapter index will be updated by 'relocated' or 'rendered' events.
    } catch (error) {
      console.error('Error displaying EPUB content:', error);
      // Attempt to display from the beginning if CFI display failed
      if (cfi && rendition.value) {
        try {
          await rendition.value.display();
        } catch (displayError) {
          console.error('Error displaying EPUB content from beginning after CFI fail:', displayError);
        }
      }
    }
  };

  /**
   * Handles window resize events to adjust the rendition dimensions.
   */
  const handleResize = () => {
    if (!rendition.value || !epubContentArea.value) return;
    const container = epubContentArea.value;
    // Ensure container has valid dimensions before resizing
    if (container.offsetWidth > 0 && container.offsetHeight > 0) {
        rendition.value.resize(container.offsetWidth, container.offsetHeight);
    }
  };

  return {
    book, // Readonly ref to the book instance
    rendition, // Readonly ref to the rendition instance
    tableOfContents, // Readonly ref
    spineItems, // Readonly ref
    currentChapterIndex, // Readonly ref
    actualIframeElement, // Readonly ref, discovered by this composable
    isReaderReady, // Computed
    initializeBookAndRendition,
    destroyCore,
    displayContent,
    handleResize,
  };
}