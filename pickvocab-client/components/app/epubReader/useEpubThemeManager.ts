import { ref, computed, type Ref } from 'vue';
import type { Rendition } from 'epubjs';

/**
 * Defines the structure for theme rules.
 * Each key is a CSS selector, and the value is an object of CSS properties.
 */
export interface ThemeRules {
  [selector: string]: Record<string, string>;
}

/**
 * Defines the structure for a collection of themes.
 * Each key is a theme name (e.g., 'default', 'rust'), and the value is its ThemeRules.
 */
export interface ThemesCollection {
  [themeName: string]: ThemeRules;
}

/**
 * Options for initializing the useEpubThemeManager composable.
 */
export interface EpubThemeManagerOptions {
  /** Reactive reference to the ePub.js Rendition instance. */
  rendition: Ref<Rendition | null>;
  /** Reactive reference to the actual iframe element of the EPUB content. */
  actualIframeElement: Ref<HTMLIFrameElement | null>;
  /** The collection of theme definitions. */
  definedThemes: ThemesCollection;
  /** The default theme name to use if none is stored. */
  defaultThemeName?: 'default' | 'rust' | string; // Allow string for extensibility
  /** Storage key for persisting the selected theme. */
  themeStorageKey?: string;
}

/**
 * Composable for managing EPUB themes, including applying themes,
 * persisting selection, and providing theme-related reactive state.
 */
export function useEpubThemeManager(options: EpubThemeManagerOptions) {
  const {
    rendition,
    actualIframeElement,
    definedThemes,
    defaultThemeName = 'default',
    themeStorageKey = 'epubTheme',
  } = options;

  const getInitialTheme = (): string => {
    if (typeof localStorage !== 'undefined') {
      return localStorage.getItem(themeStorageKey) || defaultThemeName;
    }
    return defaultThemeName;
  };

  const currentTheme = ref<string>(getInitialTheme());

  const themeBackgroundColors: Record<string, string> = {};
  for (const themeName in definedThemes) {
    // Attempt to find a body background color, default to white
    const bodyRule = definedThemes[themeName]['body'] || definedThemes[themeName]['body, body.' + themeName + 'Theme'];
    themeBackgroundColors[themeName] = bodyRule?.['background-color']?.replace(' !important', '') || '#FFFFFF';
  }

  const currentThemeBackgroundColor = computed(() => {
    return themeBackgroundColors[currentTheme.value] || themeBackgroundColors[defaultThemeName] || '#FFFFFF';
  });

  /**
   * Registers all defined themes with the ePub.js rendition.
   * This should be called after the rendition is initialized.
   */
  const registerThemes = () => {
    if (!rendition.value) return;
    for (const themeName in definedThemes) {
      rendition.value.themes.register(themeName, definedThemes[themeName]);
    }
  };

  /**
   * Selects and applies a theme to the EPUB rendition.
   * Persists the selected theme to localStorage.
   * @param themeName The name of the theme to select.
   */
  const selectTheme = (themeName: string) => {
    if (!rendition.value || !definedThemes[themeName]) {
      console.warn(`Theme "${themeName}" not found or rendition not available.`);
      return;
    }

    const oldThemeName = currentTheme.value;
    currentTheme.value = themeName;

    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(themeStorageKey, themeName);
    }

    // It's crucial to save the current CFI *before* changing themes,
    // as theme changes can sometimes cause reflows that alter the CFI.
    const currentCfi = rendition.value.location?.start?.cfi;

    try {
      // epub.js themes can be tricky to reset fully.
      // A common approach is to register a blank theme or re-register.
      // For simplicity, we'll rely on epub.js's select mechanism.
      // If issues arise, more aggressive reset (unregistering/re-registering) might be needed.
      
      // Ensure all themes are registered before selecting
      registerThemes(); // Re-registering might be necessary if themes were cleared or rendition reset

      rendition.value.themes.select(themeName);

      // Add/remove body classes for additional CSS specificity if needed
      if (actualIframeElement.value && actualIframeElement.value.contentDocument?.body) {
        const body = actualIframeElement.value.contentDocument.body;
        if (oldThemeName && definedThemes[oldThemeName]) {
            body.classList.remove(`${oldThemeName}Theme`);
        }
        body.classList.add(`${themeName}Theme`);
      }
    } catch (e) {
      console.error('Error applying theme:', e);
    }

    // Re-display content at the saved CFI to ensure theme is fully applied
    // and to counteract potential reflow issues.
    if (currentCfi && rendition.value) {
      // A short delay can sometimes help ensure the theme CSS has been processed.
      setTimeout(() => {
        if (rendition.value) { // Check again as rendition might be destroyed
          rendition.value.display(currentCfi);
        }
      }, 50); // Adjust delay as needed, or remove if not necessary
    } else if (rendition.value) {
        // If no CFI, just re-display current location (which might be start)
        rendition.value.display();
    }
  };
  
  /**
   * Initializes the theme manager by registering themes and applying the current theme.
   * Should be called after the rendition is available.
   */
  const initializeThemeManager = () => {
    if (!rendition.value) {
        console.warn("Rendition not available for theme initialization.");
        return;
    }
    registerThemes();
    // selectTheme will also apply the theme to the rendition and iframe body
    selectTheme(currentTheme.value);
  };


  return {
    currentTheme, // Readonly ref
    currentThemeBackgroundColor, // Computed
    selectTheme,
    initializeThemeManager,
    registerThemes, // Expose if manual re-registration is needed
  };
}