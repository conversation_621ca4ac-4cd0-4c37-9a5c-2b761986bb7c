# Plan: Recent Books Feature with File System Access API & Dexie.js

**I. Core Goal:**
Implement a "Recent Books" feature allowing users to quickly re-open EPUB files. This will utilize the File System Access API to obtain persistent file handles and Dexie.js to manage these handles and associated metadata in IndexedDB, aiming for a seamless re-opening experience.

**II. Key Technologies:**
*   **File System Access API:** For initial file selection (`window.showOpenFilePicker()`) and obtaining `FileSystemFileHandle` objects.
*   **Dexie.js:** A wrapper for IndexedDB to simplify database operations for storing file handles and metadata.
*   **Nuxt 3 / Vue 3:** Existing application framework.

**III. Data Structure & IndexedDB Schema (via Dexie.js):**
1.  **Database Name:** `pickvocabAppDB` (or a more specific name like `pickvocabEpubReaderDB`)
2.  **Object Store Name:** `recentBooks`
3.  **Interface for Stored Entries:**
    ```typescript
    // To be defined, likely in a new types file or within the new composable
    export interface RecentBookHandleEntry {
      id: string; // Primary Key: bookId generated by useEpubReadingProgress.ts
      fileHandle: FileSystemFileHandle; // The actual file handle
      filename: string; // Original filename (e.g., "my-book.epub")
      title?: string; // Book title from EPUB metadata
      // coverImage?: string; // Optional: Base64 data URL for a cover image
      lastOpened: number; // Timestamp, for sorting (will be an index)
      // progressCfi?: string; // Optional: Last known reading progress CFI
    }
    ```
4.  **Dexie.js Database Definition (e.g., in `useRecentBooksStorage.ts`):**
    ```typescript
    import Dexie, { type Table } from 'dexie';

    export class RecentBooksDB extends Dexie {
      recentBooks!: Table<RecentBookHandleEntry, string>; // string is the type of the primary key 'id'

      constructor() {
        super('pickvocabAppDB'); // Database name
        this.version(1).stores({
          // 'id' is primary key, 'lastOpened' is an index for sorting
          recentBooks: 'id, lastOpened, filename'
        });
      }
    }
    export const db = new RecentBooksDB();
    ```

**IV. New Composable: `useRecentBooks.ts` (name TBC)**
*   **Responsibilities:**
    *   Encapsulate all Dexie.js interactions for the `recentBooks` store.
    *   Provide reactive state for the list of recent books.
    *   **Functions:**
        *   `addOrUpdateRecentBook(entry: Omit<RecentBookHandleEntry, 'lastOpened'> & { lastOpened?: number }): Promise<string | void>`: Adds a new book or updates `lastOpened` if it exists. Handles pruning if list exceeds max size.
        *   `fetchRecentBooks(limit: number = 10): Promise<void>`: Fetches books, sorts by `lastOpened` descending, updates reactive state.
        *   `getRecentBookById(id: string): Promise<RecentBookHandleEntry | undefined>`
        *   `removeRecentBook(id: string): Promise<void>`
        *   `clearAllRecentBooks(): Promise<void>` (Optional)
    *   **Reactive State:**
        *   `recentBooksList: Ref<RecentBookHandleEntry[]> = ref([])`
        *   `isLoading: Ref<boolean> = ref(false)`
        *   `error: Ref<string | null> = ref(null)`
*   **Dependencies:** Dexie.js.

**V. Modifying Initial File Opening (in `pickvocab-client/pages/app/reader.vue` or its setup logic):**
1.  **Replace `<input type="file">`:**
    *   The "Open Book" button will now trigger a method that calls:
        ```javascript
        async function openFileWithPicker() {
          if (!('showOpenFilePicker' in window)) {
            alert('File System Access API is not supported in this browser. Please use a modern browser.');
            // Optionally, fall back to <input type="file"> here, but that complicates recent book logic.
            return null;
          }
          try {
            const [handle] = await window.showOpenFilePicker({
              types: [{ description: 'EPUB Files', accept: { 'application/epub+zip': ['.epub'] } }],
              multiple: false,
            });
            return handle;
          } catch (err) {
            if ((err as DOMException).name === 'AbortError') {
              console.log('User cancelled file picker.');
            } else {
              console.error('Error picking file:', err);
            }
            return null;
          }
        }
        ```
// Note: If using TypeScript, install @types/wicg-file-system-access for File System Access API type definitions:
    // pnpm add -D @types/wicg-file-system-access
2.  **Processing the Selected File:**
    *   If a `FileSystemFileHandle` (`handle`) is obtained:
        *   Get the `File` object: `const file = await handle.getFile();`
        *   Read `file` as `ArrayBuffer`: `const arrayBuffer = await file.arrayBuffer();`
        *   Pass `arrayBuffer` and `file.name` to the `useEpubReader` composable (via props `epubDataProp` and `filenameProp`).
        *   **Crucially, also pass the `handle: FileSystemFileHandle` and `file.name` to a new mechanism (likely within `useEpubReader` or orchestrated by `reader.vue`) that will eventually call `useRecentBooks.addOrUpdateRecentBook()` once the `bookId` is generated.**

**VI. Integrating with `useEpubReader.ts` and `useEpubReadingProgress.ts`:**
1.  **`useEpubReader.ts`:**
    *   After `initializeReader()` successfully loads the book and `useEpubReadingProgress.generateBookId()` provides the `bookId`:
    *   It needs access to the `FileSystemFileHandle` and the book's `title` (from `coreLogic.book.value.packaging.metadata.title`).
    *   It will then call `useRecentBooks.addOrUpdateRecentBook()` with all necessary details.
    *   This implies `useEpubReader` might need to accept the `FileSystemFileHandle` as part of its options or have a method called by `reader.vue` post-initialization.
2.  **`useEpubReadingProgress.ts`:**
    *   `generateBookId` function remains largely the same as it operates on `filenameProp` and `epubDataProp` (the `ArrayBuffer`). `useEpubReader` will ensure these are correctly populated before calling `generateBookId`.

**VII. New Component: `RecentBooksDisplay.vue` (or similar)**
*   **Location:** `pickvocab-client/components/app/epubReader/RecentBooksDisplay.vue`
*   **Props:**
    *   `onSelectBook: (bookId: string) => void` (emits event or calls prop when a book is chosen)
*   **Logic:**
    *   Injects or instantiates `useRecentBooks`.
    *   Calls `fetchRecentBooks()` on mount and potentially when books are added/removed.
    *   Displays `recentBooksList.value` (e.g., `filename`, `title`, formatted `lastOpened`).
    *   Handles click events on list items, calling `props.onSelectBook(entry.id)`.
    *   Includes UI for removing an entry (calling `useRecentBooks.removeRecentBook(id)` and then re-fetching).
*   **Integration:** This component will be used within `StandaloneEpubReader.vue` or `reader.vue`, likely toggled by a button and displayed in a `Sheet` or `Dialog`.

**VIII. Re-opening a Book from the Recent List (orchestrated by `reader.vue` or `StandaloneEpubReader.vue`):**
1.  `RecentBooksDisplay.vue` emits/calls `onSelectBook(bookId)`.
2.  The parent component receives `bookId`.
3.  It calls `useRecentBooks.getRecentBookById(bookId)` to retrieve the `RecentBookHandleEntry`.
4.  If the entry and `fileHandle` are found:
    *   **Verify & Request Permission:**
        ```javascript
        async function getFileFromHandle(handle: FileSystemFileHandle): Promise<File | null> {
          try {
            if (await handle.queryPermission({ mode: 'read' }) !== 'granted') {
              if (await handle.requestPermission({ mode: 'read' }) !== 'granted') {
                console.warn('Read permission denied for file handle.');
                // Notify user, potentially remove from recent list or mark as needing re-selection.
                return null;
              }
            }
            return await handle.getFile();
          } catch (error) {
            console.error('Error accessing file from handle:', error);
            // Notify user, file might be moved/deleted. Remove from recent list.
            await useRecentBooks.removeRecentBook(entry.id); // Use entry.id (bookId) as it's the primary key
            return null;
          }
        }
        const file = await getFileFromHandle(entry.fileHandle);
        ```
    *   If `file` is obtained:
        *   Read as `ArrayBuffer`: `const arrayBuffer = await file.arrayBuffer();`
        *   Update `epubDataProp` and `filenameProp` for `useEpubReader` to load the book.
        *   Call `useRecentBooks.addOrUpdateRecentBook({...entry, lastOpened: Date.now()})` to update the timestamp.
    *   If `file` is `null` (permission denied or error): Handle appropriately (e.g., show error to user, remove stale entry from recent list).

**IX. UI/UX Considerations:**
*   **Permissions:** Browser prompts for file access and potentially persistent permissions. The app should handle granted/denied states gracefully.
*   **Error Handling:**
    *   File System Access API not supported.
    *   User cancels file picker.
    *   Permission denied for a stored handle.
    *   File handle invalid (file moved/deleted).
*   **Recent Books List:**
    *   Clear indication of which book is which (title, filename).
    *   Easy removal of entries.
    *   Loading state while fetching from DB.
    *   Empty state message.
*   **Max List Size:** Silently enforced.

**X. Implementation Steps (High-Level):**
1.  **Setup Dexie.js:** Add dependency, define `RecentBooksDB` and `RecentBookHandleEntry` interface.
2.  **Develop `useRecentBooks.ts`:** Implement core DB logic (add, fetch, remove, update timestamp).
3.  **Modify File Opening:** Change `reader.vue` to use `showOpenFilePicker()`.
4.  **Integrate `useEpubReader` with `useRecentBooks`:** Ensure `addOrUpdateRecentBook` is called with the `FileSystemFileHandle` after a new book is opened and `bookId` generated.
5.  **Develop `RecentBooksDisplay.vue`:** UI for listing and interacting with recent books.
6.  **Implement Re-opening Logic:** Handle selection from `RecentBooksDisplay.vue`, permission checks, and loading the book via `useEpubReader`.
7.  **Styling and UX Refinements:** Ensure all new UI elements are consistent and user-friendly.
8.  **Thorough Testing:** Cover different scenarios (first open, re-open, permission granted/denied, file moved, API not supported if fallback is implemented).

**XI. Fallback (Optional but Recommended for Robustness):**
*   If `showOpenFilePicker` is not available, the "Open Book" button could revert to using `<input type="file">`.
*   In this fallback mode, the "Recent Books" feature would operate differently:
    *   It would *not* store file handles.
    *   Selecting a recent book would require the user to re-select the file via `<input type="file">`.
    *   Verification would happen by comparing the `bookId` of the newly selected file with the stored `bookId` of the recent entry.
*   This adds conditional logic throughout the feature. For a first pass, we might assume modern browser support for the File System Access API.