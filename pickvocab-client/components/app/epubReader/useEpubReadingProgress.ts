import { ref, type Ref } from 'vue';
// Types for Book and Rendition will be 'any' in the options to avoid structural type conflicts
// import type { Book as EpubBookType, Rendition as EpubRenditionType } from 'epubjs'; // No longer needed here

export interface ReadingProgress {
  cfi: string;
  lastUpdated: number;
}

export const PARTIAL_HASH_SIZE = 64 * 1024; // 64KB for partial content hash

interface EpubReadingProgressOptions {
  book: Ref<any>; // Use Ref<any> to bypass strict structural checking for this prop
  rendition: Ref<any>; // Use Ref<any> to bypass strict structural checking for this prop
  filenameProp: Ref<string | null>;
  epubDataProp: Ref<ArrayBuffer | null>;
  actualIframeElement: Ref<HTMLIFrameElement | null>;
}

// Simple debounce function
function _debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;
  return ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
      timeoutId = null;
    }, delay);
  }) as T;
}

export function useEpubReadingProgress({
  book,
  rendition,
  filenameProp,
  epubDataProp,
  actualIframeElement,
}: EpubReadingProgressOptions) {
  const bookId = ref<string | null>(null);
  let debouncedSaveProgressInternal: (() => void) | null = null;
  let iframeScrollListenerHandle: (() => void) | null = null;

  async function _sha256(buffer: ArrayBuffer): Promise<string> {
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  const generateBookId = async (): Promise<string | null> => {
    if (!filenameProp.value || !epubDataProp.value || !book.value || !book.value.packaging) {
      bookId.value = null;
      return null;
    }
    try {
      // Ensure book is ready before accessing packaging details
      if (book.value.packaging) { // Already checked but good for safety
        const filename = filenameProp.value;
        const epubMetaIdentifier = book.value.packaging.metadata.identifier || 'no-epub-id';
        const partialBuffer = epubDataProp.value.slice(0, PARTIAL_HASH_SIZE);
        const partialHash = await _sha256(partialBuffer);
        const newBookId = `pickvocab-epub-progress::${filename}::${epubMetaIdentifier}::${partialHash}`;
        bookId.value = newBookId;
        return newBookId;
      } else {
        // This case should ideally not be reached if book.value.ready was awaited before calling this
        bookId.value = null;
        return null;
      }
    } catch (error) {
      bookId.value = null;
      return null;
    }
  };

  const saveProgress = (trigger: string = 'unknown') => {
    if (!rendition.value || !bookId.value) {
      return;
    }
    const currentLocation = rendition.value.currentLocation();
    if (currentLocation && (currentLocation as any).start && (currentLocation as any).start.cfi) {
      const cfi = (currentLocation as any).start.cfi;
      const progressData: ReadingProgress = {
        cfi: cfi,
        lastUpdated: Date.now(),
      };
      try {
        localStorage.setItem(bookId.value, JSON.stringify(progressData));
      } catch (e) { /* console.error('Failed to save progress:', e); */ }
    }
  };

  const loadProgress = (): ReadingProgress | null => {
    if (!bookId.value) {
      return null;
    }
    try {
      const saved = localStorage.getItem(bookId.value);
      if (saved) {
        return JSON.parse(saved) as ReadingProgress;
      }
    } catch (e) { /* console.error('Failed to load progress:', e); */ }
    return null;
  };

  const handleIframeScroll = () => {
    if (actualIframeElement.value?.contentWindow) {
      if (debouncedSaveProgressInternal) {
        debouncedSaveProgressInternal();
      }
    }
  };

  const reinitializeScrollListener = () => {
    // Clear existing listener first
    if (iframeScrollListenerHandle) {
      iframeScrollListenerHandle();
      iframeScrollListenerHandle = null;
    }
    if (!actualIframeElement.value?.contentWindow) {
      return;
    }
    const contentWin = actualIframeElement.value.contentWindow;
    contentWin.addEventListener('scroll', handleIframeScroll as EventListener, true);
    iframeScrollListenerHandle = () => {
      contentWin.removeEventListener('scroll', handleIframeScroll as EventListener, true);
    };
  };

  const clearScrollListener = () => {
    if (iframeScrollListenerHandle) {
      iframeScrollListenerHandle();
    }
    iframeScrollListenerHandle = null;
  };

  const initializeProgressTracking = () => {
    debouncedSaveProgressInternal = _debounce(() => {
      if (actualIframeElement.value?.contentWindow) {
        saveProgress('debouncedScroll');
      }
    }, 750);
    // Scroll listener will be added via reinitializeScrollListener by the parent composable
  };

  const destroyProgressTracking = () => {
    if (debouncedSaveProgressInternal) {
      saveProgress('destroyProgressTracking'); // Save one last time
    }
    clearScrollListener();
    debouncedSaveProgressInternal = null;
    bookId.value = null; // Reset bookId
  };

  return {
    bookId,
    generateBookId,
    saveProgress,
    loadProgress,
    initializeProgressTracking,
    destroyProgressTracking,
    reinitializeScrollListener,
    clearScrollListener,
  };
}