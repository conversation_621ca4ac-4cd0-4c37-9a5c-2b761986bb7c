import { ref, computed, type Ref, nextTick } from 'vue';
import type { Rendition, Book } from 'epubjs';
// Assuming TocItem is defined in useEpubCoreLogic or a shared types file.
// If not, it needs to be defined or imported here.
// For now, let's assume it's available or define a basic one.
interface TocItemBasic {
  id: string;
  href: string;
  label: string;
  subitems?: TocItemBasic[];
  parent?: string;
}

/**
 * Options for initializing the useEpubNavigationHandler composable.
 */
export interface EpubNavigationHandlerOptions {
  /** Reactive reference to the ePub.js Rendition instance. */
  rendition: Ref<Rendition | null>;
  /** Reactive reference to the ePub.js Book instance. */
  book: Ref<Book | null>;
  /** Reactive reference to the current chapter index (from core logic). */
  currentChapterIndex: Ref<number>;
  /** Reactive reference to the array of spine items (from core logic). */
  spineItems: Ref<any[]>; // Using any[] due to persistent Section type issues
  /** Reactive reference to the HTMLDivElement containing the EPUB content area. */
  epubContentArea: Ref<HTMLDivElement | null>;
}

/**
 * Composable for managing EPUB navigation, including chapter changes
 * and navigating via Table of Contents.
 */
export function useEpubNavigationHandler(options: EpubNavigationHandlerOptions) {
  const { rendition, book, currentChapterIndex, spineItems, epubContentArea } = options;

  const isNavigating = ref(false); // To prevent rapid/overlapping navigation calls

  /**
   * Navigates to the previous chapter.
   * Includes a visual transition for the content area.
   */
  const prevChapter = async () => {
    if (!rendition.value || !book.value || spineItems.value.length === 0 || isNavigating.value) return;

    const prevIndex = currentChapterIndex.value - 1;
    if (prevIndex >= 0) {
      const prevChapterHref = spineItems.value[prevIndex]?.href;
      if (!prevChapterHref) {
        console.warn('Previous chapter HRef not found for index:', prevIndex);
        return;
      }

      isNavigating.value = true;
      if (epubContentArea.value) {
        epubContentArea.value.style.transition = 'opacity 0.1s ease-out';
        epubContentArea.value.style.opacity = '0';
        await new Promise(resolve => setTimeout(resolve, 100)); // Wait for opacity transition
      }

      try {
        await rendition.value.display(prevChapterHref);
        // currentChapterIndex should be updated by 'relocated' or 'rendered' events
        // from useEpubCoreLogic.
      } catch (error) {
        console.error('Error navigating to previous chapter:', error);
      } finally {
        if (epubContentArea.value) {
          // Ensure opacity is restored even if display fails, or after successful display
          await nextTick(); // Allow DOM updates from display()
          epubContentArea.value.style.transition = 'opacity 0.1s ease-in';
          epubContentArea.value.style.opacity = '1';
        }
        isNavigating.value = false;
      }
    }
  };

  /**
   * Navigates to the next chapter.
   * Includes a visual transition for the content area.
   */
  const nextChapter = async () => {
    if (!rendition.value || !book.value || spineItems.value.length === 0 || isNavigating.value) return;

    const nextIndex = currentChapterIndex.value + 1;
    if (nextIndex < spineItems.value.length) {
      const nextChapterHref = spineItems.value[nextIndex]?.href;
      if (!nextChapterHref) {
        console.warn('Next chapter HRef not found for index:', nextIndex);
        return;
      }
      
      isNavigating.value = true;
      if (epubContentArea.value) {
        epubContentArea.value.style.transition = 'opacity 0.1s ease-out';
        epubContentArea.value.style.opacity = '0';
        await new Promise(resolve => setTimeout(resolve, 100)); // Wait for opacity transition
      }

      try {
        await rendition.value.display(nextChapterHref);
        // currentChapterIndex should be updated by 'relocated' or 'rendered' events.
      } catch (error) {
        console.error('Error navigating to next chapter:', error);
      } finally {
        if (epubContentArea.value) {
          await nextTick();
          epubContentArea.value.style.transition = 'opacity 0.1s ease-in';
          epubContentArea.value.style.opacity = '1';
        }
        isNavigating.value = false;
      }
    }
  };

  /**
   * Navigates to a specific item in the Table of Contents.
   * @param href The HRef of the TOC item to navigate to.
   */
  const navigateToTocItem = async (href: string) => {
    if (!rendition.value || isNavigating.value) return;
    
    isNavigating.value = true;
    // Optional: Add opacity transition for TOC navigation as well
    if (epubContentArea.value) {
        epubContentArea.value.style.transition = 'opacity 0.1s ease-out';
        epubContentArea.value.style.opacity = '0';
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    try {
      await rendition.value.display(href);
    } catch (error) {
      console.error('Error navigating to TOC item:', href, error);
    } finally {
        if (epubContentArea.value) {
            await nextTick();
            epubContentArea.value.style.transition = 'opacity 0.1s ease-in';
            epubContentArea.value.style.opacity = '1';
        }
        isNavigating.value = false;
    }
  };

  /**
   * Handles global keydown events for navigation (ArrowLeft, ArrowRight).
   * @param e The KeyboardEvent.
   */
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!rendition.value || !book.value) return; // Ensure reader is active

    switch (e.key) {
      case 'ArrowLeft':
        prevChapter();
        break;
      case 'ArrowRight':
        nextChapter();
        break;
    }
  };
  
  /**
   * Initializes navigation event listeners (e.g., global keydown).
   */
  const initializeNavigationListeners = () => {
    window.addEventListener('keydown', handleKeyDown);
  };

  /**
   * Destroys navigation event listeners.
   */
  const destroyNavigationListeners = () => {
    window.removeEventListener('keydown', handleKeyDown);
  };


  return {
    prevChapter,
    nextChapter,
    navigateToTocItem,
    isNavigating, // Expose if parent component needs to know navigation state
    initializeNavigationListeners,
    destroyNavigationListeners,
    // handleKeyDown could be exposed if manual triggering is needed, but typically internal
  };
}