import Dexie, { type Table } from 'dexie';

export interface RecentBookHandleEntry {
  id: string; // Primary Key: bookId generated by useEpubReadingProgress.ts
  fileHandle: FileSystemFileHandle; // The actual file handle
  filename: string; // Original filename (e.g., "my-book.epub")
  title?: string; // Book title from EPUB metadata
  coverImage?: string; // URL or base64 string for the cover image
  lastOpened: number; // Timestamp, for sorting (will be an index)
}

export class RecentBooksDB extends Dexie {
  recentBooks!: Table<RecentBookHandleEntry, string>; // string is the type of the primary key 'id'

  constructor() {
    super('pickvocabAppDB'); // Database name
    this.version(2).stores({ // Incremented version number
      recentBooks: 'id, lastOpened, filename, coverImage' // 'id' is primary key, 'lastOpened', 'filename', 'coverImage' are indices
    }).upgrade(tx => {
      // Example upgrade path, if needed. For adding a new optional field,
      // <PERSON><PERSON> handles it gracefully if no specific data migration is required.
      // If you had to transform data from v1 to v2, you'd do it here.
      // For simply adding 'coverImage', existing entries will have it as undefined.
      console.log("Upgrading recentBooks table to version 2, adding coverImage field.");
      // No explicit data migration needed for adding an optional field.
      // Dexie will add the new indexed field to new/updated records.
      // Old records won't have it until they are updated.
      return tx.table("recentBooks").toCollection().modify(book => {
        if (book.coverImage === undefined) {
          // @ts-ignore
          book.coverImage = null; // Or undefined, depending on preference for non-existent covers
        }
      });
    });
    // Fallback for version 1 if needed, or handle upgrades.
    // For simplicity, this example directly uses version 2.
    // If you need to maintain version 1 schema for some reason,
    // you might need a more complex versioning strategy.
    // However, for adding an optional field, upgrading is usually best.
  }
}
export const db = new RecentBooksDB();

// Helper function to get recent book by ID
export async function getRecentBookById(id: string) {
  return db.recentBooks.get(id);
}