<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, computed } from 'vue';
import { useRecentBooks } from '~/components/app/epubReader/useRecentBooks';
import { Button } from '@/components/ui/button';
// @ts-ignore
import IconBook2 from '@tabler/icons-vue/dist/esm/icons/IconBook2.mjs';
// @ts-ignore
import IconX from '@tabler/icons-vue/dist/esm/icons/IconX.mjs';
import { isMobile } from 'is-mobile';

const emit = defineEmits<{
  (e: 'select-book', bookId: string): void
}>();

const { recentBooksList, isLoading, error, fetchRecentBooks, removeRecentBook } = useRecentBooks();

onMounted(() => {
  fetchRecentBooks(12); // Fetch more books for a grid display
});

const handleSelectBook = (bookId: string) => {
  emit('select-book', bookId);
};

const handleRemoveBook = async (bookId: string) => {
  // Consider a less intrusive confirmation, or remove if not strictly necessary for this UI
  // if (confirm('Are you sure you want to remove this book from recent items?')) {
    try {
      await removeRecentBook(bookId);
    } catch (e) {
      console.error('Failed to remove book:', e);
      // TODO: User-facing error
    }
  // }
};

// Long-press logic for mobile
const longPressedBookId = ref<string | null>(null);
let longPressTimeout: ReturnType<typeof setTimeout> | null = null;

const handleTouchStart = (bookId: string) => {
  longPressTimeout = setTimeout(() => {
    longPressedBookId.value = bookId;
  }, 600); // 600ms for long press
};

const handleTouchEnd = () => {
  if (longPressTimeout) {
    clearTimeout(longPressTimeout);
    longPressTimeout = null;
  }
};

const handleGlobalTouch = (e: TouchEvent) => {
  // If the tap is outside any book card, hide the remove button
  if (!(e.target as HTMLElement).closest('.book-card')) {
    longPressedBookId.value = null;
  }
};

onMounted(() => {
  document.addEventListener('touchstart', handleGlobalTouch);
});
onBeforeUnmount(() => {
  document.removeEventListener('touchstart', handleGlobalTouch);
});
</script>

<template>
  <div class="w-full">
    <div v-if="isLoading" class="flex justify-center items-center h-64">
      <p class="text-muted-foreground">Loading recent books...</p>
      <!-- You can add a spinner here -->
    </div>
    <div v-else-if="error" class="p-6 text-center text-destructive bg-destructive/10 border border-destructive rounded-lg">
      <p class="font-semibold">Error loading recent books:</p>
      <p class="text-sm">{{ error }}</p>
    </div>
    <div v-else-if="recentBooksList.length > 0" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 p-1">
      <div
        v-for="book in recentBooksList"
        :key="book.id"
        @click="handleSelectBook(book.id)"
        @touchstart="handleTouchStart(book.id)"
        @touchend="handleTouchEnd"
        @touchmove="handleTouchEnd"
        @contextmenu.prevent
        class="book-card bg-card p-4 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 ease-in-out cursor-pointer group relative flex flex-col"
      >
        <button
          v-if="longPressedBookId === book.id || !isMobile()"
          @click.stop="handleRemoveBook(book.id)"
          class="absolute top-2 right-2
                 opacity-100 sm:opacity-0 sm:group-hover:opacity-100
                 transition-opacity bg-white/80 text-red-600 hover:text-red-800 rounded-full p-1 shadow"
          aria-label="Remove book"
        >
          <IconX class="w-4 h-4" />
        </button>
        
        <div class="aspect-[2/3] w-full bg-muted rounded-md flex items-center justify-center text-muted-foreground mb-3 overflow-hidden">
          <img v-if="book.coverImage" :src="book.coverImage" :alt="`Cover for ${book.title || book.filename}`" class="w-full h-full object-cover" />
          <IconBook2 v-else class="w-16 h-16 opacity-50" />
        </div>
        
        <div class="flex flex-col flex-grow">
          <strong class="text-base font-semibold mt-1 block truncate group-hover:text-primary transition-colors">{{ book.title || book.filename }}</strong>
          <p class="text-xs text-muted-foreground mt-auto pt-2">Last opened: {{ new Date(book.lastOpened).toLocaleDateString() }}</p>
        </div>
      </div>
    </div>
    <div v-else class="py-12 text-center">
      <IconBook2 class="w-16 h-16 text-muted-foreground mx-auto mb-4 opacity-30" />
      <h3 class="text-xl font-semibold text-muted-foreground mb-2">No recent books</h3>
      <p class="text-muted-foreground">Start reading by opening an EPUB file.</p>
    </div>
  </div>
</template>

<style scoped>
/* Minimal scoped styles, relying mostly on Tailwind */
.group:hover .group-hover\\:text-primary {
  color: hsl(var(--primary)); /* Ensure primary color is applied on hover */
}
</style>