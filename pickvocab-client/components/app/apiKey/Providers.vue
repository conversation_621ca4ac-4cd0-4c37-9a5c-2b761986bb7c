<script setup lang="ts">
const llmStore = useLLMStore();
const emit = defineEmits(["close"]);

async function close() {
  emit("close");
}
</script>

<template>
  <form class="p-4 md:p-5 mt-2 flex flex-col justify-between flex-grow">
    <div class="grid gap-4 mb-4 grid-cols-2 max-h-[320px] overflow-y-auto">
      <div class="col-span-2" v-for="provider in llmStore.providers">
        <div v-if="!llmStore.providerMap[provider.name].hidden">
          <div class="inline-block mb-2">
            <label
              for="name"
              class="text-sm font-medium text-gray-600 dark:text-white"
              >{{ provider.name }} API Key&nbsp;</label
            >
            <a
              :href="llmStore.providerMap[provider.name].apiKeyUrl"
              target="_blank"
              class="text-blue-600 dark:text-blue-500 hover:underline text-sm"
              >(Get API Key here)</a
            >
          </div>
          <div class="flex items-center">
            <img
              :src="llmStore.providerMap[provider.name].logo"
              :alt="provider.name"
              class="w-5 h-5"
            />
            <input
              type="text"
              name="name"
              id="name"
              class="ml-2 bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
              :placeholder="llmStore.providerMap[provider.name].keyFormat"
              required
              v-model="provider.apiKey"
            />
          </div>
        </div>
      </div>

    </div>
    <div class="flex items-center mt-8">
      <button
        type="button"
        @click="close()"
        class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 border border-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
      >
        Close
      </button>
    </div>
  </form>
</template>

<style scoped></style>
