<script setup lang="ts">
// import { IconKey } from '@tabler/icons-vue';
// @ts-ignore
import IconKey from '@tabler/icons-vue/dist/esm/icons/IconKey.mjs';
import type { Modal } from 'flowbite';
import Providers from './Providers.vue';
import Models from './Models.vue';

const store = useAppStore();
const { isShowAPIKeyModal } = storeToRefs(store);

let modal: Modal;
const tab: Ref<"providers" | "models"> = ref("models");

onMounted(() => {
  useFlowbite(({ Modal }) => {
    const $modelEl = document.getElementById("api-key-modal");
    modal = new Modal($modelEl);

    modal.updateOnShow(() => {
        tab.value = "models";
    });

    // in case user clicks outside the modal
    modal.updateOnHide(() => {
        store.hideAPIKeyModal();
    });
  });
});

watch(isShowAPIKeyModal, (value) => {
    if (value === true) {
        modal.show();
    } else {
        modal.hide();
    }
});
</script>

<template>
    <!-- Main modal -->
    <div
        id="api-key-modal"
        tabindex="-1"
        aria-hidden="true"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
        <div class="relative p-4 w-full max-w-xl max-h-full">
            <!-- Modal content -->
            <div
                class="relative bg-white rounded-lg min-h-[545px] shadow dark:bg-gray-700 flex flex-col"
            >
                <!-- Modal header -->
                <div
                    class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600"
                >
                    <h3
                        class="text-lg font-semibold text-gray-900 dark:text-white flex items-center"
                    >
                        <icon-key stroke-width="2.5" class="inline-block w-5 h-5 me-3"></icon-key>
                        <span>API Keys</span>
                    </h3>
                    <button
                        type="button"
                        @click="store.hideAPIKeyModal()"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    >
                        <svg
                            class="w-3 h-3"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 14 14"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                            />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>

                <div
                    class="text-sm px-2 font-medium text-center text-gray-500 border-b border-gray-200 dark:text-gray-400 dark:border-gray-700"
                >
                    <ul class="flex flex-wrap -mb-px">
                        <li class="me-2">
                            <a
                                href="#"
                                class="inline-block p-4 rounded-t-lg"
                                :class="
                                    tab === 'models'
                                        ? [
                                              'text-blue-600',
                                              'border-b-2',
                                              'border-blue-600',
                                              'dark:text-blue-500',
                                              'dark:border-blue-500',
                                          ]
                                        : [
                                              'border-b-2',
                                              'border-transparent',
                                              'hover:text-gray-600',
                                              'hover:border-gray-300',
                                              'dark:hover:text-gray-300',
                                          ]
                                "
                                @click="tab = 'models'"
                                >Models</a
                            >
                        </li>
                        <li class="me-2">
                            <a
                                href="#"
                                class="inline-block p-4 rounded-t-lg"
                                :class="
                                    tab === 'providers'
                                        ? [
                                              'text-blue-600',
                                              'border-b-2',
                                              'border-blue-600',
                                              'dark:text-blue-500',
                                              'dark:border-blue-500',
                                          ]
                                        : [
                                              'border-b-2',
                                              'border-transparent',
                                              'hover:text-gray-600',
                                              'hover:border-gray-300',
                                              'dark:hover:text-gray-300',
                                          ]
                                "
                                @click="tab = 'providers'"
                                >Providers</a
                            >
                        </li>
                    </ul>
                </div>

                <!-- Modal body -->
                <div class="flex flex-col flex-grow">
                    <Models
                        v-if="tab === 'models'"
                        @close="store.hideAPIKeyModal()"
                    ></Models>
                    <Providers
                        v-else
                        @close="store.hideAPIKeyModal()"
                    ></Providers>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
