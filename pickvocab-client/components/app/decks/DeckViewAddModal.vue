<script setup lang="ts">
// import { IconCards } from '@tabler/icons-vue';
// @ts-ignore
import IconCards from '@tabler/icons-vue/dist/esm/icons/IconCards.mjs';
import type { Modal } from 'flowbite';
import { watchDebounced } from '@vueuse/core';
import { CardType } from '~/utils/card';

const authStore = useAuthStore();

const props = defineProps<{ deck: Deck }>();
const emit = defineEmits(['submit']);
const store = useAppStore();

const isShowModal = defineModel<boolean>('isShowModal', { default: false });
let modal: Modal;
const selectedIdx = ref(0);
const searchText = ref('');
const searchInput = ref(null);
let suggestedCards: Ref<Card[]> = ref([]);
const cardRefs = ref([]);

function handleKeyPress (e: KeyboardEvent) {
  if (e.key === 'ArrowUp') {
    e.stopPropagation();
    e.preventDefault();
    selectedIdx.value = Math.max(0, selectedIdx.value - 1);
  } else if (e.key === 'ArrowDown') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedCards.value.length > 0) {
      selectedIdx.value = Math.min(suggestedCards.value.length - 1, selectedIdx.value + 1);
    }
  } else if (e.key === 'Enter') {
    e.stopPropagation();
    e.preventDefault();
    if (suggestedCards.value.length > 0) {
      addCard(suggestedCards.value[selectedIdx.value]);
    }
  }
}

onMounted(() => {
  useFlowbite(({ Modal }) => {
    const $modelEl = document.getElementById('add-card-to-list-modal');
    modal = new Modal($modelEl);

    modal.updateOnShow(async () => {
      // suggestedCards.value = (await store.listCards()).result;
      suggestedCards.value = (await store.listGenericCards({ owner: authStore.currentUser?.id })).result;
      window.addEventListener('keydown', handleKeyPress, true);
    });

    modal.updateOnHide(() => {
      isShowModal.value = false;
      window.removeEventListener('keydown', handleKeyPress, true);
    });
  });
});

watch(isShowModal, async (value) => {
  if (value === true) {
    searchText.value = '';
    selectedIdx.value = 0;
    nextTick(() => (searchInput.value as any).focus());
    modal.show();
  } else {
    modal.hide();
  }
});

let currentController: AbortController | undefined;

watchDebounced(searchText, async () => {
  if (searchText.value === '') {
    // suggestedCards.value = (await store.listCards()).result;
    suggestedCards.value = (await store.listGenericCards({ owner: authStore.currentUser?.id })).result;
  } else {
    if (currentController) {
      currentController.abort();
    }
    currentController = new AbortController();
    suggestedCards.value = await store.searchGenericCards(searchText.value, currentController.signal);
    if (selectedIdx.value >= suggestedCards.value.length) {
      selectedIdx.value = 0;
    }
  }

}, {
  debounce: 300
});

watch(selectedIdx, () => {
  (cardRefs.value[selectedIdx.value] as any).scrollIntoView(false);
});

async function addCard(card: Card) {
  await store.addCardToDeck(props.deck.id, card.id); // FIXME
  isShowModal.value = false;
  emit('submit');
}
</script>

<template>
  <!-- Main modal -->
  <div id="add-card-to-list-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <icon-cards stroke-width="2.5" class="inline-block w-5 h-5 me-3"></icon-cards>
            <span>Add card to notebook</span>
          </h3>
          <button type="button" @click="isShowModal = false"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <form class="">
          <div class="p-4 md:p-5">
            <label for="name" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">DefinitionCard</label>
            <input type="text"
              class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
              ref="searchInput" v-model="searchText">
          </div>
          <div class="pb-4">
            <div class="mt-4 h-[360px] overflow-y-auto">
              <div class="px-4 md:px-5">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Your cards</p>
                <ul class="my-4 space-y-3">
                  <li v-for="(card, idx) in suggestedCards" ref="cardRefs"
                    class="cursor-pointer p-3 text-base font-bold text-gray-600 rounded-lg hover:bg-gray-200 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
                    :class="{ 'bg-gray-200': idx === selectedIdx, 'bg-gray-50': idx !== selectedIdx }"
                    @click="addCard(card)">
                    <div v-if="card.cardType === CardType.DefinitionCard">
                      <div class="flex items-center ms-3">
                        <span>
                          <span>{{ card.word }}</span>
                          <span class="ml-1 inline-block text-xs border rounded-xl border-blue-400 text-blue-400 px-2 align-text-top"
                            v-if="card.definition.partOfSpeech"
                            :class="stylesForPartOfSpeech(card.definition.partOfSpeech)">{{ card.definition.partOfSpeech }}</span>
                        </span>
                      </div>
                      <p class="ms-3 font-normal text-gray-500" v-if="card.definition.definition">{{ card.definition.definition }}</p>
                    </div>
                    <div v-else>
                      <div class="flex items-center ms-3">
                        <span>
                          <span>{{ card.wordInContext.word }}</span>
                          <span class="ml-1 inline-block text-xs border rounded-xl border-blue-400 text-blue-400 px-2 align-text-top"
                            v-if="card.wordInContext.definition?.partOfSpeech"
                            :class="stylesForPartOfSpeech(card.wordInContext.definition.partOfSpeech)">{{ card.wordInContext.definition.partOfSpeech }}</span>
                        </span>
                      </div>
                      <p class="ms-3 font-normal text-gray-500" v-if="card.wordInContext.definition?.definition">{{ card.wordInContext.definition.definition }}</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
