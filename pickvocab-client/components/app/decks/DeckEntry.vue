<script setup lang="ts">
const props = defineProps<{
  deck: Deck,
  public?: boolean
}>();
</script>

<template>
  <div
    class="cursor-pointer block p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700">

    <h5 class="mb-2 text-lg font-semibold tracking-tight text-gray-700 dark:text-white">
      <span class="align-middle">{{ deck.name }}</span>
      <span v-if="public"
        class="ml-2 bg-yellow-100 text-yellow-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded border border-yellow-300">Public</span>
    </h5>
    <p class="font-normal text-gray-600 dark:text-gray-400" v-if="deck.description">{{ deck.description }}</p>
  </div>
</template>

<style scoped></style>
