<script setup lang="ts">
import { FwbPagination } from 'flowbite-vue';
// import { IconPlus } from '@tabler/icons-vue';
// @ts-ignore
import IconPlus from '@tabler/icons-vue/dist/esm/icons/IconPlus.mjs';
// @ts-ignore
import VueSimpleContextMenu from 'vue-simple-context-menu';
import DeckViewAddModal from '~/components/app/decks/DeckViewAddModal.vue';
import DefinitionCardEntry from '~/components/app/cards/DefinitionCardEntry.vue';
import { CardType } from '~/utils/card';
import ContextCardEntry from '../cards/ContextCardEntry.vue';
import DangerAlert from '~/components/app/utils/DangerAlert.vue';

const props = withDefaults(defineProps<{
  isDemoPage?: boolean
}>(), { isDemoPage: false });

const store = useAppStore();
const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const deck: Ref<Deck | undefined> = ref();
const isShowAddCardModal = ref(false);

const currentPage = ref(1);
const totalPages = ref(0);
const isLoading = ref(false);

useSeoMeta({
  title: computed(() => deck.value ? `${deck.value.name} - Notebooks | Pickvocab` : 'Notebooks | Pickvocab'),
});

async function initDeck(id: DeckId) {
  isLoading.value = true;
  try {
    const storeDeck = await store.getDeck(Number(id), currentPage.value);
    deck.value = storeDeck ? { ...storeDeck } : undefined;

    if (deck.value) {
      const totalCards = deck.value ? deck.value.totalCards || deck.value.cards.length : 0;
      totalPages.value = deck.value ? Math.ceil(totalCards / 20) : 0;

      const newRoute = router.resolve({
        params: { slug: slugifyText(deck.value.name), ...route.params },
        query: route.query
      });
      window.history.replaceState('', '', newRoute.fullPath);
    }
  } finally {
    isLoading.value = false;
  }
}

watch(() => route.params.id, async (value) => {
  if (Array.isArray(value)) throw new Error('Unexpected');
  await initDeck(value);
});

watch(() => route.query.page, async (value) => {
  if (Array.isArray(value)) throw new Error('Unexpected');

  currentPage.value = value ? Number(value) : 1;
  await initDeck(Number(route.params.id));

}, { immediate: true });

watch(currentPage, (value) => {
  if (value === 1) {
    router.replace({ query: { page: undefined } });
  } else {
    router.push({ query: { page: value } });
  }
});

let timer: any;
let touchDuration = 500;
function cardTouchStart(event: any, card: Card) {
  timer = setTimeout(() => openCardContextMenu({ pageX: event.targetTouches[0].pageX, pageY: event.targetTouches[0].pageY }, card), touchDuration);
}

function cardTouchEnd() {
  clearTimeout(timer);
}

const cardContextMenu: Ref<any> = ref();

const cardContextMenuOptions = [
  {
    name: 'Delete'
  }
]

async function cardOptionClick(event: any) {
  const card: Card = event.item;
  const option: { name: string } = event.option;
  if (option.name === 'Delete') {
    await store.removeCardFromDeck(deck.value!.id, card.id);
    const storeDeck = await store.getDeck(Number(route.params.id), currentPage.value);
    console.log(storeDeck);
    deck.value = storeDeck ? { ...storeDeck } : undefined;
  }
}

function openCardContextMenu(event: any, item: any) {
  cardContextMenu.value.showMenu(event, item);
}

async function reload() {
  deck.value = await store.getDeck(Number(route.params.id), currentPage.value);
  console.log(deck.value);
}

function goToCard(cardId: CardId) {
  if (props.isDemoPage) {
    router.push({ name: 'demo-cards-slug-id', params: { id: cardId } });
  } else {
    router.push({ name: 'app-cards-slug-id', params: { id: cardId } });
  }
}

</script>

<template>
  <div class="w-full h-full box-border bg-gray-50">
    <div class="sm:ml-64 mt-14 bg-gray-50">
      <div v-if="deck">
        <div class="py-10 pl-10 pr-10">
          <div class="flex items-center py-2">
            <p class="text-3xl text-gray-700 font-semibold">{{ deck.name }}</p>
            <span v-if="deck.isDemo"
              class="ml-2 bg-yellow-100 text-yellow-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded border border-yellow-300">Public</span>
            <button v-if="deck.owner === authStore.currentUser?.id" type="button" class="flex items-center ml-auto"
              @click="isShowAddCardModal = true">
              <icon-plus class="w-6 h-6 text-gray-800 dark:text-white"></icon-plus>
            </button>
          </div>

          <p class="text-base text-gray-500 mt-4" v-if="deck.description">
            {{ deck.description }}
          </p>
        </div>

        <div v-for="card in deck.cards" class="px-4 lg:px-32 xl:px-44 2xl:px-64">
          <DefinitionCardEntry v-if="card.cardType === CardType.DefinitionCard"  :card="card" class="mb-4" @click="goToCard(card.id)"
            @touchstart="cardTouchStart($event, card)" @touchend="cardTouchEnd()"
            @contextmenu.prevent.stop="openCardContextMenu($event, card)"></DefinitionCardEntry>
          <ContextCardEntry v-if="card.cardType === CardType.ContextCard"  :card="card" class="mb-4" @click="goToCard(card.id)"
            @touchstart="cardTouchStart($event, card)" @touchend="cardTouchEnd()"
            @contextmenu.prevent.stop="openCardContextMenu($event, card)"></ContextCardEntry>
        </div>

        <fwb-pagination class="mt-4 py-8 flex justify-center items-center" v-model="currentPage"
          :total-pages="totalPages"></fwb-pagination>

        <DeckViewAddModal v-if="deck !== undefined" :deck="deck" v-model:is-show-modal="isShowAddCardModal"
          @submit="reload()"></DeckViewAddModal>
        <vue-simple-context-menu element-id="cardContextMenu" :options="cardContextMenuOptions" ref="cardContextMenu"
          @option-clicked="cardOptionClick">
        </vue-simple-context-menu>
      </div>
      <div v-else-if="!isLoading" class="py-10 pl-10 pr-10">
        <DangerAlert
          :label="!authStore.user ? 'Sign in to see this page' : 'Notebook not found'"
          :primary-btn-label="!authStore.user ? 'Sign in' : undefined"
          @primary-btn-click="!authStore.user ? authStore.showLoginModal() : undefined"
        />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
