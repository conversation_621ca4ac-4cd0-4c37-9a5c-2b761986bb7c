<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { FunctionalComponent } from 'vue';
// @ts-ignore
import IconChevronRight from '@tabler/icons-vue/dist/esm/icons/IconChevronRight.mjs';

// Define the type for the icon component prop (simplified)
type IconComponent = FunctionalComponent<any>;

const props = defineProps<{
  title: string;
  iconComponent: IconComponent;
  itemCount?: number | null; // Optional item count
}>();

const isOpen = defineModel<boolean>({ default: false });


function toggle() {
  isOpen.value = !isOpen.value;
}
</script>

<template>
  <div>
    <!-- Header -->
    <div 
      class="flex items-center text-gray-800 hover:bg-gray-100 rounded-md py-2 px-3 cursor-pointer border border-transparent hover:border-gray-200 transition-colors"
      @click="toggle"
      role="button"
      :aria-expanded="isOpen"
      :aria-controls="`collapsible-content-${title.replace(/\s+/g, '-')}`"
    >
      <div class="flex items-center flex-1">
        <component :is="iconComponent" class="h-5 w-5 mr-3 text-emerald-600 flex-shrink-0" />
        <span class="text-base font-medium">{{ title }}</span>
        <span 
          v-if="itemCount !== undefined && itemCount !== null" 
          class="inline-flex items-center justify-center ml-2 w-6 h-6 text-xs font-semibold text-emerald-800 bg-emerald-100 rounded-full"
        >
          {{ itemCount }}
        </span>
      </div>
      <IconChevronRight 
        class="h-5 w-5 text-gray-400 transition-transform"
        :class="{ 'rotate-90': isOpen }"
      />
    </div>

    <!-- Content -->
    <div 
      v-if="isOpen" 
      :id="`collapsible-content-${title.replace(/\s+/g, '-')}`"
      class="mt-2 rounded-md overflow-hidden transition-all duration-300"
    >
      <slot></slot> <!-- Default slot for content -->
    </div>
  </div>
</template>

<style scoped>
/* Minimal styles, relies on Tailwind */
</style>