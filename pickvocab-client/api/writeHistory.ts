import { getAxiosInstance } from '~/utils/axiosInstance';
import type { Card } from '~/utils/card';

// Type definitions matching the backend models
export interface Revision {
  revision: string;
  feedback: string;
  learning_focus?: string[];
  user_vocabularies_used?: string[]; // Index references from LLM generation
  real_card_ids?: string[]; // Actual card IDs (used when restoring from history)
}

export interface ApiWritingRevisionHistory {
  id: number;
  owner: number;
  created_at: string;
  trigger_type: string;
  original_text: string;
  use_my_vocabulary: boolean;
  selected_tone: string;
  revisions: Revision[];
}

export interface WritingRevisionHistory {
  id: number;
  createdAt: string;
  triggerType: 'Revise' | 'Refresh' | 'GrammarCheck';
  originalText: string;
  useMyVocabulary: boolean;
  selectedTone: string;
  revisions: Revision[];
}

export interface ApiWritingRevisionHistoryCards {
  id: number;
  history: number;
  card: number;
}

// Mapping functions for API data conversion
function toWritingRevisionHistory(apiHistory: ApiWritingRevisionHistory): WritingRevisionHistory {
  return {
    id: apiHistory.id,
    createdAt: apiHistory.created_at,
    triggerType: apiHistory.trigger_type as 'Revise' | 'Refresh' | 'GrammarCheck',
    originalText: apiHistory.original_text,
    useMyVocabulary: apiHistory.use_my_vocabulary,
    selectedTone: apiHistory.selected_tone,
    revisions: apiHistory.revisions
  };
}

function toApiWritingRevisionHistory(history: Partial<WritingRevisionHistory>): Partial<ApiWritingRevisionHistory> {
  return {
    trigger_type: history.triggerType,
    original_text: history.originalText,
    use_my_vocabulary: history.useMyVocabulary,
    selected_tone: history.selectedTone,
    revisions: history.revisions
  };
}

export class WriteHistoryApi {
  /**
   * List user's writing revision history entries
   */
  async list(params?: { page?: number, [key: string]: unknown }): Promise<{
    result: WritingRevisionHistory[],
    totalPages: number
  }> {
    const config = useRuntimeConfig();
    const pageSize = Number(config.public.PAGE_SIZE || 20);
    const axios = getAxiosInstance();
    const response = await axios.get('/write/history/', { params });

    return {
      result: response.data.results.map(toWritingRevisionHistory),
      totalPages: Math.ceil(response.data.count / pageSize),
    };
  }

  /**
   * Get a specific writing revision history entry by ID
   */
  async get(id: number): Promise<WritingRevisionHistory> {
    const axios = getAxiosInstance();
    const response = await axios.get(`/write/history/${id}/`);
    return toWritingRevisionHistory(response.data);
  }

  /**
   * Create a new writing revision history entry
   */
  async create(history: Partial<WritingRevisionHistory>, cardIds?: string[]): Promise<WritingRevisionHistory> {
    const axios = getAxiosInstance();
    const apiHistory = toApiWritingRevisionHistory(history);
    const payload = {
      ...apiHistory,
      cardIds  // Add cardIds as a separate field in the request payload
    };
    const response = await axios.post('/write/history/', payload);
    return toWritingRevisionHistory(response.data);
  }

  /**
   * Delete a writing revision history entry
   */
  async delete(id: number): Promise<void> {
    const axios = getAxiosInstance();
    await axios.delete(`/write/history/${id}/`);
  }

  /**
   * Update a specific writing revision history entry
   */
  async update(id: number, data: Partial<WritingRevisionHistory>): Promise<WritingRevisionHistory> {
    const axios = getAxiosInstance();
    const apiData = toApiWritingRevisionHistory(data); // Convert frontend data to API format
    const response = await axios.patch(`/write/history/${id}/`, apiData);
    return toWritingRevisionHistory(response.data); // Convert API response back to frontend format
  }
}