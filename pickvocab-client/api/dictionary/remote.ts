import type { BaseWordEntry, DefinitionDetails, WordEntry, WordEntryId } from "pickvocab-dictionary";
import type { DictionaryApi } from "./types";

interface ApiBaseWordEntry {
  word: string;
  llm_model: number;
  definitions: DefinitionDetails[];
}

export interface ApiWordEntry extends ApiBaseWordEntry {
  id: number;
  creator: number;
  created_at: string;
  updated_at: string;
}

export function toWordEntry(apiWordEntry: ApiWordEntry): WordEntry {
  return {
    ...apiWordEntry,
    createdAt: apiWordEntry.created_at,
    updatedAt: apiWordEntry.updated_at
  };
}

function toApiBaseWordEntry(base: BaseWordEntry): ApiBaseWordEntry {
  return base;
}

export class RemoteDictionaryApi implements DictionaryApi {
  async list(options?: { page?: number, word?: string, is_verified?: boolean, slug?: string }): Promise<WordEntry[]> {
    const axios = getAxiosInstance();
    const response = await axios.get('/words/', { params: options });
    return response.data.results.map(toWordEntry);
  }
  async get(id: WordEntryId): Promise<WordEntry | undefined> {
    const axios = getAxiosInstance();
    const response = await axios.get(`/words/${id}/`);
    return toWordEntry(response.data);
  }
  async create(base: BaseWordEntry): Promise<WordEntry> {
    const apiBase = toApiBaseWordEntry(base);
    const axios = getAxiosInstance();
    const response = await axios.post('/words/', apiBase);
    return toWordEntry(response.data);
  }
  async put(word: WordEntry): Promise<WordEntry> {
    const axios = getAxiosInstance();
    const response = await axios.put(`/words/${word.id}/`, word);
    return toWordEntry(response.data);
  }
  async delete(id: WordEntryId): Promise<void> {
    const axios = getAxiosInstance();
    await axios.delete(`/words/${id}/`);
  }
  async search(text: string, abortSignal?: AbortSignal): Promise<string[]> {
    const axios = getAxiosInstance();
    try {
      const response = await axios.get('/words/search/', { params: { text }, signal: abortSignal });
      return response.data;
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return [];
      }
      throw err;
    }
  }
}
