<script setup lang="ts">
// @ts-ignore
import IconScale from '@tabler/icons-vue/dist/esm/icons/IconScale.mjs';
import type { Modal } from 'flowbite';

const router = useRouter();

const store = useAppStore();
const { isShowCompareModal } = storeToRefs(store);
const words = ref<string[]>([]);
const wordRefs = useTemplateRef<string>('wordRefs');

let modal: Modal;

onMounted(() => {
  useFlowbite(({ Modal }) => {
    const $targetEl = document.getElementById('compare-words-modal');
    modal = new Modal($targetEl);
  
    modal.updateOnShow(async () => {
      words.value = [store.currentCompareWord, ''];
      nextTick(() => {
        if (wordRefs.value && wordRefs.value.length > 1) {
          (wordRefs.value![1] as any).focus();
        }
      });
  
      window.addEventListener('keydown', handleKeyPress, true);
    });
    modal.updateOnHide(() => {
      store.hideCompareModal();
      window.removeEventListener('keydown', handleKeyPress, true);
    });
  });
});

watch(isShowCompareModal, (value) => {
  if (value === true) {
    modal.show();
  } else {
    modal.hide();
  }
});

function handleKeyPress(e: KeyboardEvent) {
  if (e.key === 'Enter') {
    submit();
  }
}

function addMoreWord() {
  words.value.push('');
}

function isOkToSubmit() {
  return words.value.filter(Boolean).length > 1;
}

function submit() {
  if (isOkToSubmit()) {
    router.push({
      name: 'app-ask',
      query: {
        compare: words.value.map((w) => slugifyText(w)).join(','),
      },
    });
    store.hideCompareModal();
  }
}

</script>

<template>
  <div>
    <!-- Main modal -->
    <div id="compare-words-modal" tabindex="-1" aria-hidden="true"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
          <!-- Modal header -->
          <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <icon-scale stroke-width="2.5" class="inline-block w-5 h-5 me-3"></icon-scale>
              <span>Words comparison</span>
            </h3>
            <button type="button" @click="store.hideCompareModal()"
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
              <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
              <span class="sr-only">Close modal</span>
            </button>
          </div>
          <!-- Modal body -->
          <form class="p-4 md:p-5">
            <div class="grid gap-4 mb-4 grid-cols-2">
              <div v-for="(word, idx) in words" class="col-span-2">
                <label for="name" class="block mb-2 text-sm font-medium text-gray-600 dark:text-white">Word</label>

                <input type="text"
                  class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                  v-model="words[idx]" ref="wordRefs" />
              </div>
              <button type="button" class="flex items-center" @click="addMoreWord()">
                <svg class="w-4 h-4 text-blue-700 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                  fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5 12h14m-7 7V5" />
                </svg>
                <span class="text-xs text-blue-700">Add more words</span>
              </button>
            </div>
            <div class="flex items-center mt-8">
              <button type="button" @click="submit()" :disabled="!isOkToSubmit()"
                class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 border border-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center disabled:bg-blue-400 disabled:cursor-not-allowed disabled:border-blue-400 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                Compare
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>