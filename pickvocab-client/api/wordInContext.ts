import type { BaseWordInContextEntry, WordInContextEntry } from "pickvocab-dictionary";

interface ApiBaseWordInContextEntry extends Omit<BaseWordInContextEntry, 'defintionShort'> {
  definition_short?: {
    explanation: string;
  }
}

export interface ApiWordInContextEntry extends ApiBaseWordInContextEntry {
  id: number;
  created_at: string;
  updated_at: string;
}

export function toWordInContextEntry(apiWordEntry: ApiWordInContextEntry): WordInContextEntry {
  return {
    ...apiWordEntry,
    definitionShort: apiWordEntry.definition_short,
    createdAt: apiWordEntry.created_at,
    updatedAt: apiWordEntry.updated_at,
  };
}

function toApiWordInContextEntry(base: BaseWordInContextEntry): ApiBaseWordInContextEntry {
  return {
    ...base,
    definition_short: base.definitionShort
  };
}

export class RemoteWordInContextApi {
  async list(options?: { word?: string }): Promise<WordInContextEntry[]> {
    const axios = getAxiosInstance();
    const response = await axios.get('/word_in_context/', { params: options });
    return response.data.results.map(toWordInContextEntry);
  }
  async get(id: number): Promise<WordInContextEntry | undefined> {
    const axios = getAxiosInstance();
    const response = await axios.get(`/word_in_context/${id}/`);
    return toWordInContextEntry(response.data);
  }
  async create(base: BaseWordInContextEntry): Promise<WordInContextEntry> {
    const apiBase = toApiWordInContextEntry(base);
    const axios = getAxiosInstance();
    const response = await axios.post('/word_in_context/', apiBase);
    return toWordInContextEntry(response.data);
  }
  async put(word: WordInContextEntry): Promise<WordInContextEntry> {
    const apiWord = toApiWordInContextEntry(word);
    const axios = getAxiosInstance();
    const response = await axios.put(`/word_in_context/${word.id}/`, apiWord);
    return toWordInContextEntry(response.data);
  }
  async delete(id: number): Promise<void> {
    const axios = getAxiosInstance();
    await axios.delete(`/word_in_context/${id}/`);
  }
  async lookupPair(word: string, context: string): Promise<WordInContextEntry[] | undefined> {
    const axios = getAxiosInstance();
    const response = await axios.post('/word_in_context/lookup_pair/', {
      word,
      context
    });
    return response.data.results.map(toWordInContextEntry);
  }
}
