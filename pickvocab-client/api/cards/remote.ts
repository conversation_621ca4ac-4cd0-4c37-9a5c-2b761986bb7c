import type { DefinitionDetails } from "pickvocab-dictionary";
import { toWordEntry, type ApiWordEntry } from "../dictionary/remote";
import type { CardsApi } from "./types";
import { CardType } from "~/utils/card";

export interface ApiCard {
  id: number;
  word: string;
  word_ref?: CardId;
  word_ref_detail?: ApiWordEntry;
  word_def_idx: number;
  definition: Partial<DefinitionDetails>;
  owner: number;
  created_at: string;
  updated_at: string;
}

interface ApiBaseCard {
  word: string;
  word_ref?: CardId;
  word_def_idx: number;
  definition: Partial<DefinitionDetails>;
}

export function toCard(apiCard: ApiCard): DefinitionCard {
  return {
    cardType: CardType.DefinitionCard,
    id: apiCard.id,
    word: apiCard.word,
    referenceWord: apiCard.word_ref_detail ? toWordEntry(apiCard.word_ref_detail) : undefined,
    refDefIdx: apiCard.word_def_idx,
    definition: apiCard.definition,
    owner: apiCard.owner,
    createdAt: apiCard.created_at,
    updatedAt: apiCard.updated_at
  }
}

function toApiBaseCard(card: BaseDefinitionCard): ApiBaseCard {
  return {
    word: card.word,
    definition: card.definition,
    word_ref: card.referenceWord?.id,
    word_def_idx: card.refDefIdx ? card.refDefIdx : 0,
  }
}

export class RemoteCardsApi implements CardsApi {
  async list(params?: { page?: number, [key: string]: unknown }): Promise<{ result: DefinitionCard[], totalPages: number }> {
    const config = useRuntimeConfig();
    const pageSize = Number(config.public.PAGE_SIZE || 20);
    const axios = getAxiosInstance();
    const response = await axios.get('/cards/', { params });
    return { result: response.data.results.map(toCard), totalPages: Math.ceil(response.data.count / pageSize) };
  }
  async get(id: CardId): Promise<DefinitionCard | undefined> {
    const axios = getAxiosInstance();
    const response = await axios.get(`/cards/${id}/`);
    return toCard(response.data);
  }
  async create(card: BaseDefinitionCard): Promise<DefinitionCard> {
    const apiBase = toApiBaseCard(card);
    const axios = getAxiosInstance();
    const response = await axios.post('/cards/', apiBase);
    return toCard(response.data as ApiCard);
  }
  async delete(id: CardId): Promise<void> {
    const axios = getAxiosInstance();
    await axios.delete(`/cards/${id}/`);
  }
  async updateDefinition(card: DefinitionCard): Promise<DefinitionCard> {
    const axios = getAxiosInstance();
    const response = await axios.put(`/cards/${card.id}/update_definition/`, {
      definition: card.definition
    });
    return toCard(response.data as ApiCard);
  }
  async search(text: string, abortSignal?: AbortSignal): Promise<DefinitionCard[]> {
    const axios = getAxiosInstance();
    try {
      const response = await axios.get(`/cards/search?text=${text}`, { signal: abortSignal });
      return response.data.map(toCard);
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return [];
      }
      throw err;
    }
  }
}
