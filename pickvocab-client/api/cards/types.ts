export interface CardsApi {
  list(params?: { page?: number, [key: string]: unknown }): Promise<{ result: DefinitionCard[], totalPages: number }>;
  get(id: CardId): Promise<DefinitionCard | undefined>;
  create(card: BaseDefinitionCard): Promise<DefinitionCard>;
  delete(id: CardId): Promise<void>;
  updateDefinition(card: DefinitionCard): Promise<DefinitionCard>;
  search(text: string): Promise<DefinitionCard[]>;
}