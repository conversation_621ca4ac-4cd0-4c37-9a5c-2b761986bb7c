import { getAxiosInstance } from '~/utils/axiosInstance';

// Type definitions matching the backend models and frontend usage

// Matches the Django REST Framework serializer output (snake_case)
export interface ApiCustomTone {
  id: number;
  owner: number; // Assuming owner is represented by user ID in API response
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  bg_color?: string;
  text_color?: string;
}

// Frontend representation (camelCase)
export interface CustomTone {
  id: number;
  ownerId: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  bgColor: string;
  textColor: string;
}

// Payload for creating/updating (camelCase)
export interface CustomTonePayload {
  name: string;
  description?: string; // Description is optional based on schema (blank=True)
  bgColor?: string;
  textColor?: string;
}

// Payload for API (snake_case) - only name/description are sent
interface ApiCustomTonePayload {
  name: string;
  description?: string;
  bg_color?: string;
  text_color?: string;
}


// --- Mapping Functions ---

function toCustomTone(apiTone: ApiCustomTone): CustomTone {
  return {
    id: apiTone.id,
    ownerId: apiTone.owner,
    name: apiTone.name,
    description: apiTone.description,
    createdAt: apiTone.created_at,
    updatedAt: apiTone.updated_at,
    bgColor: apiTone.bg_color || 'bg-gray-100', // Default color if not set
    textColor: apiTone.text_color || 'text-gray-800', // Default color if not set
  };
}

function toApiCustomTonePayload(payload: CustomTonePayload): ApiCustomTonePayload {
  // Only include description if it's provided and not just whitespace
  const apiPayload: ApiCustomTonePayload = {
    name: payload.name,
  };
  if (payload.description !== undefined && payload.description.trim() !== '') {
    apiPayload.description = payload.description;
  } else {
     // Explicitly set to empty string if provided as empty or whitespace,
     // assuming backend handles blank=True correctly.
     // If backend expects null or omission for blank, adjust this.
     apiPayload.description = '';
  }
  
  // Add color properties if provided
  if (payload.bgColor) {
    apiPayload.bg_color = payload.bgColor;
  }
  
  if (payload.textColor) {
    apiPayload.text_color = payload.textColor;
  }
  
  return apiPayload;
}


// --- API Class ---

export class CustomTonesApi {
  private axios = getAxiosInstance();
  private baseUrl = '/custom_tones/'; // Base URL from design doc

  /**
   * List user's custom writing tones
   */
  async list(): Promise<CustomTone[]> {
    const response = await this.axios.get(this.baseUrl);
    return response.data.results.map(toCustomTone);
  }

  /**
   * Create a new custom writing tone
   */
  async create(payload: CustomTonePayload): Promise<CustomTone> {
    const apiPayload = toApiCustomTonePayload(payload);
    const response = await this.axios.post<ApiCustomTone>(this.baseUrl, apiPayload);
    return toCustomTone(response.data);
  }

  /**
   * Partially update a specific custom writing tone (using PATCH)
   */
  async update(id: number, payload: Partial<CustomTonePayload>): Promise<CustomTone> {
    // For PATCH, we might not need a specific snake_case conversion if names match
    // but let's be explicit for clarity if needed.
    // Assuming backend accepts partial snake_case payload for PATCH.
    const apiPayload: Partial<ApiCustomTonePayload> = {};
    if (payload.name !== undefined) {
      apiPayload.name = payload.name;
    }
    // Handle description carefully for PATCH: send empty string if explicitly cleared,
    // or the new value if provided. Omit if not in payload.
    if (payload.description !== undefined) {
       apiPayload.description = payload.description.trim() === '' ? '' : payload.description;
    }

    const response = await this.axios.patch<ApiCustomTone>(`${this.baseUrl}${id}/`, apiPayload);
    return toCustomTone(response.data);
  }

  /**
   * Delete a specific custom writing tone
   */
  async delete(id: number): Promise<void> {
    await this.axios.delete(`${this.baseUrl}${id}/`);
    // No response body expected on 204 No Content
  }

  // Optional: Get a single tone if needed later
  // async get(id: number): Promise<CustomTone> {
  //   const response = await this.axios.get<ApiCustomTone>(`${this.baseUrl}${id}/`);
  //   return toCustomTone(response.data);
  // }
}