export interface ApiBaseWordComparision {
  words: string;
  content: string;
  llm_model: number;
}

export interface ApiWordComparsion extends ApiBaseWordComparision {
  id: number;
  created_at: string;
  updated_at: string;
}

export interface BaseWordComparison {
  words: string;
  content: string;
  llm_model: number;
}

export interface WordComparison extends BaseWordComparison {
  id: number;
  createdAt: string;
  updatedAt: string;
}

function toWordComparison(apiWordComparison: ApiWordComparsion): WordComparison {
  return {
    ...apiWordComparison,
    createdAt: apiWordComparison.created_at,
    updatedAt: apiWordComparison.updated_at
  };
}

function toApiBaseWordComparison(base: BaseWordComparison): ApiBaseWordComparision {
  return base;
}

export class WordComparisonApi {
  async list(options?: { page?: number, words?: string }): Promise<WordComparison[]> {
    const axios = getAxiosInstance();
    const response = await axios.get('/word_comparisons/', { params: options });
    return response.data.results.map(toWordComparison);
  }
  async create(base: BaseWordComparison): Promise<WordComparison> {
    const axios = getAxiosInstance();
    const apiBase = toApiBaseWordComparison(base);
    const response = await axios.post('/word_comparisons/', apiBase);
    return toWordComparison(response.data);
  }
}