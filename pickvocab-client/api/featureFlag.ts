import type { FeatureFlag } from "~/utils/featureFlag";

interface ApiFeatureFlag {
  name: string;
  is_active: boolean;
  except_users: number[];
  created_at: string;
  updated_at: string;
}

function toFeatureFlag(apiFeatureFlag: ApiFeatureFlag): FeatureFlag {
  return {
    name: apiFeatureFlag.name,
    isActive: apiFeatureFlag.is_active,
    exceptUsers: apiFeatureFlag.except_users,
    createdAt: apiFeatureFlag.created_at,
    updatedAt: apiFeatureFlag.updated_at
  }
}

export class FeatureFlagApi {
  async list(): Promise<FeatureFlag[]> {
    const axios = getAxiosInstance();
    const response = await axios.get('/feature_flags/');
    return response.data.map(toFeatureFlag);
  }
}
