import { CodestralChat } from "pickvocab-dictionary";

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const { apiKey, modelId, modelName, maxToken, endpoint, args } = body;

  const chatSource = new CodestralChat({
    modelId,
    modelName,
    maxToken,
    apiKey,
  });

  if ((chatSource as any)[endpoint] === undefined) {
    throw createError({
      statusCode: 400,
      statusMessage: `Invalid endpoint: ${endpoint}`,
    });
  }

  if (endpoint === "sendMessage") {
    const messages = args[0];
    const message = args[1];
    chatSource.setHistory(messages);
    return chatSource.sendMessage(message);
  }
}); 