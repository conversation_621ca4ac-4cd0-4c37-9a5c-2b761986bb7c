import { CodestralSource } from "pickvocab-dictionary";

export default defineEventHandler(async (event) => {
  // Handle CORS
  setHeader(event, 'Access-Control-Allow-Origin', '*');
  setHeader(event, 'Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  setHeader(event, 'Access-Control-Allow-Headers', 'Content-Type, Authorization');
  setHeader(event, 'Access-Control-Allow-Credentials', 'true');

  if (event.node.req.method === 'OPTIONS') {
    event.node.res.statusCode = 204;
    event.node.res.end();
    return;
  }

  const body = await readBody(event);
  const { apiKey, modelId, modelName, maxToken, endpoint, args } = body;

  const source = new CodestralSource({
    modelId,
    modelName,
    maxToken,
    apiKey,
  });

  if ((source as any)[endpoint] === undefined) {
    throw createError({
      statusCode: 400,
      statusMessage: `Invalid endpoint: ${endpoint}`,
    });
  }

  const response = await (source as any)[endpoint](...args);
  return response;
}); 