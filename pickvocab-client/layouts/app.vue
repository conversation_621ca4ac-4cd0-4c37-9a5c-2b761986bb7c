<script setup lang="ts">
import { useEventListener } from '@vueuse/core';
import CompareModal from '~/api/words/CompareModal.vue';
import Navigation from '~/components/app/Navigation.vue';
import Sidebar from '~/components/app/Sidebar.vue';
import APIKeyModal from '~/components/app/apiKey/APIKeyModal.vue';
import AddCardModal from '~/components/app/cards/AddCardModal.vue';
import AddDeckModal from '~/components/app/decks/AddDeckModal.vue';
import ReviewModal from '~/components/app/reviews/ReviewModal.vue';
import SearchModal from '~/components/app/search/SearchModal.vue';
import SignInModal from '~/components/app/user/SignInModal.vue';
import WebExtensionToast from '~/components/app/toasts/WebExtensionToast.vue';
import { isMobile } from 'is-mobile';

const store = useAppStore();

function handleKeyPress(e: KeyboardEvent) {
  if (e.key === 'k' && e.ctrlKey) {
    e.stopPropagation();
    e.preventDefault();
    store.isShowSearchModal = !store.isShowSearchModal;
  }
}

onMounted(async () => {
  useFlowbite(({ initFlowbite }) => {
    initFlowbite();
  });
  useEventListener(window, 'keydown', handleKeyPress);

  if (!isMobile() && !store.isShowWebExtensionToast) {
    setTimeout(() => {
      store.isShowWebExtensionToast = true;
      store.showWebExtensionToast = true;
    }, 15000);
  }
})
</script>

<template>
  <div class="flex flex-col h-dvh">
    <Navigation />
    <Sidebar />

    <slot />

    <SearchModal />
    <AddCardModal />
    <AddDeckModal />
    <APIKeyModal />
    <SignInModal />
    <ReviewModal />
    <CompareModal />
    <WebExtensionToast v-model:open="store.showWebExtensionToast"></WebExtensionToast>
  </div>
</template>

<style scoped></style>
