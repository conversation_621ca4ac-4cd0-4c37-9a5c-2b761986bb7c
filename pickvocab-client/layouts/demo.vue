<script setup lang="ts">
import Navigation from '~/components/app/Navigation.vue';
import Sidebar from '~/components/app/Sidebar.vue';
import AddDeckModal from '~/components/app/decks/AddDeckModal.vue';
import AddCardModal from '~/components/app/cards/AddCardModal.vue';
import SearchModal from '~/components/app/search/SearchModal.vue';
import APIKeyModal from '~/components/app/apiKey/APIKeyModal.vue';
import SignInModal from '~/components/app/user/SignInModal.vue';
import ReviewModal from '~/components/app/reviews/ReviewModal.vue';
import CompareModal from '~/api/words/CompareModal.vue';
import { useEventListener } from '@vueuse/core';

const store = useAppStore();

function handleKeyPress(e: KeyboardEvent) {
  if (e.key === 'k' && e.ctrlKey) {
    e.stopPropagation();
    e.preventDefault();
    store.isShowSearchModal = !store.isShowSearchModal;
  }
}

onMounted(async () => {
  useFlowbite(({ initFlowbite }) => {
    initFlowbite();
  });
  useEventListener(window, 'keydown', handleKeyPress);
})
</script>

<template>
  <div class="flex flex-col h-dvh">
    <Navigation :is-demo-page="true"/>
    <Sidebar :is-demo-page="true"/>

    <slot />

    <SearchModal></SearchModal>
    <AddCardModal></AddCardModal>
    <AddDeckModal></AddDeckModal>
    <APIKeyModal></APIKeyModal>
    <SignInModal></SignInModal>
    <ReviewModal></ReviewModal>
    <CompareModal></CompareModal>
  </div>
</template>

<style scoped>
</style>