import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename); // get the name of the directory

const wordUrls = JSON.parse(fs.readFileSync(path.join(__dirname, 'urls/wordUrls.json'), 'utf8'));
const wordUrlsVn = JSON.parse(fs.readFileSync(path.join(__dirname, 'urls/wordUrlsVn.json'), 'utf8'));

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: true },
  css: ['~/assets/css/tailwind.css'],
  modules: [
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/sitemap',
    '@nuxtjs/robots',
    'nuxt-gtag',
    '@zadigetvoltaire/nuxt-gtm',
    'nuxt-tiptap-editor',
    'shadcn-nuxt'
  ],
  gtag: {
    id: 'G-8WSEEZLYR9',
    enabled: process.env.NODE_ENV === 'production',
  },
  gtm: {
    id: 'GTM-PHX58M5M'
  },
  runtimeConfig: {
    public: {
      ENVIRONMENT: process.env.NODE_ENV === 'production' ? 'production' : 'development',
      GOOGLE_API_KEY_1: process.env.GOOGLE_API_KEY_1,
      GOOGLE_API_KEY_2: process.env.GOOGLE_API_KEY_2,
      GROQ_API_KEY_1: process.env.GROQ_API_KEY_1,
      GROQ_API_KEY_2: process.env.GROQ_API_KEY_2,
      CEREBRAS_API_KEY_1: process.env.CEREBRAS_API_KEY_1,
      PICKVOCAB_MODEL: process.env.PICKVOCAB_MODEL,
      PAGE_SIZE: process.env.PAGE_SIZE,
      LLM_STORE_VERSION: process.env.LLM_STORE_VERSION,
      AUTH_STORE_VERSION: process.env.AUTH_STORE_VERSION,
      API_URL: process.env.NUXT_PUBLIC_API_URL,
      GOOGLE_REDIRECT_URL: process.env.NUXT_PUBLIC_GOOGLE_REDIRECT_URL,
      GOOGLE_CLIENT_ID: process.env.NUXT_PUBLIC_GOOGLE_CLIENT_ID,
      DEFINITION_CARD_ID: process.env.NUXT_DEFINITION_CARD_ID,
      CONTEXT_CARD_ID: process.env.NUXT_CONTEXT_CARD_ID,
    }
  },
  site: {
    url: 'https://pickvocab.com',
    name: 'Pickvocab'
  },
  sitemap: {
    sitemaps: true,
    defaultSitemapsChunkSize: 5000,
    exclude: ['/app/**', '/auth/google'],
    // sources: [
    //   'https://pickvocab.com/wordUrls.json',
    //   'https://pickvocab.com/wordUrlsVn.json'
    // ],
    urls: wordUrls.concat(wordUrlsVn),
  },
  routeRules: {
    '/dictionary/**': {
      appMiddleware: ['auth'],
      ssr: true,
    },
    '/app/cards': { redirect: '/app' },
    '/app/**': {
      ssr: false,
      appMiddleware: ['auth'],
      robots: false,
    },
    '/demo/notebooks': { redirect: '/demo' },
    '/demo/**': {
      ssr: false,
      appMiddleware: ['auth']
    }
  },
  app: {
    head: {
      link: [
        { rel: 'icon', type: 'image/svg+xml', href: '/pickvocab.svg' }
      ],
      title: 'Pickvocab',
      meta: [
        { name: 'description', content: 'Get accurate, context-aware definitions for every word, idiom and phrase with our AI-powered dictionary. Build your own vocabulary notebooks and reinforce learning with spaced-repetition.' },
        { name: 'keywords', content: 'AI dictionary, smart English dictionary, instant definitions, context-aware translations, idioms, collocations, IELTS prep, GMAT vocabulary, GRE word list, TOEFL study, TOEIC practice, paraphrasing tool, word comparison, language learning, vocabulary builder' },
        { property: 'og:type', content: 'website' },
        { property: 'og:site_name', content: 'pickvocab' },
        { property: 'og:url', content: 'https://pickvocab.com' },
        { property: 'og:locale', content: 'en_US' },
        { property: 'og:title', content: 'Pickvocab - AI-powered English Dictionary' },
        { property: 'og:description', content: 'Discover meanings effortlessly with our AI-enhanced English dictionary. From everyday words to intricate phrases, get comprehensive definitions instantly.' },
        { property: 'og:image', content: 'https://pickvocab.com/pickvocab-social.png' },
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: 'Pickvocab - AI-powered English Dictionary' },
        { name: 'twitter:description', content: 'Discover meanings effortlessly with our AI-enhanced English dictionary. From everyday words to intricate phrases, get comprehensive definitions instantly.' },
        { name: 'twitter:image', content: 'https://pickvocab.com/pickvocab-social.png' },
      ]
    }
  },
  tiptap: {
    prefix: 'Tiptap',
  },
  imports: {
    presets: [{
      from: 'pinia',
      imports: ['defineStore', 'storeToRefs', 'acceptHMRUpdate'],
    }],
  },
  robots: {
    blockNonSeoBots: true,
  },
  shadcn: {
    /**
     * Prefix for all the imported component
     */
    prefix: '',
    /**
     * Directory that the component lives in.
     * @default "./components/ui"
     */
    componentDir: './components/ui'
  }
})