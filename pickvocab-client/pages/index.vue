<script setup lang="ts">
import {
  Fwb<PERSON><PERSON>bar,
  FwbNavbar<PERSON>oll<PERSON>se,
  FwbButton,
  FwbBadge,
} from "flowbite-vue";
// import { IconBook, IconBulb, IconAffiliate } from '@tabler/icons-vue';
// @ts-ignore
import IconBook from '@tabler/icons-vue/dist/esm/icons/IconBook.mjs';
// @ts-ignore
import IconBulb from '@tabler/icons-vue/dist/esm/icons/IconBulb.mjs';
// @ts-ignore
import IconAffiliate from '@tabler/icons-vue/dist/esm/icons/IconAffiliate.mjs';
import HomeJumpotron from "~/components/home/<USER>";
import HomeFooter from "~/components/home/<USER>";
import HomeNavbar from '~/components/home/<USER>';
import { Button } from '~/components/ui/button';

useSeoMeta({
  title: 'Pickvocab: AI-powered English Dictionary'
});

const appUrl = '/app';
const demoUrl = '/demo';
</script>

<template>
  <div>
    <HomeNavbar />
    <HomeJumpotron class="pt-10"></HomeJumpotron>

    <div class="flex flex-col items-center mt-28 md:mt-52 py-16 px-4 md:px-8 bg-gradient-to-b from-white to-slate-50 dark:from-gray-900 dark:to-gray-800 shadow-sm">
      <h2 class="mb-6 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center leading-tight">
        Tired of guessing the right meaning <span class="text-blue-600 dark:text-blue-400">every time?</span>
      </h2>
      <div class="text-base text-center font-normal text-gray-600 lg:text-lg sm:px-0 lg:px-24 dark:text-gray-300 max-w-3xl">
        Understand words based on how they're used, not in isolation
      </div>

      <div class="mt-10 space-y-2 px-4 max-w-2xl w-full">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M5 11.917 9.724 16.5 19 7.5" />
          </svg>
          <p class="ml-2 text-gray-700 dark:text-gray-200">
            Get the
            <span class="font-medium text-blue-600 dark:text-blue-400 underline decoration-2 underline-offset-4 decoration-blue-400/50 dark:decoration-blue-500/50">exact meaning</span>
            of words and phrases in context with just one-click
          </p>
        </div>

        <div class="flex items-center">
          <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M5 11.917 9.724 16.5 19 7.5" />
          </svg>
          <p class="ml-2 text-gray-700 dark:text-gray-200">
            Works seamlessly across all websites, from news articles to social media
          </p>
        </div>

        <div class="flex items-center">
          <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M5 11.917 9.724 16.5 19 7.5" />
          </svg>
          <p class="ml-2 text-gray-700 dark:text-gray-200">
            Easily save words along with the context in which you found them for later review
          </p>
        </div>
      </div>

      <div class="mt-10 flex flex-col items-center">
        <div class="flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-3">
          <Button
            as="a"
            href="https://chromewebstore.google.com/detail/pickvocab-ai-powered-dict/nfhhjfaahjkjdjbkpacapdblonogknag?hl=en"
            target="_blank"
            class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Get the browser extension →
          </Button>
          <NuxtLink to="/app/contextual-meaning/2">
            <Button
              variant="outline"
              class="w-full sm:w-auto bg-white hover:bg-gray-100 text-gray-800 font-medium px-6 py-3 rounded-lg border border-gray-200 transition-all duration-200">
              Try on the web
            </Button>
          </NuxtLink>
        </div>

        <div class="mt-4 flex space-x-3 items-center">
          <img
            src="~/assets/chrome.webp"
            alt="Pickvocab Chrome extension - AI dictionary for web browsing"
            title="Install Pickvocab Chrome Extension"
            class="w-8 h-8 object-contain">
          <img
            src="~/assets/edge.webp"
            alt="Pickvocab Edge extension - AI dictionary for web browsing"
            title="Install Pickvocab Edge Extension"
            class="w-8 h-8 object-contain">
        </div>
      </div>

      <div class="mt-16 w-full max-w-[1000px] rounded-xl overflow-hidden shadow-lg">
        <video
          src="~/assets/context-lookup3.mp4"
          class="w-full"
          controls
          autoplay
          muted
          loop
          playsinline
          fetchpriority="high"
          aria-label="Demo of Pickvocab's contextual lookup feature">
          <track
            kind="captions"
            src="/vtt/context-lookup.vtt"
            srclang="en"
            label="English">
        </video>
      </div>
    </div>

    <!-- Epub Reader section -->
    <div class="flex flex-col items-center md:mt-14 py-10 px-4 md:px-8">
      <h2 class="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center">
        <span class="text-blue-600 dark:text-blue-500">AI dictionary</span> inside every book
      </h2>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        Our new Book Reader lets you enjoy English literature with a seamlessly integrated dictionary.
        Tap any word or phrase for instant understanding and accelerate your vocabulary acquisition.
      </div>

      <div class="mt-8 space-y-2 px-4">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M5 11.917 9.724 16.5 19 7.5" />
          </svg>
          <p class="ml-2">Read your favorite English ebooks on any device</p>
        </div>
        <div class="flex items-center">
          <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M5 11.917 9.724 16.5 19 7.5" />
          </svg>
          <p class="ml-2">Instant dictionary lookups without leaving your book</p>
        </div>
        <div class="flex items-center">
          <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M5 11.917 9.724 16.5 19 7.5" />
          </svg>
          <p class="ml-2">Boost vocabulary through an immersive reading experience</p>
        </div>
      </div>

      <div class="mt-8">
        <NuxtLink to="/app/reader">
          <Button
            class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Try the Book Reader →
          </Button>
        </NuxtLink>
      </div>

      <img
        src="/epub-reader.webp"
        alt="Pickvocab Book Reader with integrated AI dictionary"
        title="Read books with instant word lookup"
        class="w-full mt-8 shadow md:w-3/4 max-w-[1024px] md:mt-8 border rounded-lg bg-gray-100 dark:bg-gray-800 object-cover"
        fetchpriority="high"
      />
    </div>

    <!-- Lookup everything section -->
    <!-- <div class="flex flex-col items-center md:mt-14 py-10 px-4 md:px-8">
      <h2 class="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center">
        Lookup
        <span
          class="underline underline-offset-3 decoration-8 decoration-blue-400 dark:decoration-blue-600">everything</span>
      </h2>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        <p>In your language</p>
        <div class="mt-4">🇬🇧 🇪🇸 🇨🇳 🇩🇪 🇫🇷 🇻🇳 🇯🇵 🇰🇷 🇮🇹 🇮🇳 🇷🇺 🇵🇹</div>
      </div>
      <img src="/lookup-everything.webp" alt="Lookup everything"
        class="hidden sm:block w-full mt-4 shadow md:w-3/4 lg:w-2/3 xl:w-1/2 md:mt-8 border rounded-lg" />
      <img src="/lookup-everything-mobile.webp" alt="Lookup everything"
        class="sm:hidden w-full mt-4 shadow md:w-3/4 lg:w-2/3 xl:w-1/2 md:mt-8 border rounded-lg" />
    </div> -->

    <!-- Build up your vocabulary knowledge -->
    <div class="flex flex-col items-center md:mt-14 py-10 px-4 md:px-8">
      <h2 class="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center">
        <mark class="px-2 text-white bg-blue-600 rounded dark:bg-blue-500">Build up</mark>
        your vocabulary knowledge
      </h2>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        Curate and organize your vocabularies into notebooks for better review
      </div>
      <a :href="demoUrl">
        <Button
          variant="outline"
          class="w-full sm:w-auto bg-white hover:bg-gray-100 text-gray-800 font-medium px-6 py-3 rounded-lg border border-gray-200 transition-all duration-200 mt-4">
          View example notebooks
        </Button>
      </a>
      <img src="/deck.webp" alt="Vocabulary notebook"
        class="hidden sm:block w-full mt-4 shadow md:w-3/4 max-w-[1024px] md:mt-8 border rounded-lg" />
      <img src="/deck-mobile.webp" alt="Vocabulary notebook"
        class="sm:hidden w-full mt-4 shadow md:w-3/4 max-w-[1024px] md:mt-8 border rounded-lg" />
      <!-- <img src="/deck-2.webp" alt="" class="w-full mt-4 shadow md:w-3/4 lg:w-2/3 xl:w-1/2 md:mt-8 border rounded-lg"> -->
    </div>

    <!-- Writing Assistant -->
    <div class="flex flex-col items-center md:mt-14 py-10 px-4 md:px-8">
      <h2 class="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center">
        <span class="text-blue-600 dark:text-blue-500">Writing Assistant</span> with your vocabulary
      </h2>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        Improve your writing fluency while putting your vocabulary into practice
      </div>

      <div class="mt-8 lg:mt-16 flex flex-col lg:flex-row justify-center w-full max-w-8xl">
        <div class="flex flex-col items-center lg:items-start">
          <div class="space-y-4">
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M5 11.917 9.724 16.5 19 7.5" />
              </svg>
              <p class="ml-2">Fix grammar and enhance wording for more natural expression</p>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M5 11.917 9.724 16.5 19 7.5" />
              </svg>
              <p class="ml-2">Improve overall writing clarity and fluency</p>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M5 11.917 9.724 16.5 19 7.5" />
              </svg>
              <p class="ml-2">Incorporate words from your notebooks into your writing</p>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M5 11.917 9.724 16.5 19 7.5" />
              </svg>
              <p class="ml-2">Track your progress with revision history</p>
            </div>
          </div>

          <div class="mt-8">
            <NuxtLink to="/app/write">
              <Button
                class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
                Try it now →
              </Button>
            </NuxtLink>
          </div>
        </div>

        <div class="flex justify-center items-center mt-8 lg:mt-0 lg:ml-16">
          <!-- Writing assistant image -->
          <img
            src="/writing.webp"
            alt="Writing Assistant"
            class="w-full lg:max-w-[700px] max-w-[400px] sm:max-w-[600px]"
          />
        </div>
      </div>
    </div>

    <!-- Review -->
    <div class="flex flex-col items-center md:mt-14 py-10 px-4 md:px-8">
      <h2 class="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center">
        <span class="text-blue-600 dark:text-blue-500">Spaced repetition</span>
        learning
      </h2>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        Review with our intelligent spaced repetition system for optimal
        retention and learning
      </div>
      <img src="/review.webp" alt="Spaced Repetition Learning"
        class="hidden sm:block w-full mt-4 shadow md:w-3/4 max-w-[1024px] md:mt-8 border rounded-lg" />
      <img src="/review-mobile.webp" alt="Spaced Repetition Learning"
        class="sm:hidden w-full mt-4 shadow md:w-3/4 max-w-[1024px] md:mt-8 border rounded-lg" />
      <!-- <img src="/review-2.webp" alt="" class="w-full mt-4 shadow md:w-3/4 lg:w-2/3 xl:w-1/2 md:mt-8 border rounded-lg"> -->
    </div>

    <!-- What makes pickvocab different?  section -->
    <div class="flex flex-col items-center mt-16 md:mt-32 py-24 md:py-44 px-8 bg-slate-50">
      <h3 class="mb-4 text-2xl font-semibold text-gray-900 dark:text-white md:text-3xl lg:text-4xl text-center">
        Have you ever understood English articles but still <span
          class="text-blue-600 dark:text-blue-500">struggled</span>
        with speaking or writing?
      </h3>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 2xl:px-96 dark:text-gray-400">
        That's because building English instinct takes more than knowing definitions - it requires mastering how to use
        words fluently.
      </div>
      <div class="mt-20 lg:mt-32 flex flex-col lg:flex-row px-8 2xl:px-72">
        <div>
          <icon-book class="w-8 h-8 mb-4 text-gray-800 dark:text-white"></icon-book>
          <p class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Contextual learning</p>
          <p class="font-normal text-gray-700 dark:text-gray-400">Learn words within real-life contexts, not in
            isolation
          </p>
        </div>

        <div class="mt-12 lg:mt-0 lg:ml-12">
          <icon-bulb class="w-8 h-8 mb-4 text-gray-800 dark:text-white"></icon-bulb>
          <p class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Build language instinct</p>
          <p class="font-normal text-gray-700 dark:text-gray-400">Develop quick, natural responses in English
            conversations
          </p>
        </div>

        <div class="mt-12 lg:mt-0 lg:ml-12">
          <icon-affiliate class="w-8 h-8 mb-4 text-gray-800 dark:text-white"></icon-affiliate>

          <p class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Deep word connections</p>
          <p class="font-normal text-gray-700 dark:text-gray-400">Grasp subtle differences between similar words for
            precise
            usage</p>
        </div>
      </div>
    </div>

    <!-- Call to Action section -->
    <div class="flex flex-col items-center mt-16 md:mt-32 py-10 px-8">
      <h3 class="mb-4 text-2xl font-semibold text-gray-900 dark:text-white md:text-3xl lg:text-4xl text-center">
        Ready to enrich your English vocabularies?
      </h3>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        Join thousands of learners who have transformed their English skills
        with us
      </div>
      <div class="mt-12">
        <NuxtLink to="/app">
          <Button
            class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Get started →
          </Button>
        </NuxtLink>
        <NuxtLink to="/demo">
          <Button
            variant="outline"
            class="w-full sm:w-auto bg-white hover:bg-gray-100 text-gray-800 font-medium px-6 py-3 rounded-lg border border-gray-200 transition-all duration-200 mt-2 md:mt-0 md:ml-2">
            Demo
          </Button>
        </NuxtLink>
      </div>
    </div>

    <HomeFooter class="mt-24 md:mt-36"></HomeFooter>
  </div>
</template>

<style scoped></style>
