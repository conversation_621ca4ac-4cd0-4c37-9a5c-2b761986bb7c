<script setup lang="ts">
import {
  Fwb<PERSON><PERSON><PERSON>,
  FwbNavbar<PERSON><PERSON><PERSON>se,
  FwbButton,
} from "flowbite-vue";
import HomeFooter from "~/components/home/<USER>";
import HomeNavbar from '~/components/home/<USER>';
// @ts-ignore
import IconBook from '@tabler/icons-vue/dist/esm/icons/IconBook.mjs';
// @ts-ignore
import IconBulb from '@tabler/icons-vue/dist/esm/icons/IconBulb.mjs';
// @ts-ignore
import IconBookmark from '@tabler/icons-vue/dist/esm/icons/IconBookmark.mjs';
// @ts-ignore
import IconStar from '@tabler/icons-vue/dist/esm/icons/IconStar.mjs';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconSparkles from '@tabler/icons-vue/dist/esm/icons/IconSparkles.mjs';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '~/components/ui/carousel'
import AutoScroll from 'embla-carousel-auto-scroll'

useSeoMeta({
  title: "AI-Powered English Dictionary & Contextual Lookup | Pickvocab",
  description: "Go beyond static definitions with Pickvocab's AI Dictionary & Contextual Lookup. Instantly understand any word, phrase, or idiom in its true context. Save, learn, and master English faster",
  keywords: "ai dictionary, contextual meaning, contextual lookup, english dictionary, learn english vocabulary, vocabulary builder, phrase lookup, idiom dictionary, vocabulary notebook, instant translation, real english"
});

const appUrl = '/app';
</script>

<template>
  <div>
    <HomeNavbar />

    <!-- Hero Section -->
    <div class="flex flex-col items-center mt-16 md:mt-24 py-10 px-4 md:px-8">
      <h1 class="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center">
        The Smartest <span class="text-blue-600 dark:text-blue-500">English Dictionary</span>
      </h1>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        Understand any English instantly with AI that explains everything traditional dictionaries can't
      </div>

      <div class="mt-8">
        <NuxtLink :to="appUrl">
          <Button
            class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Get started →
          </Button>
        </NuxtLink>
      </div>

      <video
        src="~/assets/context-lookup3.mp4"
        class="w-full mt-8 max-w-[1024px] rounded-lg"
        controls
        autoplay
        muted
        loop
        playsinline
        fetchpriority="high"
        aria-label="Demo of Pickvocab's AI Dictionary in action">
        <track
          kind="captions"
          src="/vtt/context-lookup.vtt"
          srclang="en"
          label="English">
      </video>
    </div>

    <!-- Feature Introduction Sections -->
    <!-- 1. AI-Powered Dictionary -->
    <div class="flex flex-col md:flex-row items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 md:mt-32 py-10 px-4 md:px-12 md:gap-8">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-full">
            <IconBook class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          The AI-powered dictionary
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Look up & understand everything, from simple words to complex expressions</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">See how words are used through examples and synonyms</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Learn in your own language with definitions translated for better understanding</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for AI Dictionary Visual -->
        <div class="flex flex-col items-center w-full mt-4 shadow md:mt-8 border rounded-lg overflow-hidden">
          <img src="/lookup-everything.webp" alt="Lookup everything" title="Lookup everything"
            class="hidden sm:block w-full object-cover" />
          <img src="/lookup-everything-mobile.webp" alt="Lookup everything" title="Lookup everything"
            class="sm:hidden w-full object-cover" />
        </div>
      </div>
    </div>

    <!-- 2. Contextual Meaning Lookup -->
    <div class="flex flex-col md:flex-row-reverse items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 py-10 px-4 md:px-12 bg-gray-50 dark:bg-gray-800 rounded-lg md:gap-8">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-indigo-50 dark:bg-indigo-900 rounded-full">
            <IconBulb class="w-8 h-8 text-indigo-600 dark:text-indigo-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Understand words based on how they're used
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">See the exact meaning based on context - no more guessing from generic definitions</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Works seamlessly across all websites, from news articles to social media, <a href="https://chromewebstore.google.com/detail/pickvocab-ai-powered-dict/nfhhjfaahjkjdjbkpacapdblonogknag" target="_blank" class="text-blue-500">with our dedicated extension</a></p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Look up words instantly while reading ebooks with our built-in dictionary</p>
          </div>
        </div>
      </div>

      <div class="md:w-1/2">
        <!-- Placeholder for Contextual Lookup Visual -->
        <div class="flex flex-col items-center w-full mt-4 md:mt-16 border rounded-lg overflow-hidden">
          <img src="/context-lookup.webp" alt="Contextual Lookup Interface" title="Contextual Lookup Interface"
            class="hidden sm:block w-full object-cover" />
          <img src="/context-lookup-4-mobile.webp" alt="Contextual Lookup Interface" title="Contextual Lookup Interface"
            class="sm:hidden w-full object-cover" />
        </div>
      </div>
    </div>

    <!-- 3. Save & Master Words -->
    <div class="flex flex-col md:flex-row items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 py-10 px-4 md:px-12 md:gap-8">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-full">
            <IconBookmark class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Save & master every word you discover
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Capture words exactly as you find them - with real examples that show authentic usage</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Turn every reading session into effortless vocabulary growth with one-click saving</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Learn vocabulary naturally through smart reviews that remind you how each word works in context</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Vocabulary Notebook Visual -->
        <div class="flex flex-col items-center w-full mt-4 shadow md:mt-8 border rounded-lg overflow-hidden">
          <img src="/capture-word.webp" alt="Screenshot of the Vocabulary Notebook interface" title="Screenshot of the Vocabulary Notebook interface"
            class="hidden sm:block w-full object-cover" />
          <img src="/capture-word-mobile.webp" alt="Screenshot of the Vocabulary Notebook interface" title="Screenshot of the Vocabulary Notebook interface"
            class="sm:hidden w-full object-cover" />
        </div>
      </div>
    </div>

    <!-- LLM Providers Section -->
    <div class="mt-16 md:mt-32 py-20 bg-gradient-to-b from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 relative overflow-hidden">
      <!-- Decorative elements -->
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden opacity-5 pointer-events-none">
        <div class="absolute -top-24 -left-24 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl"></div>
        <div class="absolute -bottom-24 -right-24 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-3xl"></div>
      </div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
          <!-- @ts-ignore -->
          <IconSparkles class="inline-block w-10 h-10 text-blue-600 dark:text-blue-400 mb-4" :stroke-width="1.5" />
          <h2 class="mb-3 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400">
            Powered by World-Class AI Models
          </h2>
          <p class="mt-4 text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Leveraging the most advanced language models for accurate and nuanced definitions
          </p>
          <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto mt-6"></div>
        </div>

        <Carousel
          :opts="{ align: 'start', loop: true }"
          :plugins="[AutoScroll({ speed: 2, stopOnInteraction: false, stopOnMouseEnter: false })]"
          class="w-full"
        >
          <CarouselContent class="-ml-2">
            <CarouselItem class="basis-1/2 md:basis-1/3 pl-2 py-4">
              <!-- OpenAI logos -->
              <div class="flex items-center justify-center">
                <img src="/openai.svg" alt="OpenAI" class="flex-none leading-none mr-3 h-8 w-8">
                <img src="/openai-logo-text.svg" class="h-8" alt="OpenAI Logo Text">
              </div>
            </CarouselItem>
            <CarouselItem class="basis-1/2 md:basis-1/3 pl-2 py-4">
              <!-- Gemini logos -->
              <div class="flex items-center justify-center">
                <img src="/gemini.svg" alt="Gemini" class="flex-none leading-none mr-3 h-8 w-8">
                <img src="/gemini-logo-text.svg" class="h-8" alt="Gemini Logo Text">
              </div>
            </CarouselItem>
            <CarouselItem class="basis-1/2 md:basis-1/3 pl-2 py-4">
              <!-- Claude logos -->
              <div class="flex items-center justify-center">
                <img src="/claude.svg" alt="Claude" class="flex-none leading-none mr-3 h-8 w-8">
                <img src="/claude-logo-text.svg" class="h-8" alt="Claude Logo Text">
              </div>
            </CarouselItem>
            <CarouselItem class="basis-1/2 md:basis-1/3 pl-2 py-4">
              <!-- Mistral logos -->
              <div class="flex items-center justify-center">
                <img src="/mistral.svg" alt="Mistral" class="flex-none leading-none mr-3 h-8 w-8">
                <img src="/mistral-logo-text.svg" class="h-6" alt="Mistral Logo Text">
              </div>
            </CarouselItem>
            <CarouselItem class="basis-1/2 md:basis-1/3 pl-2 py-4">
              <!-- Meta logos -->
              <div class="flex items-center justify-center">
                <img src="/meta.svg" alt="Meta" class="flex-none leading-none mr-3 h-8 w-8">
                <img src="/meta-logo-text.svg" class="h-8" alt="Meta Logo Text">
              </div>
            </CarouselItem>
            <CarouselItem class="basis-1/2 md:basis-1/3 pl-2 py-4">
              <!-- Qwen logos -->
              <div class="flex items-center justify-center">
                <img src="/qwen.svg" alt="Qwen" class="flex-none leading-none mr-3 h-8 w-8">
                <img src="/qwen-logo-text.svg" class="h-8" alt="Qwen Logo Text">
              </div>
            </CarouselItem>
            <CarouselItem class="basis-1/2 md:basis-1/3 pl-2 py-4">
              <!-- DeepSeek logos -->
              <div class="flex items-center justify-center">
                <img src="/deepseek.svg" alt="Deepseek" class="flex-none leading-none mr-3 h-8 w-8">
                <img src="/deepseek-logo-text.svg" class="h-8" alt="Deepseek Logo Text">
              </div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      </div>
    </div>

    <!-- Testimonials Section -->
    <div class="max-w-screen-xl mx-auto px-4 md:px-12 py-16 md:py-24">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl">
          What Our Users Say
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto mt-4"></div>
        <p class="mt-4 text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Real stories from learners who've transformed their language journey
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Testimonial 1 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 transform transition duration-300 hover:-translate-y-2">
          <div class="flex items-center mb-4">
            <img src="/kenji.webp" alt="Kenji Tanaka" class="h-12 w-12 rounded-full object-cover bg-gray-200 dark:bg-gray-700" />
            <div class="ml-4">
              <h3 class="font-semibold text-lg text-gray-900 dark:text-white">Kenji Tanaka</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">Student - The University of Tokyo</p>
            </div>
          </div>
          <div class="relative">
            <svg class="absolute top-0 left-0 w-8 h-8 text-green-200 dark:text-green-800 transform -translate-x-4 -translate-y-4" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
              <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
            </svg>
            <p class="relative text-gray-700 dark:text-gray-300 italic">
              With old traditional dictionaries, I couldn't look up specific phrases, modern expressions, or idioms – they just weren't in there. But with Pickvocab's AI dictionary, I can truly look up anything – words, idioms, even the latest slang – and get a clear, real-world explanation. It has fundamentally changed how I approach learning!
            </p>
          </div>
        </div>

        <!-- Testimonial 2 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 transform transition duration-300 hover:-translate-y-2">
          <div class="flex items-center mb-4">
            <img src="/petrova.webp" alt="Anastasia Petrova" class="h-12 w-12 rounded-full object-cover bg-gray-200 dark:bg-gray-700" />
            <div class="ml-4">
              <h3 class="font-semibold text-lg text-gray-900 dark:text-white">Anastasia Petrova</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">Editor</p>
            </div>
          </div>
          <div class="relative">
            <svg class="absolute top-0 left-0 w-8 h-8 text-green-200 dark:text-green-800 transform -translate-x-4 -translate-y-4" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
              <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
            </svg>
            <p class="relative text-gray-700 dark:text-gray-300 italic">
              Finding the right meaning of a word in a tricky sentence used to take ages. With Pickvocab's contextual lookup, I get the exact meaning instantly, right there on the page. And saving it to my notebook is incredibly helpful for remembering new words!
            </p>
          </div>
        </div>
      </div>

      <!-- Call to Action -->
      <div class="mt-12 text-center">
        <NuxtLink :to="appUrl">
          <Button
            class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Start Learning with Pickvocab →
          </Button>
        </NuxtLink>
      </div>
    </div>
    <HomeFooter />
  </div>
</template>

<style scoped></style>
