<script setup lang="ts">
import {
  FwbNavbar,
  FwbNavbarCollapse,
} from "flowbite-vue";
import HomeFooter from "~/components/home/<USER>";
import HomeNavbar from '~/components/home/<USER>';
// @ts-ignore
import IconEdit from '@tabler/icons-vue/dist/esm/icons/IconEdit.mjs';
// @ts-ignore
import IconSparkles from '@tabler/icons-vue/dist/esm/icons/IconSparkles.mjs';
// @ts-ignore
import IconBook from '@tabler/icons-vue/dist/esm/icons/IconBook.mjs';
// @ts-ignore
import IconVocabulary from '@tabler/icons-vue/dist/esm/icons/IconVocabulary.mjs';
// @ts-ignore
import IconAdjustments from '@tabler/icons-vue/dist/esm/icons/IconAdjustments.mjs';
// @ts-ignore
import IconTrendingUp from '@tabler/icons-vue/dist/esm/icons/IconTrendingUp.mjs';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';


useSeoMeta({
  title: "AI Writing Assistant | Pickvocab",
  description: "Enhance English writing with real-time AI feedback. Fix grammar, improve style, and use vocabulary in context. Perfect for essays, emails, and professional writing.",
  keywords: "AI writing assistant, English grammar checker, vocabulary enhancer, academic writing help, professional email writing"
});
</script>

<template>
  <div>
    <HomeNavbar />

    <!-- Hero Section -->
    <div class="flex flex-col items-center mt-16 md:mt-24 py-10 px-4 md:px-8">
      <h1 class="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center">
        Transform Your Writing with <span class="text-blue-600 dark:text-blue-500">AI Assistance</span>
      </h1>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        Write confidently, master your vocabulary, and track your progress—all in one powerful tool
      </div>

      <div class="mt-8">
        <NuxtLink to="/app/write">
          <Button
            class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Try it now →
          </Button>
        </NuxtLink>
      </div>

      <!-- Writing Assistant Demo -->
      <div class="w-full mt-8 shadow md:w-3/4 max-w-[1024px] md:mt-8 border rounded-lg bg-gray-200 dark:bg-gray-700 h-64 md:h-96 flex flex-col items-center justify-center">
        <p class="text-gray-500 dark:text-gray-400 text-center px-4 mb-4">
          [Visual Placeholder: Writing assistant interface showing vocabulary suggestions]
        </p>

        <!-- Writing sample with schema markup -->
        <div itemscope itemtype="https://schema.org/Code" class="bg-white dark:bg-gray-600 p-4 rounded-lg max-w-md">
          <meta itemprop="programmingLanguage" content="English"/>
          <div class="text-sm text-gray-700 dark:text-gray-300">
            <p class="mb-2"><strong>Before:</strong> "I go to store yesterday"</p>
            <p><strong>After:</strong> "I went to the store yesterday"</p>
          </div>
          <pre class="hidden">
            Before: "I go to store yesterday"
            After: "I went to the store yesterday"
          </pre>
        </div>
      </div>
    </div>

    <!-- 1. Rewrite with Confidence -->
    <div class="flex flex-col md:flex-row items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 md:mt-32 py-10 px-4 md:px-12">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-full">
            <IconEdit class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Your Personal AI Editor
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Improve fluency, clarity, and grammar instantly</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Refine phrasing to make your writing shine</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Receive real-time feedback with easy-to-understand explanations</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Rewrite Visual -->
        <div class="flex items-center justify-center w-full bg-gray-200 dark:bg-gray-700 rounded-lg h-64 md:h-80">
          <p class="text-gray-500 dark:text-gray-400 text-center px-4">
            [Visual Placeholder: Before-and-after comparison of revised text]
          </p>
        </div>
      </div>
    </div>

    <!-- 2. Bring Your Vocabulary to Life -->
    <div class="flex flex-col md:flex-row-reverse items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 py-10 px-4 md:px-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-indigo-50 dark:bg-indigo-900 rounded-full">
            <IconVocabulary class="w-8 h-8 text-indigo-600 dark:text-indigo-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Use the Words You've Learned
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Seamlessly incorporate words from your Vocabulary Notebooks</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Get context-appropriate usage suggestions for saved words</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Reinforce vocabulary retention by using learned words in real writing</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Vocabulary Integration Visual -->
        <div class="flex items-center justify-center w-full bg-gray-200 dark:bg-gray-700 rounded-lg h-64 md:h-80">
          <p class="text-gray-500 dark:text-gray-400 text-center px-4">
            [Visual Placeholder: Animation showing vocabulary suggestions]
          </p>
        </div>
      </div>
    </div>

    <!-- 3. Write for Any Occasion -->
    <div class="flex flex-col md:flex-row items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 py-10 px-4 md:px-12">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-full">
            <IconAdjustments class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Adapt Your Writing Style
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Choose from academic, professional, casual, or custom tones</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Switch effortlessly between styles to match your writing needs</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Maintain a consistent voice throughout your text</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Tone Selector Visual -->
        <div class="flex items-center justify-center w-full bg-gray-200 dark:bg-gray-700 rounded-lg h-64 md:h-80">
          <p class="text-gray-500 dark:text-gray-400 text-center px-4">
            [Visual Placeholder: Interactive tone selector with examples]
          </p>
        </div>
      </div>
    </div>

    <!-- 4. See Your Progress -->
    <div class="flex flex-col md:flex-row-reverse items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 py-10 px-4 md:px-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-indigo-50 dark:bg-indigo-900 rounded-full">
            <IconTrendingUp class="w-8 h-8 text-indigo-600 dark:text-indigo-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Track Your Growth
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">View and compare all document revisions</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Track how often you use saved vocabulary words</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Identify underutilized vocabulary for focused practice</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Progress Dashboard Visual -->
        <div class="flex items-center justify-center w-full bg-gray-200 dark:bg-gray-700 rounded-lg h-64 md:h-80">
          <p class="text-gray-500 dark:text-gray-400 text-center px-4">
            [Visual Placeholder: Progress dashboard showing statistics]
          </p>
        </div>
      </div>
    </div>

    <!-- Integration Diagram Section -->
    <div class="max-w-screen-xl mx-auto px-4 md:px-12 py-16 md:py-24">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl">
          Learn → Practice → Remember
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto mt-4"></div>
        <p class="mt-4 text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Turn saved words into active vocabulary you can actually use
        </p>
      </div>

      <!-- Visual workflow -->
      <div class="flex flex-col md:flex-row items-center justify-center gap-4 md:gap-8 mb-8">
        <!-- Learning Step -->
        <div class="flex flex-col items-center text-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg w-full md:w-56 min-h-[160px] md:min-h-[200px]">
          <div class="mb-2 p-3 bg-white dark:bg-gray-800 rounded-full">
            <IconBook class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="1.5" />
          </div>
          <h3 class="font-semibold mb-2">1. Learn Words</h3>
          <p class="text-sm text-gray-600 dark:text-gray-300 px-2">
            Save from books/web with examples
          </p>
        </div>

        <IconArrowRight class="w-8 h-8 text-gray-400 hidden md:block" :stroke-width="1.5" />

        <!-- Practice Step -->
        <div class="flex flex-col items-center text-center p-4 bg-purple-50 dark:bg-purple-900 rounded-lg w-full md:w-56 min-h-[160px] md:min-h-[200px]">
          <div class="mb-2 p-3 bg-white dark:bg-gray-800 rounded-full">
            <IconEdit class="w-8 h-8 text-purple-600 dark:text-purple-400" :stroke-width="1.5" />
          </div>
          <h3 class="font-semibold mb-2">2. Write Better</h3>
          <p class="text-sm text-gray-600 dark:text-gray-300 px-2">
            Get suggestions from your saved words
          </p>
        </div>

        <IconArrowRight class="w-8 h-8 text-gray-400 hidden md:block" :stroke-width="1.5" />

        <!-- Mastery Step -->
        <div class="flex flex-col items-center text-center p-4 bg-green-50 dark:bg-green-900 rounded-lg w-full md:w-56 min-h-[160px] md:min-h-[200px]">
          <div class="mb-2 p-3 bg-white dark:bg-gray-800 rounded-full">
            <IconTrendingUp class="w-8 h-8 text-green-600 dark:text-green-400" :stroke-width="1.5" />
          </div>
          <h3 class="font-semibold mb-2">3. Remember Longer</h3>
          <p class="text-sm text-gray-600 dark:text-gray-300 px-2">
            Track words you actually use
          </p>
        </div>
      </div>


      <!-- Final CTA -->
      <div class="mt-32 text-center">
        <p class="mb-4 text-lg text-gray-600 dark:text-gray-300 italic">
          "We are what we repeatedly do. Excellence, then, is not an act, but a habit."<br>
          - <strong>Aristotle</strong>
        </p>
        <NuxtLink to="/app/write">
          <Button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 font-medium rounded-lg transition-all duration-200">
            Start Writing with Confidence →
          </Button>
        </NuxtLink>
      </div>
    </div>
    <HomeFooter />
  </div>
</template>

<style scoped></style>
