<script setup lang="ts">
import { authenticateGoogleUser } from '~/api/auth';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

onMounted(async () => {
  const { code } = route.query;
  const { key } = await authenticateGoogleUser(code as string);
  await authStore.signIn(key);
  const redirectPath = sessionStorage.getItem('redirectPath') || '/app';
  router.push(redirectPath);
  sessionStorage.removeItem('redirectPath');
});

</script>

<template>
  <div></div>
</template>

<style scoped>
</style>
