<script setup lang="ts">
import ReviewCardBackView from '~/components/app/reviews/ReviewCardBackView.vue';
import ReviewCardFrontView from '~/components/app/reviews/ReviewCardFrontView.vue';
import DangerAlert from '~/components/app/utils/DangerAlert.vue';

useSeoMeta({
  title: 'Review | Pickvocab'
});

definePageMeta({
  layout: 'app',
});

enum CardState {
  Front,
  Back
}

const store = useAppStore();

const route = useRoute();
const router = useRouter();
const review: Ref<Review | undefined> = ref();
const currentCardIdx: Ref<number> = ref(0);
const currentCardState: Ref<CardState> = ref(CardState.Front);
const isLoading = ref(false);

watch(() => route.params.id, async (value) => {
  if (Array.isArray(value)) throw new Error('Unexpected');
  isLoading.value = true;
  try {
    review.value = await store.getReview(Number(value));
  } finally {
    isLoading.value = false;
  }
}, { immediate: true });

function showDefinition() {
  currentCardState.value = CardState.Back;
}

function updateScore(delta: number) {
  review.value!.cards[currentCardIdx.value].deltaScore = delta;
  currentCardIdx.value += 1;
  currentCardState.value = CardState.Front;

  if (currentCardIdx.value >= review.value!.cards.length) {
    alert('All done!');
    store.updateScoreReview(review.value!);
    router.push({ path: '/app' });
  }
}

</script>

<template>
  <NuxtLayout name="app">
    <template v-if="review && review.cards.length > 0">
      <template v-if="currentCardIdx < review.cards.length">
        <ReviewCardFrontView @show-definition="showDefinition()" v-if="currentCardState === CardState.Front" :card="review.cards[currentCardIdx].card">
        </ReviewCardFrontView>
        <ReviewCardBackView @answer="updateScore" :card="review.cards[currentCardIdx].card" v-else>
        </ReviewCardBackView>
      </template>
    </template>
    <div v-else-if="!isLoading" class="sm:ml-64 pt-14 pl-10 pr-10">
      <DangerAlert
        class="mt-10"
        :label="'Error'"
        :message="'No cards available for review. Please create some cards first.'"
      />
    </div>

  </NuxtLayout>
</template>

<style scoped></style>
