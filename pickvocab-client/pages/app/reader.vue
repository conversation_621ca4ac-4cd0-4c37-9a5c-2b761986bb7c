<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Button } from '@/components/ui/button';
import RecentBooksDisplay from '@/components/app/epubReader/RecentBooksDisplay.vue';
import { useRecentBooks } from '~/components/app/epubReader/useRecentBooks';
import { PlusCircleIcon } from 'lucide-vue-next'; // XIcon removed
// Assume useBookReaderStore will be created in a subsequent task
import { useBookReaderStore } from '~/stores/bookReaderStore';

definePageMeta({
  layout: 'app',
});

useSeoMeta({
  title: 'Reading Hub | Pickvocab'
});

const router = useRouter();
const bookReaderStore = useBookReaderStore(); // Will be uncommented when store is available

const { getRecentBookById, addOrUpdateRecentBook, removeRecentBook } = useRecentBooks();

const showReaderOnboarding = ref(false);
const onboardingKey = 'pickvocab_readerPageOnboardingDismissed';

onMounted(() => {
  if (localStorage.getItem(onboardingKey) !== 'true') {
    showReaderOnboarding.value = true;
  }
});

const dismissReaderOnboarding = () => {
  localStorage.setItem(onboardingKey, 'true');
  showReaderOnboarding.value = false;
};

async function openFileWithPicker(): Promise<FileSystemFileHandle | null> {
  if (!('showOpenFilePicker' in window)) {
    alert('File System Access API is not supported in this browser. Please use a modern browser.');
    return null;
  }
  try {
    const [handle] = await window.showOpenFilePicker({
      types: [{ description: 'EPUB Files', accept: { 'application/epub+zip': ['.epub'] } }],
      multiple: false,
    });
    return handle;
  } catch (err) {
    if ((err as DOMException).name === 'AbortError') {
      console.log('User cancelled file picker.'); // Keep this
    } else {
      console.error('Error picking file:', err); // Keep this
    }
    return null;
  }
}

const openFileAndProcess = async () => {
  const handle = await openFileWithPicker();
  if (handle) {
    try {
      const file = await handle.getFile();
      const arrayBuffer = await file.arrayBuffer();

      bookReaderStore.setCurrentBookToRead({ data: arrayBuffer, name: file.name, handle: handle });
      console.log('EPUB file loaded via picker, ready to pass to store:', file.name); // Keep this operational log
      router.push('/app/read-book');
    } catch (error) {
      console.error('Error processing file from picker:', error); // Keep this
      // TODO: Show error to user
    }
  }
};

async function handleRecentBookSelect(bookId: string) {
  const entry = await getRecentBookById(bookId);

  if (!entry || !entry.fileHandle) {
    console.error('Recent book entry or file handle not found for ID:', bookId); // Keep
    alert('Could not open recent book. It might have been removed or an error occurred.');
    if (entry) await removeRecentBook(entry.id);
    return;
  }

  async function getFileFromHandle(handle: FileSystemFileHandle, entryId: string): Promise<File | null> {
    try {
      if (await handle.queryPermission({ mode: 'read' }) !== 'granted') {
        if (await handle.requestPermission({ mode: 'read' }) !== 'granted') {
          console.warn('Read permission denied for file handle:', handle.name); // Keep
          alert(`Permission to read the file "${handle.name}" was denied. Please grant permission to open it again, or it will be removed from recent books.`);
          await removeRecentBook(entryId);
          return null;
        }
      }
      return await handle.getFile();
    } catch (error) {
      console.error('Error accessing file from handle:', handle.name, error); // Keep
      alert(`Could not access the file "${handle.name}". It might have been moved or deleted. It will be removed from recent books.`);
      await removeRecentBook(entryId);
      return null;
    }
  }

  const file = await getFileFromHandle(entry.fileHandle, entry.id);

  if (file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      
      bookReaderStore.setCurrentBookToRead({ data: arrayBuffer, name: file.name, handle: entry.fileHandle });
      console.log(`Successfully prepared to re-open book via store: ${entry.filename}`); // Keep this
      router.push('/app/read-book');

      await addOrUpdateRecentBook({ ...entry, lastOpened: Date.now() });
    } catch (readError) {
      console.error('Error reading file for re-opening:', readError); // Keep
      alert('An error occurred while trying to read the book file.');
    }
  } else {
    console.warn(`Failed to get file for ${entry.filename}. It might have been removed from recent list.`); // Keep
  }
}
</script>

<template>
  <NuxtLayout name="app">
    <div class="sm:ml-64 mt-14 pt-10 pl-10 pr-10 xl:pr-48 flex flex-col h-full bg-background text-foreground">
      <main class="flex-grow w-full bg-card">
        <div class="flex flex-col items-center justify-start p-8 h-full overflow-y-auto">
          <div class="w-full max-w-4xl">
            <!-- Onboarding Message -->
            <Transition name="fade">
              <div
                v-if="showReaderOnboarding"
                class="mb-6 p-4 bg-blue-500 text-white rounded-lg shadow-lg flex items-center justify-between"
                role="alert"
              >
                <p class="text-sm">
                  Welcome to your Reading Hub! Click 'Open New Book' to upload an EPUB, or select a recent book to continue reading. While reading, select any word or phrase to look it up
                </p>
                <button
                  @click="dismissReaderOnboarding"
                  class="ml-4 px-3 py-1 bg-blue-700 hover:bg-blue-800 rounded text-sm font-semibold whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-blue-300"
                  aria-label="Dismiss onboarding message"
                >
                  Got it!
                </button>
              </div>
            </Transition>

            <!-- "Open New Book" Button -->
            <div class="flex mb-8">
              <Button @click="openFileAndProcess" size="lg" variant="outline" class="mx-auto sm:mx-0 px-6">
                <PlusCircleIcon class="mr-2 h-5 w-5" />
                Open New Book (.epub)
              </Button>
            </div>
            <!-- Recent Books Display -->
            <RecentBooksDisplay @select-book="handleRecentBookSelect" class="w-full max-w-4xl" />
          </div>
        </div>
      </main>
    </div>
  </NuxtLayout>
</template>

<style scoped>
/* Animations and transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Ensure other styles are not conflicting */
</style>