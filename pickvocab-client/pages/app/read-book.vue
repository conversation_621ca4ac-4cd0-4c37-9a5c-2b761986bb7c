<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import StandaloneEpubReader from '~/components/app/epubReader/StandaloneEpubReader.vue';
import { useBookReaderStore } from '~/stores/bookReaderStore';

definePageMeta({
  layout: 'reader',
});

useSeoMeta({
  title: 'Read Book | Pickvocab'
});

const bookReaderStore = useBookReaderStore();
const router = useRouter();

const epubData = ref<ArrayBuffer | null>(null);
const fileName = ref<string | null>(null);
const fileHandle = ref<FileSystemFileHandle | null>(null);
const showOnboardingMessage = ref(false);

const ONBOARDING_DISMISSED_KEY = 'pickvocab_epubReaderDictionaryOnboardingDismissed';

onMounted(async () => {
  const consumed = await bookReaderStore.consumeBookData();
  if (consumed && consumed.data) {
    epubData.value = consumed.data;
    fileName.value = consumed.name;
    fileHandle.value = consumed.handle;
  } else {
    console.warn("No book data found to read, redirecting to library.");
    router.replace('/app/reader');
  }
});

watch(epubData, (newValue) => {
  if (newValue) {
    if (typeof localStorage !== 'undefined') {
      const dismissed = localStorage.getItem(ONBOARDING_DISMISSED_KEY);
      if (dismissed !== 'true') {
        showOnboardingMessage.value = true;
      }
    }
  }
}, { immediate: true });

const dismissOnboardingMessage = () => {
  showOnboardingMessage.value = false;
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem(ONBOARDING_DISMISSED_KEY, 'true');
  }
};

const returnToLibrary = () => {
  epubData.value = null;
  fileName.value = null;
  fileHandle.value = null;
  router.push('/app/reader');
};
</script>

<template>
  <NuxtLayout name="reader">
    <div v-if="epubData" class="relative h-screen w-screen">
      <StandaloneEpubReader
        :key="fileName || '__read_book_key_fallback__'"
        :epub-data="epubData"
        :filename="fileName"
        :fileHandleProp="fileHandle"
        class="h-screen w-screen"
        @back-to-library="returnToLibrary"
      />
      <!-- Onboarding Message -->
      <div
        v-if="showOnboardingMessage"
        class="absolute top-4 left-1/2 -translate-x-1/2 w-11/12 max-w-md p-4 bg-blue-500 text-white rounded-lg shadow-lg flex items-center justify-between z-50"
      >
        <div class="text-sm">
          <strong>Tips for reading:</strong>
          <ul class="list-disc pl-5 mt-1 space-y-1">
            <li>Scroll to read through the current chapter.</li>
            <li>Use the navigation buttons to move between chapters.</li>
            <li>Select any word or phrase to look it up in the dictionary.</li>
          </ul>
        </div>
        <button
          @click="dismissOnboardingMessage"
          class="ml-4 px-3 py-1 bg-blue-700 hover:bg-blue-800 rounded text-sm font-semibold whitespace-nowrap"
          aria-label="Dismiss onboarding message"
        >
          Got it!
        </button>
      </div>
    </div>
    <div v-else class="flex items-center justify-center h-screen">
      <p>Loading reader...</p>
    </div>
  </NuxtLayout>
</template>
