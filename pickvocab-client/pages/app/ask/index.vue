<script setup lang="ts">
import type { ChatSource } from 'pickvocab-dictionary';
import ChatBubble from '~/components/app/ask/ChatBubble.vue';
import ChatInput from '~/components/app/ask/ChatInput.vue';
import { CommandKind, getPrompt } from '~/components/app/ask/command';
import DictionaryWordViewErrorAlert from '~/components/app/dictionary/DictionaryWordViewErrorAlert.vue';
import { 
  handleHelpCommand, 
  handleCompareCommand, 
  handleReviseCommand, 
  handleDefaultCommand,
  handleCheckCommand,
  type CommandHandlerContext 
} from '~/components/app/ask/handlers';
import { cleanup } from '~/components/app/ask/revise/index';
// @ts-ignore
import assistantImg from '/pickvocab.svg';
// @ts-ignore
import annonymousImg from '~/assets/annonymous.jpg';
// @ts-ignore
import IconMessagePlus from '@tabler/icons-vue/dist/esm/icons/IconMessagePlus.mjs';

useSeoMeta({
  title: 'Ask | Pickvocab'
});

definePageMeta({
  layout: 'app',
});

const route = useRoute();

const store = useAppStore();
const authStore = useAuthStore();
const llmStore = useLLMStore();
let user: Ref<User | undefined> = ref(undefined);
const currentMessage = ref('');
const errorMessage = ref('');

const historyList: Ref<{ role: 'user' | 'model', parts: string }[]> = ref([]);
let chatSource: ChatSource;

onMounted(async () => {
  user.value = authStore.currentUser;

  if (route.query.compare) {
    const compareWords = route.query.compare as string;
    const words = compareWords.split(',').map(w => w.split('-').join(' ').replace(/_/g, '-'));

    sendMessage(`/compare ${words.map(w => `"${w}"`).join(' ')}`);
  }

  // /chat?compare=consider,deem,so-as-to
});

// Clean up polling when component is unmounted
onUnmounted(() => {
  cleanup();
});

watch(() => llmStore.activeUserModel, () => {
  if (llmStore.activeUserModel) {
    chatSource = llmStore.createChatSource(llmStore.activeUserModel);
  } else {
    chatSource = llmStore.pickvocabChatSource;
  }
}, { immediate: true });

const collection = [
  {
    trigger: '/',
    values: [
      { key: 'revise', value: 'revise' },
      { key: 'check', value: 'check' },
      { key: 'compare', value: 'compare' },
      { key: 'help', value: 'help' },
    ]
  }
];

const chatUser = computed(() => {
  return {
    name: user.value ? getProfileName(user.value) : 'Anonymous',
    picture: user.value ? getProfilePicture(user.value) : annonymousImg
  };
});

const botUser = {
  name: 'Assistant',
  picture: assistantImg
}

const botWelcomeMessage = `
I'm your friendly English language assistant! I can help polish your grammar, enhance your writing, or answer any language questions you might have.

Try these simple commands:
✍️ \`/revise <your sentences>\` to get a more fluent version of your writing
✅ \`/check <your sentences>\` to spot any grammar errors
🔍 \`/compare <word_1> <word_2> ... <word_n>\` to see how different words are typically used
❓ \`/help\` to see this list of commands again

Or just start chatting with me like you would with a friend - I'm here for casual conversation too!
`;

function scrollToBottom() {
  const chatPanel = document.getElementById('chat-panel');
  chatPanel?.scrollTo(0, 0);
  // if (chatPanel?.scrollTop === 0) {
  //   chatPanel?.scrollTo(0, 0);
  // }
}

async function sendMessage(text: string) {
  try {
    if (!chatSource) throw new Error('Chat instance not initialized');

    const message = getPrompt(text);
    historyList.value.push({ role: 'user', parts: text });
    scrollToBottom();
    
    // Create the context object for command handlers
    const context: CommandHandlerContext = {
      chatSource,
      historyList: historyList.value,
      scrollToBottom,
      userText: text,
      message,
      authStore,
      store,
      llmStore,
      botWelcomeMessage
    };
    
    // Route to the appropriate handler based on command type
    let result;
    
    switch (message.command) {
      case CommandKind.Help:
        result = await handleHelpCommand(context);
        break;
      case CommandKind.Compare:
        result = await handleCompareCommand(context);
        break;
      case CommandKind.Revise:
        result = await handleReviseCommand(context);
        break;
      case CommandKind.Check:
        result = await handleCheckCommand(context);
        break;
      default:
        result = await handleDefaultCommand(context);
        break;
    }
    
    // Handle any errors from the command handler
    if (result.error) {
      throw result.error;
    }
    
    scrollToBottom();
  } catch (err) {
    errorMessage.value = `${err}`;
    console.error(err);
  }
}

async function submit(text: string) {
  currentMessage.value = text;
  await sendMessage(text);
}

async function retry() {
  await sendMessage(currentMessage.value);
}

function getUserFromHistory(history: { role: 'user' | 'model', parts: string }) {
  return history.role === 'user' ? chatUser.value : botUser;
}

function newChat() {
  // Clear the conversation history, keeping only the initial welcome message
  historyList.value = [];
  // Clear any error messages
  errorMessage.value = '';
  // Reset current message
  currentMessage.value = '';
  // Scroll to the bottom to show the welcome message
  scrollToBottom();
}

</script>

<template>
  <NuxtLayout name="app">
    <div class="w-full h-full box-border">
      <div class="sm:ml-64 pt-14 flex flex-col h-full">
        <div id="chat-panel" class="py-10 h-[90%] overflow-y-auto px-4 lg:px-10 flex flex-col-reverse">
          <div class="mb-auto">
            <ChatBubble :user="botUser" :message="botWelcomeMessage"></ChatBubble>
            <ChatBubble :user="getUserFromHistory(history)" :message="history.parts" v-for="(history, idx) in historyList"
              class="mt-8"></ChatBubble>
          </div>
          <!-- <ChatBubble :user="chatUser" class="mt-8"></ChatBubble>
          <ChatBubble :user="botUser" class="mt-8"></ChatBubble>
          <ChatBubble :user="chatUser" class="mt-8"></ChatBubble>
          <ChatBubble :user="botUser" class="mt-8"></ChatBubble> -->
        </div>
        <DictionaryWordViewErrorAlert v-if="errorMessage" class="mt-8 mx-4 lg:mx-10" @setup="store.showAPIKeyModal()"
          @retry="retry()" :message="errorMessage" :is-active-user-model="llmStore.activeUserModel ? true : false">
        </DictionaryWordViewErrorAlert>
        <!-- New chat button -->
        <div class="flex justify-center mb-4">
          <button @click="newChat" class="flex items-center gap-1.5 px-3 py-1.5 text-xs text-blue-600 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors border border-blue-100 dark:text-blue-400 dark:bg-gray-700 dark:hover:bg-gray-600 dark:border-blue-800 shadow-sm hover:shadow-md transition-shadow">
            <icon-message-plus stroke="1.5" class="w-4 h-4 text-blue-600 dark:text-blue-400"></icon-message-plus>
            <span class="whitespace-nowrap">New chat</span>
          </button>
        </div>
        <div class="mt-auto">
          <ChatInput :collection="collection" @submit="submit"></ChatInput>
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<style scoped></style>
