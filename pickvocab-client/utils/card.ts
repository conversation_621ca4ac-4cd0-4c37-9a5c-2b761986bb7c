import type { DefinitionDetails, WordEntry, WordInContextEntry } from "pickvocab-dictionary";

export interface BaseDefinitionCard {
  // id: number;
  referenceWord?: WordEntry;
  refDefIdx?: number;
  word: string;
  definition: Partial<DefinitionDetails>;
}

export type CardId = string | number;

export enum CardType {
  DefinitionCard,
  ContextCard
}

export type DefinitionCard = BaseDefinitionCard & {
  cardType: CardType.DefinitionCard,
  id: CardId,
  // definitionCardId: CardId,
  owner: number,
  createdAt?: string,
  updatedAt?: string
};

export interface BaseContextCard {
  wordInContext: WordInContextEntry;
}

export interface ContextCard extends BaseContextCard {
  cardType: CardType.ContextCard,
  id: CardId,
  // contextCardId: CardId,
  owner: number;
  createdAt?: string;
  updatedAt?: string;
}

export type Card = DefinitionCard | ContextCard;

export interface BaseDeck {
  cards: Card[];
  description?: string;
  name: string;
}

export type DeckId = string | number;

export type Deck = BaseDeck & {
  id: DeckId,
  totalCards: number,
  owner: number,
  isDemo: boolean
};
