// @ts-ignore
import annonymousImg from '~/assets/annonymous.jpg';

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  social_accounts?: {
    extra_data: Record<string, any>;
    provider: string;
    uid: string;
  }[];
}

export function getProfilePicture(user: User) {
  return user.social_accounts && user.social_accounts.length > 0 && user.social_accounts[0].provider === 'google'
    ? user.social_accounts[0].extra_data.picture : annonymousImg;
}

export function getProfileName(user: User) {
  return user.first_name && user.last_name ? `${user.first_name} ${user.last_name}`
    : user.username;
}
