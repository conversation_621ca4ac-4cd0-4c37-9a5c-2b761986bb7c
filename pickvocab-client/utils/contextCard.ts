import { split } from 'sentence-splitter';

export function reduceContext(
  text: string,
  selectedText: string,
  offset: number,
  windowSize = 1
): {
  text: string,
  selectedText: string,
  offset: number
} {
  if (!text || text.trim() === '') {
    // If the main text is empty, return the selected text as its own context with offset 0.
    return { text: selectedText, selectedText: selectedText, offset: 0 };
  }

  const sentences = split(text).filter((ele) => ele.type === 'Sentence');

  if (sentences.length === 0) {
    // If no sentences are found in the text (e.g., it's just a title or a very short fragment),
    // return the selected text as its own context with offset 0.
    return { text: selectedText, selectedText: selectedText, offset: 0 };
  }

  let startIdx = 0;
  for (let i = sentences.length - 1; i >= 0; i -= 1) {
    if (sentences[i].range[0] <= offset) {
      startIdx = i;
      break;
    }
  }

  // Ensure endIdx is valid. If findIndex returns -1 (not found), 
  // it might mean selectedText extends beyond the last sentence or is not found.
  // In such cases, we can cap it to the last sentence.
  let endIdx = sentences.findIndex((sentence) => sentence.range[1] >= offset + selectedText.length);
  if (endIdx === -1) {
    endIdx = sentences.length - 1; 
  }

  const from = Math.max(0, startIdx - windowSize);
  const to = Math.min(endIdx + windowSize, sentences.length - 1);

  // Additional guard: ensure `from` is not greater than `to` after adjustments, 
  // and that they are valid indices for the sentences array.
  if (from > to || from < 0 || to >= sentences.length) {
    // Fallback if indices become invalid. This might happen with very short texts or unusual selections.
    // Return selected text as its own context.
    // console.warn('reduceContext: Invalid sentence indices, returning selected text as context.');
    return { text: selectedText, selectedText: selectedText, offset: 0 };
  }

  return {
    text: text.slice(sentences[from].range[0], sentences[to].range[1]),
    selectedText,
    offset: offset - sentences[from].range[0]
  }
}
