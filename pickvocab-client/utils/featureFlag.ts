export interface FeatureFlag {
  name: string;
  isActive: boolean;
  exceptUsers: number[];
  createdAt: string;
  updatedAt: string;
}

export function isFeatureFlagEnabled(flag: FeatureFlag, userId: number): boolean {
  if (userId === undefined) {
    return flag.isActive; // Default behavior if no user ID is provided
  }

  const isUserExcepted = flag.exceptUsers.includes(userId);

  if (flag.isActive) {
    return !isUserExcepted; // Active unless in exceptUsers
  } else {
    return isUserExcepted; // Inactive unless in exceptUsers
  }
}