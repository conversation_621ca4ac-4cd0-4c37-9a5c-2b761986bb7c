import { type SelectedToneIdentifier } from '~/stores/toneStore';

/**
 * Type guard to check if an object is a valid SelectedToneIdentifier
 */
export function isSelectedToneIdentifier(obj: any): obj is SelectedToneIdentifier {
  return obj && 
    typeof obj === 'object' && 
    (obj.type === 'predefined' || obj.type === 'custom') && 
    obj.identifier !== undefined;
}

/**
 * Parses a tone identifier from various possible formats
 * Returns a valid SelectedToneIdentifier or null if parsing fails
 */
export function parseToneIdentifier(value: unknown): SelectedToneIdentifier | null {
  if (!value) return null;
  
  // Already a valid identifier
  if (isSelectedToneIdentifier(value)) {
    return value;
  }
  
  // Try to parse from string (legacy format)
  if (typeof value === 'string') {
    try {
      const parsed = JSON.parse(value);
      if (isSelectedToneIdentifier(parsed)) {
        return parsed;
      }
      // If parsed but not valid, return null
      return null;
    } catch {
      // If it's a plain string, assume it's a predefined tone name
      return { type: 'predefined', identifier: value };
    }
  }
  
  return null;
}

/**
 * Gets default tone colors to use when a tone can't be resolved
 */
export function getDefaultToneColors() {
  return { bgColor: 'bg-gray-100', textColor: 'text-gray-800' };
}

/**
 * Resolves a tone name from an identifier
 * @param selectedTone The tone identifier in any format
 * @param customToneMap A Map of custom tone IDs to their names
 * @returns The display name for the tone
 */
export function resolveToneName(
  selectedTone: unknown, 
  customToneMap?: Map<number, string>
): string {
  if (!selectedTone) return '';
  
  const parsed = parseToneIdentifier(selectedTone);
  if (!parsed) {
    // Fallback: show as string
    return typeof selectedTone === 'string' ? selectedTone : JSON.stringify(selectedTone);
  }
  
  if (parsed.type === 'predefined') {
    return String(parsed.identifier);
  }
  
  if (parsed.type === 'custom') {
    const toneId = Number(parsed.identifier);
    if (customToneMap && customToneMap.has(toneId)) {
      return customToneMap.get(toneId) || `Custom tone #${toneId}`;
    }
    return `Custom tone #${toneId}`;
  }
  
  return '';
} 