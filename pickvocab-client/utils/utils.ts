import type { PartOfSpeech } from "pickvocab-dictionary";

export function stylesForPartOfSpeech(partOfSpeech: PartOfSpeech) {
  switch (partOfSpeech) {
    case 'verb':
      return ['border-red-400', 'text-red-400'];

    case 'noun':
      return ['border-green-400', 'text-green-400'];

    case 'adjective':
      return ['border-blue-400', 'text-blue-400'];

    default:
      return ['border-purple-400', 'text-purple-400'];
  }
}