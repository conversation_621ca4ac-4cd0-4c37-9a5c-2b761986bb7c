# Decision Log - PickVocab

[2025-04-06 10:41:00] - Explicitly disable DEBUG and DEVELOPMENT flags in Docker Compose files (`docker-compose.yml`, `docker-compose-arm.yml`) for backend, celery_worker, and flower services.
  - **Rationale:** Ensure production-like settings (`DEBUG=False`, `DEVELOPMENT=False`) are enforced in the Docker environment, overriding any values potentially loaded from `.env` files included in the image build. This enhances security and aligns with production best practices.
  - **Implications:** Development/debugging features controlled by these flags (e.g., detailed error pages, Django Silk) will be disabled when running via Docker Compose, even if the `.env` file within the image has them set to True.