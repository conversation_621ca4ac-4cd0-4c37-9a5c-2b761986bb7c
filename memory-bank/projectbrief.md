# Project Brief - PickVocab

## Project Overview
PickVocab is a vocabulary learning application with client, server, and web extension components. The system uses Docker for containerized deployment.

## Core Objectives
- Help users learn and expand their vocabulary
- Provide interfaces via web app and browser extension
- Offer backend services for vocabulary management

## Architecture Components
- Frontend client (Nuxt.js-based)
- Backend server
- Web browser extension
- Reverse proxy for routing

## Deployment Strategy
Docker-based deployment with configurations for different architectures.
