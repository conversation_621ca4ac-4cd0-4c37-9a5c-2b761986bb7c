# Active Context - PickVocab

## Current Focus
- Documentation of deployment architecture and processes
- Understanding system components relationships in detail
- Cataloging technical dependencies and infrastructure
- Mapping the automated deployment workflow

## Recent Changes
- Updated Memory Bank with detailed technical context
- Documented comprehensive system architecture
- Identified all major components and their roles
- Mapped deployment processes for production and development
- [2025-04-06 10:41:00] Explicitly set `DEBUG=False` and `DEVELOPMENT=False` in `docker-compose.yml` and `docker-compose-arm.yml` for backend-related services.
- [2025-04-06 10:41:00] Updated backend image tag to `v1.7.1` in `docker-compose.yml` and `docker-compose-arm.yml`.
- [2025-04-07 16:32:00] - Modified OutputSection.vue and index.vue in the Write Assistant to display a loading spinner while LLM revision is in progress.
- [2025-04-07 16:59:00] - Refactored VocabularyCard.vue to reuse DefinitionCardEntry and ContextCardEntry components for displaying different card types.
- [2025-04-07 17:05:00] - Corrected type definitions for `usedVocabularyCards` prop across `useWriteRevisionHandler.ts`, `index.vue`, `OutputSection.vue`, and `UsedVocabularyList.vue` to consistently use the base `Card` type.
- [2025-04-07 17:57:00] - Modified `useWriteRevisionHandler.ts` to use the active user LLM model and create a fresh `ChatSource` for each revision request.
## Next Steps
- Investigate API contracts between components
- Document authentication flows, especially Google OAuth integration
- Explore vocabulary learning features and algorithms
- Examine browser extension functionality in detail
- Understand data models and database schema

## Active Decisions
- Memory Bank structure enhanced with detailed technical information
- Documentation now includes deployment processes and architecture
- System architecture documented with component relationships
- Deployment patterns clearly defined

## Key Patterns and Preferences
- Docker-based microservices architecture
- Ansible automation for deployment
- Multi-environment support (development, production)
- Architecture adaptation for different processor architectures (ARM, x86)
- Django backend with Celery for async processing
- Nuxt.js frontend with modern web technologies
- Google OAuth integration for authentication
- Browser extension for vocabulary capture
