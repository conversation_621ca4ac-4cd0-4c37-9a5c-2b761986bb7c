# System Patterns - PickVocab

## System Architecture
PickVocab consists of multiple components working together in a containerized microservices architecture:

1. **Frontend Client (Nuxt.js)**
   - Provides the user interface for vocabulary learning
   - Communicates with backend via REST API
   - Runs in its own Docker container
   - Implements responsive UI for dictionary lookups and vocabulary management
   - Features a multi-language dictionary interface with dynamic language switching

2. **Backend Server (Django)**
   - Implements REST API endpoints for vocabulary management
   - Handles authentication and user management
   - Integrated with Google OAuth for authentication
   - Uses Celery for asynchronous task processing
   - Stores data in PostgreSQL database

3. **Browser Extension**
   - Allows users to capture vocabulary while browsing the web
   - Communicates with the backend API
   - Supports Chrome browser

4. **Database Layer**
   - PostgreSQL with pgvector extension
   - Stores user data, vocabulary items, and application state
   - Supports backup and restore operations
   - Stores word definitions, translations, and multi-language support data

5. **Redis**
   - Message broker for Celery task queue
   - Used for caching and temporary data storage

6. **Reverse Proxy (Nginx)**
   - Routes requests to appropriate services
   - Handles SSL termination
   - Manages subdomains and domains

## Component Interaction
- C<PERSON> communicates with backend through REST API calls
- Browser extension syncs vocabulary data with the backend
- Celery workers process asynchronous tasks from the backend
- Flower dashboard monitors Celery task execution

## Deployment Pattern
- All components deployed as Docker containers
- Orchestration via Docker Compose
- Automated deployment using Ansible playbooks
- Separate configurations for different environments and architectures

## Dictionary System Architecture
The dictionary functionality has these key components:

1. **Multi-language Support**
   - Built-in support for multiple languages including English, Vietnamese, Spanish, Chinese, etc.
   - Uses dynamic language detection and routing via URL structure
   - Implements language-specific route handling with Nuxt.js dynamic routes

2. **Dictionary API Layer**
   - RemoteDictionaryApi class interfaces with backend dictionary services
   - Dictionary class abstracts over multiple dictionary sources (LLM models)
   - Implements methods for word lookups and translations

3. **LLM Integration**
   - Uses multiple AI language models including OpenAI (GPT models), Google Gemini, Groq, Cerebras, and other providers for generating definitions and translations
   - Support for switching between different LLM providers based on availability and performance
   - Dictionary sources are abstracted through a common interface
   - DictionarySource interface allows for pluggable model providers
   - Specialized prompts for generating accurate translations and definitions
   - Model fallback mechanisms ensure service reliability

4. **Language Processing**
   - Dynamic language handling through locale-codes library integration
   - Language selection UI for switching between dictionary languages
   - Language-specific SEO metadata and URL structures
   - Standardized data structure for storing multi-language definitions
