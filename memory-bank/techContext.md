# Technical Context - PickVocab

## Technologies Used
- Frontend: Nuxt.js (client application)
- Backend: Django with Celery for async tasks
- Database: PostgreSQL with pgvector extension
- Browser Extension: Chrome extension for capturing vocabulary while browsing
- Redis: For Celery task queue and caching
- Docker: For containerization and deployment
- Nginx: Reverse proxy for routing and managing requests
- Ansible: For automated deployment
- Vagrant: For local development environment

## Development Setup
- Docker for containerized development environment
- Docker Compose for multi-container orchestration
- Separate configurations for ARM (`docker-compose-arm.yml`) and standard architectures (`docker-compose.yml`)
- Environment configurations via environment variables in Docker Compose
- Vagrant for creating local VM environments

## Dependencies
- Node.js ecosystem (package.json, pnpm)
- pnpm workspace setup (pnpm-workspace.yaml)
- Django backend with Celery
- PostgreSQL with pgvector for database
- Redis for message broker
- Flower for Celery task monitoring

## Deployment
- Docker-based containerized deployment
- Ansible playbooks for automated deployment
- Production deployment on a VPS (OVH)
- Multiple deployment environments supported
- Cloudflare integration for security
- Domain/subdomain setup:
  - pickvocab.com (main site)
  - app.pickvocab.com (application)
  - api.pickvocab.com (API endpoints)
  - flower.pickvocab.com (Celery monitoring)
- Deployment process:
  1. Build and version Docker images locally
  2. Save images to tar files
  3. Deploy with Ansible playbooks
  4. Database backup and restoration procedures
- Local development using Vagrant VM
