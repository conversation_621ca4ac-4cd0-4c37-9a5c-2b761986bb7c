import fs from 'fs';
import fsPromises from 'fs/promises';
import path from 'path';
import slugify from 'slugify';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename); // get the name of the directory

function run() {
  const entries = fs.readdirSync(path.resolve(__dirname, '../results'));
  const urls = entries.map((entry) => {
    let content = fs.readFileSync(path.resolve(__dirname, '../results', entry), 'utf-8');
    const word = JSON.parse(content);
    return {
      loc: `https://pickvocab.com/dictionary/vi/${slugify(word.word.replaceAll(/['()/!]/g, '-'), { strict: true, lower: true })}`,
      lastmod: '2024-11-29',
      changefreq: 'monthly',
      priority: 0.7
    }
  });
  fs.writeFileSync(path.resolve(__dirname, '../sitemap/wordUrlsVn.json'), JSON.stringify(urls, null, 2));
}

run();
