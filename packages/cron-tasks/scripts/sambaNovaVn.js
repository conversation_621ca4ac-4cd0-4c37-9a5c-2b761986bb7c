import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import shell from 'shelljs';
import { fileURLToPath } from 'url';
import slugify from 'slugify';
import dotenv from 'dotenv';
import { createPickvocabSource2 } from './llm.js';
import { Dictionary } from 'pickvocab-dictionary';

dotenv.config();

const pickvocabSource = createPickvocabSource2(process.env.GOOGLE_API_KEY_1, process.env.GOOGLE_API_KEY_2, process.env.SAMBANOVA_API_KEY_1);
const dictionary = new Dictionary([pickvocabSource]);

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename); // get the name of the directory

const files = fs.readdirSync(path.resolve(__dirname, '../results'));
files.reverse();

let count = 0;

function commitAndPush() {
  try {
    shell.exec('git add .');
    shell.exec('git commit -m "Update dictionary cron results"');
    shell.exec('git push origin master');
    console.log(chalk.green('Pushed to GitHub'));
  } catch (err) {
    console.log(err);
  }
}

async function run(files) {
  function existLanguageDefinition(word, language) {
    for (let i = 0; i < word.definitions.length; i++) {
      if (!word.definitions[i].languages?.[language] || word.definitions[i].languages?.[language].definition === undefined) {
        return false;
      }
    }
    return true;
  }

  function next() {
    while (files.length > 0) {
      const file = files.pop();
      try {
        if (fs.existsSync(path.resolve(__dirname, `../results/${file}`))) {
          const word = JSON.parse(fs.readFileSync(path.resolve(__dirname, `../results/${file}`), 'utf-8'));
          if (existLanguageDefinition(word, 'Vietnamese')) {
            console.log(chalk.blue(`Skipping: ${word.word}`));
            continue;
          }
          return { word, file };
        }
      } catch (err) {
        console.log('Failed to get next word: ', file);
        console.log(err);
      }
    }
  }

  let errorConsecutiveCnt = 0;

  while (files.length > 0) {
    const entry = next();
    if (!entry) break;
    try {
      const result = await dictionary.listAllMeaningsForLanguage(entry.word, 'Vietnamese');

      fs.writeFileSync(path.resolve(__dirname, `../results/${slugify(result.word.word)}.json`), JSON.stringify(result.word, null, 2));
      console.log(chalk.green(`Done: ${result.word.word}`));

      count += 1;
      errorConsecutiveCnt = 0;

      if (count === 30) {
        commitAndPush();
        count = 0;
      }
    } catch (error) {
      console.log(chalk.red(`Failed to process entries: ${entry.word.word}`));
      console.log(error);
      errorConsecutiveCnt += 1;
      if (errorConsecutiveCnt < 3) {
        files.push(entry.file);
      }
    } finally {
      await sleep(7000);
    }
  }
  commitAndPush();
  console.log('DONE');
}

async function main() {
  run(files);
}

main();
