import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import shell from 'shelljs';
import { fileURLToPath } from 'url';
import slugify from 'slugify';
import dotenv from 'dotenv';
import { createPickvocabSource2 } from './llm.js';
import { Dictionary } from 'pickvocab-dictionary';

dotenv.config();

const pickvocabSource = createPickvocabSource2(process.env.GOOGLE_API_KEY_1, process.env.GOOGLE_API_KEY_2, process.env.SAMBANOVA_API_KEY_1);
const dictionary = new Dictionary([pickvocabSource]);

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename); // get the name of the directory

const words1 = fs.readFileSync(path.resolve(__dirname, '../data/phrasal_verbs.txt'), 'utf-8').split('\n');
const words2 = fs.readFileSync(path.resolve(__dirname, '../data/academic_20k.txt'), 'utf-8').split('\n');
const words3 = fs.readFileSync(path.resolve(__dirname, '../data/phrases.txt'), 'utf-8').split('\n');
words1.reverse();
words2.reverse();
words3.reverse();

let count = 0;

function commitAndPush() {
  try {
    shell.exec('git add .');
    shell.exec('git commit -m "Update dictionary cron results"');
    shell.exec('git push origin master');
    console.log(chalk.green('Pushed to GitHub'));
  } catch (err) {
    console.log(err);
  }
}

async function run(words) {
  function next() {
    while (words.length > 0) {
      const word = words.pop();
      try {
        if (fs.existsSync(path.resolve(__dirname, `../results/${slugify(word)}.json`))) {
          console.log(chalk.blue(`Skipping: ${word}`));
          continue;
        }
        return word;
      } catch (err) {
        console.log('Failed to get next word: ', word);
        console.log(err);
      }
    }
  }

  let errorConsecutiveCnt = 0;

  while (words.length > 0) {
    const entry = next();
    if (!entry) break;
    try {
      const result = await dictionary.lookupWord(entry);

      fs.writeFileSync(path.resolve(__dirname, `../results/${slugify(result.word)}.json`), JSON.stringify(result, null, 2));
      console.log(chalk.green(`Done: ${result.word}`));

      count += 1;
      errorConsecutiveCnt = 0;

      if (count === 30) {
        commitAndPush();
        count = 0;
      }
    } catch (error) {
      console.log(chalk.red(`Failed to process entries: ${entry}`));
      console.log(error);
      errorConsecutiveCnt += 1;
      if (errorConsecutiveCnt < 3) {
        words.push(entry);
      }
    } finally {
      await sleep(7000);
    }
  }
  commitAndPush();
  console.log('DONE');
}

async function main() {
  await run(words3);
  await run(words2);
  await run(words1);
}

main();
