import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import shell from 'shelljs';
import { fileURLToPath } from 'url';
import slugify from 'slugify';
import dotenv from 'dotenv';
import { createPickvocabSource } from './llm.js';
import { Dictionary } from 'pickvocab-dictionary';

dotenv.config();

const pickvocabSource = createPickvocabSource(process.env.GOOGLE_API_KEY_1, process.env.GROQ_API_KEY_1);
const dictionary = new Dictionary([pickvocabSource]);

const pickvocabSource2 = createPickvocabSource(process.env.GOOGLE_API_KEY_2, process.env.GROQ_API_KEY_2);
const dictionary2 = new Dictionary([pickvocabSource2]);

const pickvocabSource3 = createPickvocabSource(undefined, process.env.GROQ_API_KEY_3);
const dictionary3 = new Dictionary([pickvocabSource3]);

const dictionaries = [dictionary, dictionary2, dictionary3];


function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename); // get the name of the directory

const oxford3kFile = fs.readFileSync(path.resolve(__dirname, '../data/academic_5-21k.txt'), 'utf-8');

const words = oxford3kFile.toString().split('\n');
const words2 = fs.readFileSync(path.resolve(__dirname, '../data/phrases.txt'), 'utf-8').split('\n');
const words3 = fs.readFileSync(path.resolve(__dirname, '../data/academic_5k.txt'), 'utf-8').split('\n');
words.reverse();
words2.reverse();
words3.reverse();

let count = 0;

function commitAndPush() {
  try {
    shell.exec('git add .');
    shell.exec('git commit -m "Update dictionary cron results"');
    shell.exec('git push origin master');
    console.log(chalk.green('Pushed to GitHub'));
  } catch (err) {
    console.log(err);
  }
}

function next1() {
  while (words.length > 0) {
    const word = words.pop();
    try {
      if (fs.existsSync(path.resolve(__dirname, `../results/${slugify(word)}.json`))) {
        console.log(chalk.blue(`Skipping: ${word}`));
        continue;
      }
      return word;
    } catch (err) {
      console.log('Failed to get next word: ', word);
      console.log(err);
    }
  }
}

function next2() {
  while (words2.length > 0) {
    const word = words2.pop();
    try {
      if (fs.existsSync(path.resolve(__dirname, `../results/${slugify(word)}.json`))) {
        console.log(chalk.blue(`Skipping: ${word}`));
        continue;
      }
      return word;
    } catch (err) {
      console.log('Failed to get next word 2: ', word);
      console.log(err);
    }
  }
}

function next3() {
  while (words3.length > 0) {
    const word = words3.pop();
    try {
      if (fs.existsSync(path.resolve(__dirname, `../results/${slugify(word)}.json`))) {
        console.log(chalk.blue(`Skipping: ${word}`));
        continue;
      }
      return word;
    } catch (err) {
      console.log('Failed to get next word 3: ', word);
      console.log(err);
    }
  }
}

function next() {
  return [next1(), next2(), next3()].filter(Boolean);
}

async function run() {
  while (words.length > 0 || words2.length > 0 || words3.length > 0) {
    const entries = next();
    try {
      const results = await Promise.allSettled(entries.map((entry, idx) => dictionaries[idx].lookupWord(entry)));
      results.forEach((result, idx) => {
        if (result.status === 'fulfilled') {
          fs.writeFileSync(path.resolve(__dirname, `../results/${slugify(result.value.word)}.json`), JSON.stringify(result.value, null, 2));
          console.log(chalk.green(`Done: ${result.value.word}`));
        } else {
          console.log(chalk.red(`Failed to process word: ${entries[idx]}`));
          console.log(result.reason);
        }
      });

      count += 1;
      if (count === 10) {
        commitAndPush();
        count = 0;
      }
    } catch (error) {
      console.log(chalk.red(`Failed to process entries: ${entries}`));
      console.log(error);
    } finally {
      await sleep(7000);
    }
  }
  console.log('DONE');
}

run();