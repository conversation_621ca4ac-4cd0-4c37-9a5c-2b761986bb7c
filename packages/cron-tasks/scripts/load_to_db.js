import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename); // get the name of the directory

async function loadToDB() {
  axios.defaults.headers.common['Authorization'] = 'Token 92c7006eeb2f4b9e8d7df76e7b31b1060c35a4b7';

  const entries = fs.readdirSync(path.join(__dirname, '../results'));
  const files = entries.filter(entry => entry.endsWith('.json'));

  for (const file of files) {
    const data = fs.readFileSync(path.join(__dirname, '../results', file), 'utf8');
    const word = JSON.parse(data);
    word.llm_model = 25;
    word.is_verified = true;

    console.log(`Sending word: ${word.word}`);
    const response = await axios.post('http://127.0.0.1:8000/words/', word);
    console.log(`Received response: ${response.status}`);
  }
}

loadToDB();
