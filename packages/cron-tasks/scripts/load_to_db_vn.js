import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename); // get the name of the directory

axios.defaults.headers.common['Authorization'] = 'Token 92c7006eeb2f4b9e8d7df76e7b31b1060c35a4b7';
axios.defaults.baseURL = 'http://127.0.0.1:8000';

async function getLLMModels () {
  const llmModels = (await axios.get('/llm_models/')).data;

  const map = {};
  for (const llmModel of llmModels) {
    map[llmModel.id] = llmModel;
  }
  return map;
}

async function getWords (word, llmModels) {
  let response = await axios.get('/words/', { params: { word } });
  let words = response.data.results;
  while (response.data.next) {
    response.data = (await axios.get(response.data.next)).data;
    words = words.concat(response.data.results);
  }
  words = words.filter((word) => word.llm_model);

  words.sort((a, b) => {
    const scoreA = llmModels[a.llm_model].arena_score;
    const scoreB = llmModels[b.llm_model].arena_score;
    return scoreB - scoreA;
  });

  return words;
}

async function loadToDB() {
  try {
    const llmModels = await getLLMModels();

    const entries = fs.readdirSync(path.join(__dirname, '../results'));
    const files = entries.filter(entry => entry.endsWith('.json'));

    for (const file of files) {
      const data = fs.readFileSync(path.join(__dirname, '../results', file), 'utf8');
      const word = JSON.parse(data);

      console.log(`Processsing word: ${word.word}`);

      const remoteWords = await getWords(word.word, llmModels);

      const changedWords = [];
      remoteWords.forEach((remoteWord) => {
        let remoteWordChanged = false;
        remoteWord.definitions.forEach((definition, idx) => {
          if (
            (!definition.languages || !definition.languages.Vietnamese)
            && idx < word.definitions.length
            && definition.definition === word.definitions[idx].definition 
            && definition.context === word.definitions[idx].context
          ) {
            remoteWord.definitions[idx].languages = {
              ...word.definitions[idx].languages,
              ...remoteWord.definitions[idx].languages,
            };
            remoteWordChanged = true;
          }
        });
        if (remoteWordChanged) {
          changedWords.push(remoteWord);
        }
      });

      if (changedWords.length > 0) {
        for (const remoteWord of changedWords) {
          console.log(`Sending word: ${word.word}`);
          const response = await axios.put(`/words/${remoteWord.id}/`, remoteWord);
          console.log(`Received response: ${response.status}`);
        }
      } else {
        console.log(`No changes for word: ${word.word}`);
      }
    }
  } catch (err) {
    console.log(err);
  }
}

loadToDB();
