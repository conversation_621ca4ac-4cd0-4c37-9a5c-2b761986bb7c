import {
  PickvocabSource,
  GeminiSource, GroqSource,
  SambaNovaSource
} from 'pickvocab-dictionary';

export function createPickvocabSource (googleApiKey, groqApiKey) {
  let source1 = [];
  if (googleApiKey) {
    source1.push(new GeminiSource({
      modelId: 'gemini-1.5-flash-latest',
      modelName: 'gemini-1.5-flash-latest',
      apiKey: googleApiKey,
    }));
  }
  source1 = source1.concat([
    new GroqSource({
      modelId: 'llama-3.2-90b-text-preview',
      modelName: 'llama-3.2-90b-text-preview',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llama-3.1-70b-versatile',
      modelName: 'llama-3.1-70b-versatile',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llama3-70b-8192',
      modelName: 'llama3-70b-8192',
      apiKey: groq<PERSON><PERSON><PERSON><PERSON>,
    }),
    new GroqSource({
      modelId: 'llama3-groq-70b-8192-tool-use-preview',
      modelName: 'llama3-groq-70b-8192-tool-use-preview',
      apiKey: groqApiKey,
    }),
  ]);

  return new PickvocabSource(source1, [
    new GroqSource({
      modelId: 'llama-3.2-11b-text-preview',
      modelName: 'llama-3.2-11b-text-preview',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llama-3.2-11b-vision-preview',
      modelName: 'llama-3.2-11b-vision-preview',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llama-3.1-8b-instant',
      modelName: 'llama-3.1-8b-instant',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'gemma2-9b-it',
      modelName: 'gemma2-9b-it',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'gemma-7b-it',
      modelName: 'gemma-7b-it',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llama3-8b-8192',
      modelName: 'llama3-8b-8192',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llama-guard-3-8b',
      modelName: 'llama-guard-3-8b',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llama3-groq-8b-8192-tool-use-preview',
      modelName: 'llama3-groq-8b-8192-tool-use-preview',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llama-3.2-3b-preview',
      modelName: 'llama-3.2-3b-preview',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llama-3.2-1b-preview',
      modelName: 'llama-3.2-1b-preview',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'llava-v1.5-7b-4096-preview',
      modelName: 'llava-v1.5-7b-4096-preview',
      apiKey: groqApiKey,
    }),
    new GroqSource({
      modelId: 'mixtral-8x7b-32768',
      modelName: 'mixtral-8x7b-32768',
      apiKey: groqApiKey,
    }),
  ], {
    modelId: 1,
    modelName: 'pickvocab-public'
  });
}

export function createPickvocabSource2 (googleApiKey, googleApiKey2, sambaNovaApiKey) {
  let source1 = [];
  if (googleApiKey) {
    source1.push(new GeminiSource({
      modelId: 'gemini-1.5-flash-latest',
      modelName: 'gemini-1.5-flash-latest',
      apiKey: googleApiKey,
    }));
    source1.push(new GeminiSource({
      modelId: 'gemini-1.5-flash-latest',
      modelName: 'gemini-1.5-flash-latest',
      apiKey: googleApiKey2,
    }));
  }
  source1 = source1.concat([
    new SambaNovaSource({
      modelId: 'Meta-Llama-3.1-70B-Instruct',
      modelName: 'Meta-Llama-3.1-70B-Instruct',
      apiKey: sambaNovaApiKey,
    }),
  ]);

  return new PickvocabSource(source1, [
    new SambaNovaSource({
      modelId: 'Meta-Llama-3.1-8B-Instruct',
      modelName: 'Meta-Llama-3.1-8B-Instruct',
      apiKey: sambaNovaApiKey,
    }),
  ], {
    modelId: 1,
    modelName: 'pickvocab-public'
  });
}
