import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename); // get the name of the directory

const phrasalVerbs = JSON.parse(fs.readFileSync(path.join(__dirname, '../data/phrasal_verbs.json'), 'utf8'));

fs.writeFileSync(path.join(__dirname, '../data/phrasal_verbs.txt'), Object.keys(phrasalVerbs).map((key) => key).join('\n'));