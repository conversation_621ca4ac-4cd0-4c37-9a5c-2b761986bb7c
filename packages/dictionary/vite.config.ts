import path from 'path';
import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';

export default defineConfig({
  // esbuild: {
  //   minifyIdentifiers: false,
  //   keepNames: true,
  // },
  build: {
    lib: {
      entry: [
        path.resolve(__dirname, 'src/index.ts'),
      ],
      name: 'dictionary',
      // formats: ['es', 'cjs'],
    },
    rollupOptions: {
      external: ['fs', 'path'], // externalize node's modules for src/node
    },
  },
  plugins: [dts()],
});