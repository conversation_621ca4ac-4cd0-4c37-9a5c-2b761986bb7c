{"name": "pickvocab-dictionary", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "preview": "vite preview"}, "main": "./dist/index.umd.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.umd.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist"], "devDependencies": {"@types/lodash": "^4.17.0", "vite": "^5.4.8"}, "dependencies": {"@anthropic-ai/sdk": "^0.27.1", "@google/generative-ai": "^0.17.1", "@mistralai/mistralai": "^1.4.0", "axios": "^1.6.8", "groq-sdk": "^0.7.0", "jsonrepair": "^3.6.0", "lodash": "^4.17.21", "openai": "^4.56.0", "typescript": "^5.5.3", "vite-node": "^1.4.0", "vite-plugin-dts": "^4.2.3", "zod": "^3.23.8"}}