export type PartOfSpeech =
  | "noun"
  | "verb"
  | "adjective"
  | "adverb"
  | "preposition"
  | "conjunction"
  | "interjection"
  | string;

export interface DefinitionDetails {
  definition: string;
  partOfSpeech: PartOfSpeech;
  context?: string;
  synonyms?: SynonymWithExample[];
  derivation?: string[];
  examples?: string[];
  languages?: {
    [key: string]: {
      definition: string;
      context?: string;
      word?: string;
    }
  }
}

export interface BaseWordEntry {
  word: string;
  llm_model: number; // llm_model id
  definitions: DefinitionDetails[];
}

export interface SynonymWithExample {
  synonym: string;
  example?: string;
}

export type WordEntryId = string | number;

export type WordEntry = BaseWordEntry & {
  id: WordEntryId;
  createdAt?: string;
  updatedAt?: string;
};

export interface DictionarySource {
  listAllMeanings: (word: string) => Promise<BaseWordEntry>;
  listAllMeaningsForLanguage?: (input: string, language: string) => Promise<DefinitionForLanguagePromptResult>;
  getMoreExamples: (input: string) => Promise<ExamplePromptResult>;
  getMoreSynonymsForDefinition: (input: string) => Promise<SynonymPromptResult>;
  getMeaningInContext: (word: string, context: string, offset: number) => Promise<BaseWordInContextEntry>;
  getMeaningInContextShort: (word: string, context: string, offset: number) => Promise<BaseWordInContextEntry>;
  getMeaningInContextShortForLanguage: (
    word: string,
    context: string,
    offset: number,
    language: string,
  ) => Promise<string>;
  // lookupWord: (word: string) => Promise<WordEntry>;
}

export type DefinitionForLanguagePromptResult = {
  definition: string;
  context?: string;
  word?: string;
}[];

export type ExamplePromptResult = {
  examples: string[]
}[];

export type SynonymPromptResult = SynonymWithExample[];

interface BaseWordInContextDefinition {
  definition: string;
  partOfSpeech: PartOfSpeech;
  explanation: string;
  examples: {
    example: string;
    explanation: string;
  }[];
  synonyms: {
    synonym: string;
    example: string;
    explanation: string;
  }[];
}

export interface BaseWordInContextEntry {
  word: string;
  context: string;
  offset: number;
  definition?: BaseWordInContextDefinition,
  definitionShort?: {
    explanation: string;
    languages?: {
      [key: string]: {
        explanation: string;
      }
    }
  }
  llm_model: number;
}

export type WordInContextEntryId = string | number;
export interface WordInContextEntry extends BaseWordInContextEntry {
  id: WordInContextEntryId;
  createdAt?: string;
  updatedAt?: string;
}
