import { DefinitionDetails, PartOfSpeech, BaseWordEntry } from "./types";

export function inputForDefinitionsInLanguage(entry: BaseWordEntry, language: string): { input: string, indexes: number[] } {
  const indexes: number[] = [];
  const definitions: {
    definition: string,
    partOfSpeech: PartOfSpeech,
    example?: string,
    word: string
  }[] = [];

  entry.definitions.forEach((definition, idx) => {
    if (definition.languages?.[language] && definition.languages?.[language].definition !== undefined) {
      return;
    }
    definitions.push({
      definition: definition.definition,
      partOfSpeech: definition.partOfSpeech,
      example: definition.examples !== undefined && definition.examples.length > 0 ? definition.examples[0] : undefined,
      word: entry.word
    });
    indexes.push(idx);
  });

  return {
    input: JSON.stringify(definitions),
    indexes
  };
}

export function inputForExamples(entry: BaseWordEntry): { input: string, indexes: number[] } {
  const indexes: number[] = [];
  const definitions: {
    definition: string,
    partOfSpeech: PartOfSpeech,
    example?: string,
    usageContext?: string,
    word: string
  }[] = [];

  entry.definitions.forEach((definition, idx) => {
    if (definition.examples !== undefined && definition.examples.length > 3) {
      return;
    }
    definitions.push({
      definition: definition.definition,
      partOfSpeech: definition.partOfSpeech,
      example: definition.examples !== undefined && definition.examples.length > 0 ? definition.examples[0] : undefined,
      usageContext: definition.context,
      word: entry.word,
    });
    indexes.push(idx);
  });

  return {
    input: JSON.stringify(definitions),
    indexes,
  }
}

export function inputForSynonyms(word: string, definition: DefinitionDetails): string {
  return JSON.stringify({
    word,
    partOfSpeech: definition.partOfSpeech,
    definition: definition.definition,
    context: definition.context,
    examples: definition.examples !== undefined && definition.examples.length > 0 ?
      definition.examples.slice(0, Math.min(3, definition.examples.length)) : undefined
  });
}
