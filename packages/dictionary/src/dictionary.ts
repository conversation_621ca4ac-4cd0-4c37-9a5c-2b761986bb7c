import { inputForDefinitionsInLanguage, inputForExamples, inputForSynonyms } from "./filterInput";
import { correctLLMText } from "./sources/utils";
import { BaseWordEntry, DictionarySource, WordInContextEntry } from "./types";

export class Dictionary {
  sources: DictionarySource[];

  constructor(sources: DictionarySource[]) {
    this.sources = sources;
  }

  async listAllMeanings(word: string): Promise<{ word: BaseWordEntry, changed: boolean }> {
    if (this.sources.length === 0) throw new Error('Empty source list');
    let idx = 0;
    let source = this.sources[idx];

    while (true) {
      try {
        const base = await source.listAllMeanings(word);
        base.definitions.forEach((definition) => {
          definition.context = definition.context ? correctLLMText(definition.context) : undefined
        });
        return { word: base, changed: true };
      } catch (err) {
        console.log(err);
        if (idx + 1 >= this.sources.length) throw err;
        idx += 1;
        source = this.sources[idx];
      }
    }
  }

  async listAllMeaningsForLanguage(entry: BaseWordEntry, language: string): Promise<{ word: BaseWordEntry, changed: boolean }> {
    const { input, indexes } = inputForDefinitionsInLanguage(entry, language);
    if (indexes.length === 0) return { word: entry, changed: false };

    if (this.sources.length === 0) throw new Error('Empty source list');
    let idx = 0;
    let source = this.sources[idx];

    while (true) {
      try {
        if (!source.listAllMeaningsForLanguage) {
          if (idx + 1 >= this.sources.length) {
            if (language !== 'English') throw new Error(`The model does not currently support ${language}. Please select a different model or language.`);
            return { word: entry, changed: false };
          }
          idx += 1;
          continue;
        }
        const result = await source.listAllMeaningsForLanguage(input, language);

        result.forEach((ele, idx) => {
          const { definition, context, word } = ele;
          if (entry.definitions[indexes[idx]].languages === undefined) {
            entry.definitions[indexes[idx]].languages = {};
          }
          entry.definitions[indexes[idx]].languages![language] = {
            definition,
            context,
            word
          }
        });

        return { word: entry, changed: true };
      } catch (err) {
        console.log(err);
        if (idx + 1 >= this.sources.length) throw err;
        idx += 1;
        source = this.sources[idx];
      }
    }
  }

  async getMoreExamples(entry: BaseWordEntry): Promise<{ word: BaseWordEntry, changed: boolean }> {
    const { input, indexes } = inputForExamples(entry);
    if (indexes.length === 0) return { word: entry, changed: false };

    if (this.sources.length === 0) throw new Error('Empty source list');
    let idx = 0;
    let source = this.sources[idx];

    while (true) {
      try {
        const result = await source.getMoreExamples(input);

        result.forEach((ele, idx) => {
          const { examples } = ele;
          const defExamples = entry.definitions[indexes[idx]].examples;
          if (defExamples !== undefined && defExamples.length > 0) {
            defExamples.push(...examples);
          } else {
            entry.definitions[indexes[idx]].examples = examples;
          }
        });

        return { word: entry, changed: true };
      } catch (err) {
        console.log(err);
        if (idx + 1 >= this.sources.length) throw err;
        idx += 1;
        source = this.sources[idx];
      }
    }
  }

  async getMoreSynonyms(entry: BaseWordEntry): Promise<{ word: BaseWordEntry, changed: boolean }> {
    const indexes: number[] = [];
    const synonymInputs: string[] = [];

    entry.definitions.forEach((definition, idx) => {
      if (definition.synonyms !== undefined && definition.synonyms.length > 3) {
        return;
      }
      indexes.push(idx);
      synonymInputs.push(inputForSynonyms(entry.word, definition));
    });
    if (indexes.length === 0) return { word: entry, changed: false };

    const synonymPromises = synonymInputs
      .map(async (input) => {
        if (this.sources.length === 0) throw new Error('Empty source list');
        let idx = 0;
        let source = this.sources[idx];

        while (true) {
          try {
            const data = await source.getMoreSynonymsForDefinition(input);
            return data;
          } catch (err) {
            console.log(err);
            if (idx + 1 >= this.sources.length) throw err;
            idx += 1;
            source = this.sources[idx];
          }
        }
      });

    const synonymResults = await Promise.allSettled(synonymPromises);

    synonymResults.forEach((synonyms, idx) => {
      if (synonyms.status === 'rejected') return;
      const defSynonyms = entry.definitions[indexes[idx]].synonyms;
      if (defSynonyms && defSynonyms.length > 0) {
        defSynonyms.push(...synonyms.value);
      } else {
        entry.definitions[indexes[idx]].synonyms = synonyms.value;
      }
    });

    return { word: entry, changed: true };
  }

  async getMeaningInContext(word: string, context: string, offset: number) {
    if (this.sources.length === 0) throw new Error('Empty source list');
    let idx = 0;
    let source = this.sources[idx];

    while (true) {
      try {
        const result = await source.getMeaningInContext(word, context, offset);
        if (!result.definition) throw new Error('Expect definition');
        result.definition.explanation = correctLLMText(result.definition.explanation);
        result.definition.examples.forEach(example => {
          example.explanation = correctLLMText(example.explanation);
        });
        result.definition.synonyms.forEach(synonym => {
          synonym.explanation = correctLLMText(synonym.explanation);
        });
        return result;
      } catch (err) {
        console.log(err);
        if (idx + 1 >= this.sources.length) throw err;
        idx += 1;
        source = this.sources[idx];
      }
    }
  }

  async getMeaningInContextShort(word: string, context: string, offset: number) {
    if (this.sources.length === 0) throw new Error('Empty source list');
    let idx = 0;
    let source = this.sources[idx];

    while (true) {
      try {
        const result = await source.getMeaningInContextShort(word, context, offset);
        return result;
      } catch (err) {
        console.log(err);
        if (idx + 1 >= this.sources.length) throw err;
        idx += 1;
        source = this.sources[idx];
      }
    }
  }

  async getMeaningInContextShortForLanguage(
    wordEntry: WordInContextEntry,
    language: string,
  ) {
    if (this.sources.length === 0) throw new Error('Empty source list');
    let idx = 0;
    let source = this.sources[idx];

    while (true) {
      try {
        const result = await source.getMeaningInContextShortForLanguage(wordEntry.word, wordEntry.context, wordEntry.offset, language);
        wordEntry = {
          ...wordEntry,
          definitionShort: {
            ...wordEntry.definitionShort!,
            languages: {
              ...wordEntry.definitionShort?.languages,
              [language]: {
                explanation: result,
              }
            }
          }
        };
        return wordEntry;
      } catch (err) {
        console.log(err);
        if (idx + 1 >= this.sources.length) throw err;
        idx += 1;
        source = this.sources[idx];
      }
    }
  }

  async lookupWord(word: string): Promise<BaseWordEntry> {
    let entry = await this.listAllMeanings(word);
    entry = await this.getMoreExamples(entry.word);
    entry = await this.getMoreSynonyms(entry.word);
    return entry.word;
  }
}
