import { Anthropic } from '@anthropic-ai/sdk';
import { ChatMessage, ChatResponse, ChatSource } from './types';

export class AnthropicChat implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName = "claude-3-haiku-20240307";
  maxTokens = 2000;
  anthropic: Anthropic;
  messages: Anthropic.Messages.MessageParam[] = [];

  constructor(config: {
    modelId: number;
    modelName: string;
    maxToken?: number;
    apiKey: string;
  }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.anthropic = new Anthropic({
      apiKey: this.apiKey,
      dangerouslyAllowBrowser: true
    });
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages as Anthropic.Messages.MessageParam[];
  }

  toChatResponse(response: Anthropic.Messages.Message): ChatResponse {
    const message =
      response.content.length > 0 && response.content[0].type === "text"
        ? response.content[0].text
        : undefined;

    if (!message) throw new Error("Unreachable");

    return {
      message,
      usage: {
        promptTokens: response.usage.input_tokens,
        completionTokens: response.usage.output_tokens,
        totalTokens: response.usage.input_tokens + response.usage.output_tokens
      },
      modelId: this.modelId,
    };
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    this.messages.push({ role: 'user', content: message });

    const response = await this.anthropic.messages.create({
      max_tokens: this.maxTokens,
      model: this.modelName,
      messages: this.messages
    });
    const chatResponse = this.toChatResponse(response);
    this.messages.push({
      role: 'assistant',
      content: chatResponse.message
    });
    return chatResponse;
  }
}
