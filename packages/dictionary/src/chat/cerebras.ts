import OpenAI from 'openai';
import { ChatMessage, ChatResponse, ChatSource } from './types';
import { ChatCompletionMessageParam } from 'openai/resources';
import { ChatCompletion } from 'openai/resources';
import { removeThinkBlocks } from '../sources/utils';

export class CerebrasChat implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  isThinkingModel = false;
  maxTokens = 2000;
  openAI: OpenAI;
  messages: ChatCompletionMessageParam[] = [];

  constructor(config: { 
    modelName: string, 
    modelId: number, 
    maxToken?: number, 
    apiKey: string,
    isThinkingModel?: boolean 
  }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.isThinkingModel = config.isThinkingModel ?? false;
    this.openAI = new OpenAI({
      apiKey: this.apiKey,
      dangerouslyAllowBrowser: true,
      baseURL: 'https://api.cerebras.ai/v1',
    });

    if (!this.isThinkingModel) {
      this.messages.push({
        role: 'system',
        content: '/nothink'
      });
    }
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  toChatResponse (result: ChatCompletion): ChatResponse {
    const message = removeThinkBlocks(result.choices[0].message.content as string);
    return {
      message,
      usage: {
        promptTokens: result.usage?.prompt_tokens,
        completionTokens: result.usage?.completion_tokens,
        totalTokens: result.usage?.total_tokens,
      },
      modelId: this.modelId
    };
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    this.messages.push({ role: 'user', content: message });

    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: this.messages,
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    const chatResponse = this.toChatResponse(completion);
    this.messages.push({
      role: 'assistant',
      content: chatResponse.message
    });
    return chatResponse;
  }
}
