import OpenAI from "openai";
import { ChatMessage, ChatResponse, ChatSource } from "./types";
import { ChatCompletionMessageParam } from "openai/resources";
import { ChatCompletion } from "openai/resources";

export class OpenRouterChat implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  maxTokens = 2000;
  openAI: OpenAI;
  messages: ChatCompletionMessageParam[] = [];

  constructor(config: { modelName: string, modelId: number, maxToken?: number, apiKey: string }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.openAI = new OpenAI({
      apiKey: this.apiKey,
      baseURL: 'https://openrouter.ai/api/v1',
      defaultHeaders: {
        'HTTP-Referer': 'https://pickvocab.com/',
        'X-Title': 'Pickvocab',
      },
      dangerouslyAllowBrowser: true,
    });
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  toChatResponse (result: ChatCompletion): ChatResponse {
    return {
      message: result.choices[0].message.content as string,
      usage: {
        promptTokens: result.usage?.prompt_tokens,
        completionTokens: result.usage?.completion_tokens,
        totalTokens: result.usage?.total_tokens,
      },
      modelId: this.modelId,
    };
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    this.messages.push({ role: 'user', content: message });

    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: this.messages,
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    console.log(completion);
    const chatResponse = this.toChatResponse(completion);
    this.messages.push({
      role: 'assistant',
      content: chatResponse.message
    });
    return chatResponse;
  }
}