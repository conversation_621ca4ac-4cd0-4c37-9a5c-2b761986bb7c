import { ChatSession, GenerateContentResult, GenerativeModel, GoogleGenerativeAI } from '@google/generative-ai';
import { ChatMessage, ChatResponse, ChatSource } from './types';

export class GeminiChat implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName = 'gemini-1.5-flash-latest';
  maxTokens = 2000;
  genAI: GoogleGenerativeAI;
  model: GenerativeModel;
  chatInstance: ChatSession;

  constructor(config: { modelName: string, modelId: number, maxToken?: number, apiKey: string }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.genAI = new GoogleGenerativeAI(this.apiKey);

    if (this.modelName === 'gemini-1.5-flash-latest') {
      this.model = this.genAI.getGenerativeModel({ model: this.modelName }, { apiVersion: 'v1beta' });
    } else {
      this.model = this.genAI.getGenerativeModel({ model: this.modelName });
    }

    this.chatInstance = this.model.startChat();
  }

  setHistory(messages: ChatMessage[]): void {
    this.chatInstance = this.model.startChat({
      history: messages.map(message => ({
        role: message.role === 'assistant' ? 'model' : message.role,
        parts: [{ text: message.content }]
      }))
    });
  }

  toChatResponse(result: GenerateContentResult): ChatResponse {
    return {
      message: result.response.text(),
      usage: {
        promptTokens: result.response.usageMetadata?.promptTokenCount,
        completionTokens: result.response.usageMetadata?.candidatesTokenCount,
        totalTokens: result.response.usageMetadata?.totalTokenCount,
      },
      modelId: this.modelId
    };
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    const result = await this.chatInstance.sendMessage(message);
    return this.toChatResponse(result);
  }
}