// CodestralChat: Like MistralChat, but uses the Codestral endpoint (https://codestral.mistral.ai/)
import { Mistral } from "@mistralai/mistralai";
import { ChatMessage, ChatResponse, ChatSource } from './types';

export class CodestralChat implements ChatSource {
  apiKey: string;
  modelId: number;
  modelName: string;
  maxTokens = 2000;
  client: Mistral;
  messages: ChatMessage[] = [];

  constructor(config: { modelName: string, modelId: number, maxToken?: number, apiKey: string }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config?.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.client = new Mistral({
      apiKey: this.apiKey,
      serverURL: "https://codestral.mistral.ai/",
    });
  }

  setHistory(messages: ChatMessage[]): void {
    this.messages = messages;
  }

  toChatResponse(result: any): ChatResponse {
    return {
      message: result.choices[0].message.content as string,
      usage: {
        promptTokens: result.usage?.promptTokens,
        completionTokens: result.usage?.completionTokens,
        totalTokens: result.usage?.totalTokens,
      },
      modelId: this.modelId
    };
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    this.messages.push({ role: 'user', content: message });

    const completion = await this.client.chat.complete({
      model: this.modelName,
      messages: this.messages,
      maxTokens: this.maxTokens,
    });

    const chatResponse = this.toChatResponse(completion);
    this.messages.push({
      role: 'assistant',
      content: chatResponse.message
    });
    return chatResponse;
  }
} 