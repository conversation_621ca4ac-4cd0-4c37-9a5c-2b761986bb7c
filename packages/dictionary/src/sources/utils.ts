export function correctLLMText(text: string): string {
  return text.split(/\s*\.\s*/).map((sentence) => {
    // if string is empty, return empty string
    if (sentence.length === 0) return '';
    // uppercase first letter
    return sentence.charAt(0).toUpperCase() + sentence.slice(1);
  }).join('. ');
}

export function removeThinkBlocks(text: string): string {
  return text.replace(/<think>[\s\S]*?<\/think>/g, '');
}

export function correctLLMResponse(text: string): string {
  let response = removeThinkBlocks(text);
  response = response.replace(/[\s\S]*```json/g, '');
  response = response.replace(/```[\s\S]*/g, '');
  return response;
}
