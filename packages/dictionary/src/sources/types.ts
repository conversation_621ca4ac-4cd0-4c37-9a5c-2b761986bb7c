import { DefinitionDetails, SynonymWithExample, BaseWordEntry, BaseWordInContextEntry } from "../types";

export interface DictionarySource {
  listAllMeanings: (word: string) => Promise<BaseWordEntry>;
  listAllMeaningsForLanguage?: (input: string, language: string) => Promise<DefinitionForLanguagePromptResult>;
  getMoreExamples: (input: string) => Promise<ExamplePromptResult>;
  getMoreSynonymsForDefinition: (input: string) => Promise<SynonymPromptResult>;
  getMeaningInContext: (word: string, context: string, offset: number) => Promise<BaseWordInContextEntry>;
  getMeaningInContextShort: (word: string, context: string, offset: number) => Promise<BaseWordInContextEntry>;
  getMeaningInContextShortForLanguage: (
    word: string,
    context: string,
    offset: number,
    language: string,
  ) => Promise<string>;
  // lookupWord: (word: string) => Promise<WordEntry>;
}

export type DefinitionForLanguagePromptResult = {
  definition: string;
  context?: string;
  word?: string;
}[];

export type ExamplePromptResult = {
  examples: string[]
}[];

export type SynonymPromptResult = SynonymWithExample[];
