// To run this script, use: ts-node test_codestral.ts
// If you see a 'Cannot find name "process"' error, install @types/node:
//   npm install --save-dev @types/node
import { Mistral } from "@mistralai/mistralai";

// Set your API key in the environment variable MISTRAL_API_KEY
const apiKey = 'QFfQGcxS04a4Xhd3KE6BngtvJdCnZ1T7';
if (!apiKey) {
  throw new Error("Please set the MISTRAL_API_KEY environment variable.");
}

const client = new Mistral({
  apiKey,
  serverURL: "https://codestral.mistral.ai/",
});

async function main() {
  try {
    const response = await client.chat.complete({
      model: "codestral-latest", // or "codestral-2405" if that's the available model
      messages: [
        { role: "user", content: "Hi, how are you?" },
        { role: 'assistant', content: 'I am a helpful assistant. How can I help you today?' },
        { role: "user", content: "What is the capital of France?" },
      ],
    });
    console.log("Codestral response:", JSON.stringify(response, null, 2));
  } catch (err) {
    console.error("Error from Codestral API:", err);
  }
}

main();
