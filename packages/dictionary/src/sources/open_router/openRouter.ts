import OpenAI from "openai";
import { BaseWordEntry, BaseWordInContextEntry, PartOfSpeech } from "../../types";
import { definitionForLanguagePrompt, listAllMeaningPrompt, meaningInContextJsonPrompt, meaningInContextPrompt, meaningInContextShortPrompt, meaningInContextToJson, moreExamplesPrompt, moreSynonymsPrompt } from "./prompt";
import { jsonrepair } from "jsonrepair";
import { DefinitionForLanguagePromptResult, DictionarySource, ExamplePromptResult, SynonymPromptResult } from "../types";
import { correctLLMResponse } from "../utils";

export class OpenRouterSource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 2000;
  openAI: OpenAI;

  constructor(config: { modelId: number, modelName: string, maxToken?: number, apiKey?: string }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.openAI = new OpenAI({
      apiKey: this.apiKey,
      baseURL: 'https://openrouter.ai/api/v1',
      defaultHeaders: {
        'HTTP-Referer': 'https://pickvocab.com/',
        'X-Title': 'Pickvocab',
      },
      dangerouslyAllowBrowser: true,
    });
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'system', content: listAllMeaningPrompt },
        { role: 'user', content: word }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from OpenAI');

    console.log(`${this.modelName}-listAllMeanings`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const definitions = JSON.parse(jsonrepair(responseText));
      const data: BaseWordEntry = {
        word,
        llm_model: this.modelId,
        definitions: definitions.map((d: { partOfSpeech: PartOfSpeech, definition: string, example: string, context?: string }) => ({
          partOfSpeech: d.partOfSpeech,
          definition: d.definition,
          context: d.context,
          examples: [d.example],
          synonyms: []
        }))
      };
      return data;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'system', content: definitionForLanguagePrompt(language) },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from OpenAI');
    console.log(`${this.modelName}-listAllMeaningsForLanguage`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);


    try {
      const examples = JSON.parse(jsonrepair(responseText));
      return examples;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'system', content: moreExamplesPrompt },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from OpenAI');
    console.log(`${this.modelName}-getMoreExamples`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);


    try {
      const examples = JSON.parse(jsonrepair(responseText));
      return examples;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const completion = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'system', content: moreSynonymsPrompt },
        { role: 'user', content: input }
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });

    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from OpenAI');

    console.log(`${this.modelName}-getMoreSynonymsForDefinition`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const synonyms = JSON.parse(jsonrepair(responseText));
      return synonyms;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  // async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
  //   let completions = await this.openAI.chat.completions.create({
  //     model: this.modelName,
  //     messages: [
  //       { role: 'user', content: meaningInContextPrompt(word, context) },
  //     ],
  //     max_tokens: this.maxTokens,
  //     temperature: 0.5,
  //   });
  //   let message = completions.choices[0].message.content;
  //   console.log(`SambaNova-${this.modelName}-getMeaningInContext`, message);
  //   console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
  //   console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
  //   console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

  //   if (!message) throw new Error('Cannot parse response');

  //   completions = await this.openAI.chat.completions.create({
  //     model: this.modelName,
  //     messages: [
  //       { role: 'user', content: meaningInContextToJson(message) },
  //     ],
  //     max_tokens: this.maxTokens,
  //     temperature: 0.5,
  //   });

  //   message =  completions.choices[0].message.content;
  //   console.log(`SambaNova-${this.modelName}-toJson`, message);
  //   console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
  //   console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
  //   console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

  //   if (!message) throw new Error('Cannot parse response');

  //   const result = JSON.parse(jsonrepair(message));
  //   return {
  //     word,
  //     context,
  //     offset,
  //     definition: result,
  //     llm_model: this.modelId,
  //   };
  // }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'user', content: meaningInContextJsonPrompt(word, context) },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`SambaNova-${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');
    message = correctLLMResponse(message);

    const result = JSON.parse(jsonrepair(message));
    return {
      word,
      context,
      offset,
      definition: result,
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'user', content: meaningInContextShortPrompt(word, context) },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`SambaNova-${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');

    return {
      word,
      context,
      offset,
      definitionShort: {
        explanation: message,
      },
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    let completions = await this.openAI.chat.completions.create({
      model: this.modelName,
      messages: [
        { role: 'user', content: meaningInContextShortPrompt(word, context) },
      ],
      max_tokens: this.maxTokens,
      temperature: 0.5,
    });
    let message = completions.choices[0].message.content;
    console.log(`SambaNova-${this.modelName}-getMeaningInContext`, message);
    console.log(`${this.modelName}-promptTokens`, completions.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completions.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completions.usage?.total_tokens);

    if (!message) throw new Error('Cannot parse response');

    return message;
  }
}
