import { jsonrepair } from "jsonrepair";
import { BaseWordEntry, BaseWordInContextEntry, PartOfSpeech } from "../../types";
import { DefinitionForLanguagePromptResult, DictionarySource, ExamplePromptResult, SynonymPromptResult } from "../types";
import { definitionForLanguagePrompt, listAllMeaningPrompt, moreExamplesPrompt, moreSynonymsPrompt } from "./prompt";
import axios from "axios";

export class ArliAISource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 2000;

  constructor(config: { modelId: number, modelName: string, maxToken?: number, apiKey?: string }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const response = await axios.post("https://api.arliai.com/v1/chat/completions", {
      model: `${this.modelName}`,
      messages: [
        { role: "system", content: listAllMeaningPrompt },
        { role: "user", content: word },
      ],
      repetition_penalty: 1.1,
      temperature: 0.5,
      top_p: 0.9,
      top_k: 40,
      max_tokens: this.maxTokens,
    }, {
      headers: {
        "Authorization": `Bearer ${this.apiKey}`,
        "Content-Type": "application/json"
      },
    });

    const completion = response.data;
    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from ArliAI');

    console.log(`${this.modelName}-listAllMeanings`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const definitions = JSON.parse(jsonrepair(responseText));
      const data: BaseWordEntry = {
        word,
        llm_model: this.modelId,
        definitions: definitions.map((d: { partOfSpeech: PartOfSpeech, definition: string, example: string, context?: string }) => ({
          partOfSpeech: d.partOfSpeech,
          definition: d.definition,
          context: d.context,
          examples: [d.example],
          synonyms: []
        }))
      };
      return data;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const response = await axios.post("https://api.arliai.com/v1/chat/completions", {
      model: `${this.modelName}`,
      messages: [
        { role: "system", content: definitionForLanguagePrompt(language) },
        { role: "user", content: input },
      ],
      repetition_penalty: 1.1,
      temperature: 0.5,
      top_p: 0.9,
      top_k: 40,
      max_tokens: this.maxTokens,
    }, {
      headers: {
        "Authorization": `Bearer ${this.apiKey}`,
        "Content-Type": "application/json"
      },
    });
    const completion = response.data;
    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from ArliAI');
    console.log(`${this.modelName}-listAllMeaningsForLanguage`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const examples = JSON.parse(jsonrepair(responseText));
      return examples;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const response = await axios.post("https://api.arliai.com/v1/chat/completions", {
      model: `${this.modelName}`,
      messages: [
        { role: "system", content: moreExamplesPrompt },
        { role: "user", content: input },
      ],
      repetition_penalty: 1.1,
      temperature: 0.5,
      top_p: 0.9,
      top_k: 40,
      max_tokens: this.maxTokens,
    }, {
      headers: {
        "Authorization": `Bearer ${this.apiKey}`,
        "Content-Type": "application/json"
      },
    });
    const completion = response.data;
    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from ArliAI');
    console.log(`${this.modelName}-getMoreExamples`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const examples = JSON.parse(jsonrepair(responseText));
      return examples;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const response = await axios.post("https://api.arliai.com/v1/chat/completions", {
      model: `${this.modelName}`,
      messages: [
        { role: "system", content: moreSynonymsPrompt },
        { role: "user", content: input },
      ],
      repetition_penalty: 1.1,
      temperature: 0.5,
      top_p: 0.9,
      top_k: 40,
      max_tokens: this.maxTokens,
    }, {
      headers: {
        "Authorization": `Bearer ${this.apiKey}`,
        "Content-Type": "application/json"
      },
    });
    const completion = response.data;
    const responseText = completion.choices[0].message.content;
    if (!responseText) throw new Error('No response from ArliAI');
    console.log(`${this.modelName}-getMoreSynonymsForDefinition`, responseText);
    console.log(`${this.modelName}-promptTokens`, completion.usage?.prompt_tokens);
    console.log(`${this.modelName}-completionTokens`, completion.usage?.completion_tokens);
    console.log(`${this.modelName}-totalTokens`, completion.usage?.total_tokens);

    try {
      const synonyms = JSON.parse(jsonrepair(responseText));
      return synonyms;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    throw new Error('Not implemented');
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    throw new Error('Not implemented');
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    throw new Error('Not implemented');
  }
}
