import { DefinitionForLanguagePromptResult, DictionarySource, ExamplePromptResult, SynonymPromptResult } from "../types";
import { BaseWordEntry, BaseWordInContextEntry, PartOfSpeech } from "../../types";
import {
  definitionForLanguagePrompt,
  listAllMeaningPrompt,
  meaningInContextJsonPrompt,
  meaningInContextPrompt,
  meaningInContextShortForLanguagePrompt,
  meaningInContextShortPrompt,
  meaningInContextToJson,
  moreExamplesPrompt,
  moreSynonymsPrompt
} from "../chutes/prompt";
import { jsonrepair } from "jsonrepair";
import { correctLLMResponse } from "../utils";

export class ChutesSource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 2000;
  isThinkingModel = false;
  apiUrl = "https://llm.chutes.ai/v1/chat/completions";

  constructor(config: {
    modelId: number,
    modelName: string,
    maxToken?: number,
    apiKey?: string,
    isThinkingModel?: boolean
  }) {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.isThinkingModel = config.isThinkingModel ?? false;
  }

  private async callChutes(messages: any[], stream = false): Promise<any> {
    const response = await fetch(this.apiUrl, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${this.apiKey}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        model: this.modelName,
        messages,
        stream,
        max_tokens: this.maxTokens,
        temperature: 0.7
      })
    });
    if (!response.ok) throw new Error(`Chutes API error: ${response.status}`);
    return response.json();
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const messages = [
      {
        role: 'system',
        content: this.isThinkingModel ? listAllMeaningPrompt : `/nothink\n${listAllMeaningPrompt}`
      },
      { role: 'user', content: word }
    ];
    const data = await this.callChutes(messages);
    const responseText = data.choices?.[0]?.message?.content;
    if (!responseText) throw new Error('No response from Chutes');
    const fixed = correctLLMResponse(responseText);
    try {
      const definitions = JSON.parse(jsonrepair(fixed)).definitions;
      return {
        word,
        llm_model: this.modelId,
        definitions: definitions.map((d: { partOfSpeech: PartOfSpeech, definition: string, example: string, context?: string }) => ({
          partOfSpeech: d.partOfSpeech,
          definition: d.definition,
          context: d.context,
          examples: [d.example],
          synonyms: []
        }))
      };
    } catch (err) {
      throw new Error('Failed to parse response from Chutes', { cause: err });
    }
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const messages = [
      {
        role: 'system',
        content: this.isThinkingModel ? definitionForLanguagePrompt(language) : `/nothink\n${definitionForLanguagePrompt(language)}`
      },
      { role: 'user', content: input }
    ];
    const data = await this.callChutes(messages);
    const responseText = data.choices?.[0]?.message?.content;
    if (!responseText) throw new Error('No response from Chutes');
    const fixed = correctLLMResponse(responseText);
    try {
      const definitions = JSON.parse(jsonrepair(fixed)).definitions;
      return definitions;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const messages = [
      {
        role: 'system',
        content: this.isThinkingModel ? moreExamplesPrompt : `/nothink\n${moreExamplesPrompt}`
      },
      { role: 'user', content: input }
    ];
    const data = await this.callChutes(messages);
    const responseText = data.choices?.[0]?.message?.content;
    if (!responseText) throw new Error('No response from Chutes');
    const fixed = correctLLMResponse(responseText);
    try {
      const examples = JSON.parse(jsonrepair(fixed)).result;
      return examples;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const messages = [
      {
        role: 'system',
        content: this.isThinkingModel ? moreSynonymsPrompt : `/nothink\n${moreSynonymsPrompt}`
      },
      { role: 'user', content: input }
    ];
    const data = await this.callChutes(messages);
    const responseText = data.choices?.[0]?.message?.content;
    if (!responseText) throw new Error('No response from Chutes');
    const fixed = correctLLMResponse(responseText);
    try {
      const synonyms = JSON.parse(jsonrepair(fixed)).result;
      return synonyms;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const messages = [
      {
        role: 'user',
        content: this.isThinkingModel ? meaningInContextJsonPrompt(word, context) : `/nothink\n${meaningInContextJsonPrompt(word, context)}`
      }
    ];
    const data = await this.callChutes(messages);
    const responseText = data.choices?.[0]?.message?.content;
    if (!responseText) throw new Error('No response from Chutes');
    const fixed = correctLLMResponse(responseText);
    try {
      const result = JSON.parse(jsonrepair(fixed));
      return {
        word,
        context,
        offset,
        definition: result,
        llm_model: this.modelId,
      };
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    const messages = [
      {
        role: 'user',
        content: this.isThinkingModel ? meaningInContextShortPrompt(word, context) : `/nothink\n${meaningInContextShortPrompt(word, context)}`
      }
    ];
    let data = await this.callChutes(messages);
    let responseText = data.choices?.[0]?.message?.content;
    if (!responseText) throw new Error('No response from Chutes');
    let fixed = correctLLMResponse(responseText);
    return {
      word,
      context,
      offset,
      definitionShort: {
        explanation: fixed,
      },
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShortForLanguage(word: string, context: string, offset: number, language: string): Promise<string> {
    const messages = [
      {
        role: 'user',
        content: this.isThinkingModel ? meaningInContextShortForLanguagePrompt(word, context, language) : `/nothink\n${meaningInContextShortForLanguagePrompt(word, context, language)}`
      }
    ];
    const data = await this.callChutes(messages);
    const responseText = data.choices?.[0]?.message?.content;
    if (!responseText) throw new Error('No response from Chutes');
    const fixed = correctLLMResponse(responseText);
    return fixed;
  }
} 