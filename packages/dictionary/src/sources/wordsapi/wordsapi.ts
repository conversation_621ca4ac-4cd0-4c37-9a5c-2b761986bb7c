import axios, { AxiosError } from 'axios';
import { DefinitionDetails, BaseWordEntry, BaseWordInContextEntry } from "../../types";
import { DictionarySource, ExamplePromptResult, SynonymPromptResult } from "../types";

const WORDS_API_KEY = '';

export class WordsApi implements DictionarySource {
  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const options = {
      method: 'GET',
      url: `https://wordsapiv1.p.rapidapi.com/words/${word}`,
      headers: {
        'X-RapidAPI-Key': WORDS_API_KEY,
        'X-RapidAPI-Host': 'wordsapiv1.p.rapidapi.com'
      }
    };
    const response = await axios.request(options);
    const data = {
      ...response.data,
      definitions: response.data.results.map((d: any) => {
        return {
          ...d,
          synonyms: d.synonyms?.map((synonym: string) => {
            return {
              synonym,
            };
          })
        }
      }),
      results: undefined
    } as BaseWordEntry;
    console.log('WORDSAPI-listAllMeanings', data);
    return data;
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    throw new Error('Not supported');
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    throw new Error('Not supported');
  }

  async getMeaningInContext (word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    throw new Error('Not supported');
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    throw new Error('Not supported');
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string
  ): Promise<string> {
    throw new Error('Not supported');
  }
}
