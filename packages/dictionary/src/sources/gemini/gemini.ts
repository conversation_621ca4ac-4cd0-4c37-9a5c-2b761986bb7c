import { ChatSession, Content, GenerativeModel, GoogleGenerativeAI } from '@google/generative-ai';
import { jsonrepair } from 'jsonrepair';
import { PartOfSpeech, BaseWordEntry, BaseWordInContextEntry } from '../../types';
import { DefinitionForLanguagePromptResult, DictionarySource, ExamplePromptResult, SynonymPromptResult } from '../types';
import { definitionForLanguagePrompt, listAllMeaningPrompt, meaningInContextJsonPrompt, meaningInContextPrompt, meaningInContextShortForLanguagePrompt, meaningInContextShortPrompt, meaningInContextToJson, moreExamplesPrompt, moreSynonymsPrompt } from './prompt';
import { correctLLMResponse } from '../utils';

export class GeminiSource implements DictionarySource {
  apiKey: string | undefined;
  modelId: number;
  modelName: string;
  maxTokens = 2000;
  genAI: GoogleGenerativeAI;
  model: GenerativeModel;

  constructor(config: { modelId: number, modelName: string, maxToken?: number, apiKey: string }) {
    this.apiKey = config.apiKey;
    this.modelId = config.modelId;
    this.modelName = config.modelName;
    if (config.maxToken !== undefined) this.maxTokens = config.maxToken;
    this.genAI = new GoogleGenerativeAI(this.apiKey);

    this.model = this.genAI.getGenerativeModel({ model: this.modelName });
  }

  async listAllMeanings(word: string): Promise<BaseWordEntry> {
    const response = await this.model.generateContent(listAllMeaningPrompt(word));
    let responseText = response.response.text();
    responseText = responseText.replace('```json', '');
    responseText = responseText.replace('```', '');
    console.log(`${this.modelName}-listAllMeanings`, responseText);

    try {
      const definitions = JSON.parse(jsonrepair(responseText));
      const data: BaseWordEntry = {
        word,
        llm_model: this.modelId,
        definitions: definitions.map((d: { partOfSpeech: PartOfSpeech, definition: string, example: string, context?: string }) => ({
          partOfSpeech: d.partOfSpeech,
          definition: d.definition,
          context: d.context,
          examples: [d.example],
          synonyms: []
        }))
      };
      return data;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async listAllMeaningsForLanguage(input: string, language: string): Promise<DefinitionForLanguagePromptResult> {
    const response = await this.model.generateContent(definitionForLanguagePrompt(input, language));
    let responseText = response.response.text();
    responseText = responseText.replace('```json', '');
    responseText = responseText.replace('```', '');
    console.log(`${this.modelName}-listAllMeaningsForLanguage`, responseText);

    try {
      const examples = JSON.parse(jsonrepair(responseText));
      return examples;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreExamples(input: string): Promise<ExamplePromptResult> {
    const response = await this.model.generateContent(moreExamplesPrompt(input));
    let responseText = response.response.text();
    responseText = responseText.replace('```json', '');
    responseText = responseText.replace('```', '');
    console.log(`${this.modelName}-getMoreExamples`, responseText);

    try {
      const examples = JSON.parse(jsonrepair(responseText));
      return examples;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMoreSynonymsForDefinition(input: string): Promise<SynonymPromptResult> {
    const response = await this.model.generateContent(moreSynonymsPrompt(input));
    let responseText = response.response.text();
    responseText = responseText.replace('```json', '');
    responseText = responseText.replace('```', '');
    console.log(`${this.modelName}-getMoreSynonymsForDefinition`, responseText);

    try {
      const synonyms = JSON.parse(jsonrepair(responseText));
      return synonyms;
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  // async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
  //   // First message to get meaning in context
  //   let response = await this.model.generateContent(meaningInContextPrompt(word, context));
  //   let responseText = response.response.text();
  //   responseText = responseText.replace('```json', '');
  //   responseText = responseText.replace('```', '');
  //   console.log(`${this.modelName}-getMeaningInContext`, responseText);

  //   // Second message to convert to JSON
  //   response = await this.model.generateContent(meaningInContextToJson(responseText));
  //   responseText = response.response.text();
  //   responseText = responseText.replace('```json', '');
  //   responseText = responseText.replace('```', '');
  //   console.log(`${this.modelName}-toJson`, responseText);

  //   try {
  //     const result = JSON.parse(jsonrepair(responseText));
  //     return {
  //       word,
  //       context,
  //       offset,
  //       definition: result,
  //       llm_model: this.modelId,
  //     };
  //   } catch (err) {
  //     throw new Error('Cannot parse response', { cause: err });
  //   }
  // }

  async getMeaningInContext(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    // First message to get meaning in context
    let response = await this.model.generateContent(meaningInContextJsonPrompt(word, context));
    let responseText = response.response.text();
    console.log(`${this.modelName}-getMeaningInContext`, responseText);
    responseText = correctLLMResponse(responseText);

    try {
      const result = JSON.parse(jsonrepair(responseText));
      return {
        word,
        context,
        offset,
        definition: result,
        llm_model: this.modelId,
      };
    } catch (err) {
      throw new Error('Cannot parse response', { cause: err });
    }
  }

  async getMeaningInContextShort(word: string, context: string, offset: number): Promise<BaseWordInContextEntry> {
    // First message to get meaning in context
    let response = await this.model.generateContent(meaningInContextShortPrompt(word, context));
    let responseText = response.response.text();
    console.log(`${this.modelName}-getMeaningInContext`, responseText);

    return {
      word,
      context,
      offset,
      definitionShort: {
        explanation: responseText,
      },
      llm_model: this.modelId,
    };
  }

  async getMeaningInContextShortForLanguage(
    word: string,
    context: string,
    offset: number,
    language: string,
  ): Promise<string> {
    // First message to get meaning in context
    let response = await this.model.generateContent(meaningInContextShortForLanguagePrompt(word, context, language));
    let responseText = response.response.text();
    console.log(`${this.modelName}-getMeaningInContext`, responseText);

    return responseText;
  }

  async chatInstance(options?: { history?: Content[] }): Promise<ChatSession> {
    return await this.model.startChat({
      history: options?.history ? options.history : []
    });
  }
}
