import { GeminiSource } from './gemini';

const gemini = new GeminiSource({
  modelName: 'gemini-1.5-flash-latest',
  modelId: 0,
  maxToken: 500,
  apiKey: 'AIzaSyAG_Lq30NrMK6ICjFi4nO7JOrCO-202Oms'
});

async function test() {
  const chatInstance = await gemini.chatInstance();
  let result = await chatInstance.sendMessage('Compare two words: tentative and provisional');
  console.log(result.response.text());

  result = await chatInstance.sendMessage('Add 1 more word: temporary');
  console.log(result.response.text());
}

test();
